# LCM Manager

本项目旨在为 SMTX/Arcfra 集群变更运维动作提供后端支持。

## Description

本项目使用 Protobuf 定义 API，并通过 Buf CLI 生成 API 相关代码及接口文档。本项目使用 Temporal 作为工作流调度引擎，将集群变更相关的运维动作定义为 Temporal 工作流，并以 Activity 为粒度定义工作流的执行步骤。本项目不负责 Temporal 组件的管理，使用时依赖 CloudTower 的 k8s 运行环境及 Temporal 组件。本项目依赖 HostPlugin 的能力，在发起集群变更动作时，会在集群主机上部署相应的 HostPlugin 作为 agent，并通过 agent 执行已有的集群变更命令。

### 组件

#### lcm-manager

| 组件   | 部署方式    | 用途                                       |
| ------ | ----------- | ------------------------------------------ |
| server | Deployment  | lcm-manager 后端 API 入口                  |
| worker | Deployment  | 执行各种集群变更 workflow 的工作负载       |
| agent  | Host Plugin | 部署在集群节点上，代理执行集群变更相关命令 |

#### 其他组件

| 组件       | 部署方式    | 用途                    |
| ---------- | ----------- | ----------------------- |
| Temporal   | Deployment  | workflow 工作流编排引擎 |
| PostgreSQL | Statefulset | CloudTower 后端数据库   |

### 代码结构

```
.
├── build # 构建相关脚本和 Dockerfile
│   ├── app-base-image
│   ├── host-plugin-package
│   ├── jenkins-ci
│   ├── lcm-manager
│   ├── lcm-manager-agent
│   └── scripts
├── cmd # 命令行入口，main 文件指向这里
├── deploy # 项目部署相关脚本
│   └── lcm-manager
├── gen # protobuf 生成的 api 网关代码及接口文档
│   └── proto
├── hack # 开发调试用到的一些脚本
├── pkg
│   ├── agent # lcm-manager 定义的 agent 用于执行命令
│   ├── client # 项目封装的一些 client，如：对 zbs grpc 的调用
│   ├── config # 配置文件目录
│   ├── hostplugin # 对 hostplugin 操作相关代码
│   ├── i18n # 检查项及 tower task 相关的中英文文案
│   ├── server # api 网关后 service 入口
│   ├── utils # 通用代码封装
│   └── workflow # 集群变更 Temporal 工作流定义
├── proto # protobuf 定义
│   ├── agent
│   └── server
├── third_party # 定义 tower api 等第三方 api 代码
└── version # 用于根据 git commit 记录及 tag 生成版本号信息
```

## Getting Started

### Dependencies

* 开发环境：Linux with golang 1.22
* 部署工具：kubectl & helm client
* 运行环境：CloudTower 虚拟机
* 镜像仓库：registry.smtx.io

### Installing

* 克隆代码 `<NAME_EMAIL>:LCM/lcm-manager.git`。
* 将 cloud tower 中的 k8s config 文件拷贝到开发环境 `~/.kube/config` 中。
* 安装开发时依赖的必要的工具 `make dev-tools`。
* 执行 `make deploy` 命令来构建项目 image、helm chart，并部署到目标 cloud tower 中。

### Executing program

#### 代码修改

* 如果修改了 `proto/` 中的代码，修改完成之后应该执行命令 `make gen-docs` 来更新 api 相关代码及文档。
* 如果修改了 `third_party/tower/genqlient.graphql` 中的代码，则应该执行 `make generate-tower-sdk` 来生成 cloud tower api 相关的代码。
* 修改完代码，提交代码之前应该通过 `make format` `make lint` `make typos` `make test` 来执行代码格式检查及运行单元测试。

#### 本地调试

代码修改完成之后可以在本地进行功能自测，调试步骤如下：

1. 通过 [Smartx 自动化平台](http://auto.smartx.com/) 部署 cloudtower 虚拟机，同时关联准备好的 SMTXOS 集群。
2. 通过 ssh 登录 cloudtower 虚拟机，拷贝 ~/.kube/config 到开发环境的 ~/.kube/config 文件中。
3. 在项目的根目录下执行 `bash hack/init_tower_dev_vm.sh` 用以暴露 cloudtower-prisma 等 svc 的 nodePort 端口，方便通过开发环境直接连通这些服务。
4. 通过命令 `make build-host-plugin-pkgs-image` 来打包 lcm-manager agent 的 host plugin package。
5. 通过命令 `make download-host-plugin-pkgs` 将 host plugin package 下载到本地。
6. 通过命令 `make run-server DEV_TOWER_IP=${TOWER_IP}` 来启动 lcm-manager server。
7. 通过另一个 session 打开开发环境，cd 到项目的根目录，通过命令 `make run-worker DEV_TOWER_IP=${TOWER_IP}` 来启动 lcm-manager worker。
8. 参考 `hack/api.http` 中定义的 api 请求来发送请求到开发环境 lcm-manager server 监听到地址。如角色转换前置检查的 POST URL 为 `http://{{DEV_SEVER_IP}}:8080/api/v1/clusters/{{cluster_uuid}}/hosts/{{host_uuid}}/role_convert_check`
9. 通过浏览器打开 `https://{TOWER_IP}/debug/temporal/namespaces/lcm-manager/workflows` 来协助定位 temporal workflow 运行情况。

**注意**

* 如果不想在本地运行 server 及 worker 服务，也可以跳过以上步骤 4 - 7，执行 `make deploy` 命令来构建项目 image、helm chart，并部署到目标 cloud tower 中。
* 此时可以直接访问 tower 的 api 来进行调试，此时 URL 会增加前缀 `lcm-manager`，如角色转换前置检查的 POST URL 为 `http://{{TOWER_IP}}:80/lcm-manager/api/v1/clusters/{{cluster_uuid}}/hosts/{{host_uuid}}/role_convert_check`


#### 构建

* 执行 `make docker-build` 来自动构建所有的 image
* 执行 `make push-charts` 来推送 helm chart 到 oci registry

## Help

通过 `make help` 命令来获取帮助信息

### 部分命令使用说明

* `make sync-golang-image`: 同步 docker.io/library/golang image 到 registry.smtx.io/lcm
* `make build-app-base-image`: 构建 lcm-manager base image
* `make build-agent-image`: 构建 LCM Manager Agent image
* `make build-host-plugin-pkgs-image`: 构建 Agent Host Plugin Packages image, 依赖于 LCM Manager Agent image
* `make build-server-image`: 构建 LCM Manager Server image, 依赖于 Host Plugin Packages image
* `make download-host-plugin-pkgs`: 下载 Agent Host Plugin Packages image 中的 host plugin packages
* `make upload-host-plugin-pkgs`: 上传 Agent Host Plugin Packages image 中的 host plugin packages 到 cloudtower

## Version History

* v1.0.0
  * 支持集群变更：角色转换、移除主机。
