apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "lcm-manager.fullname" . }}
  labels:
    {{- include "lcm-manager.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "lcm-manager.selectorLabels" . | nindent 6 }}
  strategy:
    type: Recreate
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "lcm-manager.labels" . | nindent 8 }}
	{{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "lcm-manager.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: server
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "common.images.image" (dict "context" $ "repository" .Values.platformConfig.imageRepository "imageRoot" .Values.image) | quote }}
          imagePullPolicy: {{ default "IfNotPresent" .Values.platformConfig.imagePullPolicy | quote }}
          command:
            - /app/lcm-manager
            - server
          env: {{- include "common.container.env" . | nindent 12 }}
          ports:
            - name: server-port
              containerPort: {{ .Values.server.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/v1/healthz
              port: {{ .Values.server.service.port }}
            initialDelaySeconds: 5
          readinessProbe:
            httpGet:
              path: /api/v1/healthz
              port: {{ .Values.server.service.port }}
            initialDelaySeconds: 5
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        - name: worker
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "common.images.image" (dict "context" $ "repository" .Values.platformConfig.imageRepository "imageRoot" .Values.image) | quote }}
          imagePullPolicy: {{ default "IfNotPresent" .Values.platformConfig.imagePullPolicy | quote }}
          command:
            - /app/lcm-manager
            - worker
          env: {{- include "common.container.env" . | nindent 12 }}
          ports:
            - name: worker-port
              containerPort: {{ .Values.worker.service.port }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
