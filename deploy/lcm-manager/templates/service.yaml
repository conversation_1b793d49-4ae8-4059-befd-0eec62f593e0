apiVersion: v1
kind: Service
metadata:
  name: {{ include "lcm-manager.fullname" . }}
  labels:
    {{- include "lcm-manager.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: {{ .Values.server.service.name }}
      port: {{ .Values.server.service.port }}
      targetPort: {{ .Values.server.service.targetPort }}
      protocol: TCP
    - name: {{ .Values.worker.service.name }}
      port: {{ .Values.worker.service.port }}
      targetPort: {{ .Values.worker.service.targetPort }}
      protocol: TCP
  selector:
    {{- include "lcm-manager.selectorLabels" . | nindent 4 }}
