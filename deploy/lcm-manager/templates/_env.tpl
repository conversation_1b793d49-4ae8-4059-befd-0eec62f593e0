{{/*
Return the container env list
*/}}

{{- define "common.container.env" -}}
- name: HOST_IP
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP
- name: POD_NAMESPACE
  valueFrom:
    fieldRef:
      fieldPath: metadata.namespace
- name: PRODUCT_VENDOR
  value: {{ default "smtx" .Values.platformConfig.productVendor | quote }}

{{- if .Values.envs }}
  {{- range .Values.envs }}
- name: {{ .name }}
  value: {{ .value | quote }}
  {{- end }}
{{- end }}

{{- end }}