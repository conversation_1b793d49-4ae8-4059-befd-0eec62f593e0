replicaCount: 1

platformConfig:
  imageRegistry: registry.smtx.io
  imageRepository: lcm
  imagePullPolicy: IfNotPresent
  productVendor: smtx

image:
  name: lcm-manager

nameOverride: "lcm-manager"
fullnameOverride: "lcm-manager"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

envs:
- name: HOST_PLUGIN_UPLOAD_URL
  value: http://host-plugin-rest-svc.cloudtower-system.svc/api/v1/packages/upload
- name: TEMPORAL_HOST
  value: temporal-frontend
- name: TEMPORAL_PORT
  value: "7233"
- name: CLOUDTOWER_HOST
  value: cloudtower-cloudtower.cloudtower-system.svc
- name: CLOUDTOWER_PORT
  value: "80"
- name: CLOUDTOWER_PRISMA_ENDPOINT
  value: http://cloudtower-cloudtower-prisma.cloudtower-system.svc:8811
- name: DB_BACKEND_DSN
  value: host=cloudtower-cloudtower-postgres.cloudtower-system.svc user=prisma password=prisma port=5432 sslmode=disable
- name: SERVER_LISTEN_ADDR
  value: "0.0.0.0"
- name: SERVER_LISTEN_PORT
  value: "80"
- name: WORKER_LISTEN_ADDR
  value: "0.0.0.0"
- name: WORKER_LISTEN_PORT
  value: "8080"

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

server:
  service:
    port: 80
    targetPort: 80
    name: lcm-manager-server
worker:
  service:
    port: 8080
    targetPort: 8080
    name: lcm-manager-worker

service:
  type: ClusterIP

ingress:
  enabled: false
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 16m
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/configuration-snippet: |
      rewrite ^/docs$ /lcm-manager/docs/ permanent;
      rewrite ^/docs/(.*)$ /lcm-manager/docs/$1 redirect;
      rewrite ^/swagger/(.*)$ /lcm-manager/swagger/$1 redirect;
  className: "nginx"
  hosts:
    - host:
      paths:
        - path: /lcm-manager(/|$)(.*)
          pathType: Prefix

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1
    memory: 512Mi
  requests:
   cpu: 200m
   memory: 128Mi

requestStorage: 200Mi
storageClassName: local-path

# Additional volumes on the output Deployment definition.
volumes:
- name: cloudtower-server-secret
  secret:
    secretName: cloudtower-server
    items:
    - key: cloudtower.yaml
      path: cloudtower-secret.yaml
    optional: false
- name: lcm-manager-logs
  persistentVolumeClaim:
    claimName: lcm-manager-logs
- name: availability-map
  configMap:
    name: lcm-manager-config
    items:
    - key: availability-map
      path: availability-map.yaml
- name: activity-config
  configMap:
    name: lcm-manager-config
    items:
    - key: activity
      path: activity.yaml

# Additional volumeMounts on the output Deployment definition.
volumeMounts:
- name: cloudtower-server-secret
  mountPath: "/etc/lcm-manager/cloudtower-secret.yaml"
  subPath: cloudtower-secret.yaml
  readOnly: true
- name: lcm-manager-logs
  mountPath: "/var/log/lcm_manager"
- name: availability-map
  mountPath: "/etc/lcm-manager/availability-map.yaml"
  subPath: availability-map.yaml
  readOnly: true
- name: activity-config
  mountPath: "/etc/lcm-manager/activity.yaml"
  subPath: activity.yaml
  readOnly: true

# default values for helm chart template
nodeSelector: {}
tolerations: []
affinity: {}
autoscaling:
  enabled: false

pprof:
  enabled: false
swagger:
  enabled: false
