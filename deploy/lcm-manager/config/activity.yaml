ActivityDefault:
  start_to_close_timeout: 300 # 5 minutes
  retry_initial_interval: 1
  retry_backoff_coefficient: 2
  retry_maximum_interval: 30
  retry_maximum_attempts: 5

PrecheckActivityDefault:
  start_to_close_timeout: 120
  retry_initial_interval: 1
  retry_backoff_coefficient: 2
  retry_maximum_interval: 20
  retry_maximum_attempts: 5

StateUpdateActivities:
  start_to_close_timeout: 60
  retry_initial_interval: 1
  retry_backoff_coefficient: 2
  retry_maximum_interval: 4
  retry_maximum_attempts: 30

WaitingForRoleConvertDone:
  start_to_close_timeout: 1800
  retry_initial_interval: 5
  retry_backoff_coefficient: 2
  retry_maximum_interval: 30
  retry_maximum_attempts: 5

WaitingForRemoveHostDone:
  start_to_close_timeout: 1800
  retry_initial_interval: 5
  retry_backoff_coefficient: 2
  retry_maximum_interval: 30
  retry_maximum_attempts: 5

WaitingForRemoveChunkDone:
  start_to_close_timeout: 36010 # activity timeout should be larger than waiting time, so add 10s here
  retry_initial_interval: 5
  retry_backoff_coefficient: 2
  retry_maximum_interval: 30
  retry_maximum_attempts: 5

WaitingForTimeSync:
  start_to_close_timeout: 900
  retry_initial_interval: 1
  retry_backoff_coefficient: 2
  retry_maximum_interval: 20
  retry_maximum_attempts: 5

WaitingForRDMAToggle:
  start_to_close_timeout: 36010
  retry_maximum_attempts: 0
