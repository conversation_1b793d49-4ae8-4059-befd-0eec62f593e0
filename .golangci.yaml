run:
  skip-dirs:
    - vendor
    - temp
  skip-files:
    - _test.go
    - generated.go
    - pb.go
    - pb.gw.go
    - third_party/tower/client.go
    - third_party/host_plugin_operator/client.go
  skip-dirs-use-default: false
  timeout: 10m
  allow-parallel-runners: true

linters-settings:
  dupl:
    threshold: 220
  errcheck:
    check-type-assertions: true
    check-blank: false
  funlen:
    lines: 80
    statements: 50
  gci:
    sections:
      - standard
      - default
      - localmodule
  gocritic:
    settings:
      ifElseChain:
        minThreshold: 3
  gocyclo:
    min-complexity: 30
  godox:
    keywords:
      - BUG
      - FIXME
      - OPTIMIZE
      - HACK
      - TODO
  gofumpt:
    lang-version: "1.22"
  lll:
    line-length: 240
  revive:
    rules:
      - name: exported
        disabled: true
  stylecheck:
    go: "1.22"


linters:
  disable-all: true
  enable:
  - asciicheck
  - bodyclose
  # - depguard
  - dogsled
  - dupl
  - dupword
  - errcheck
  - errchkjson
  - errorlint
  - exhaustive
  - exportloopref
  - funlen
  - gci
  - gochecknoinits
  - goconst
  - gocritic
  - gocyclo
  # - godot
  - godox
  - gofmt
  - gofumpt
  - goimports
  # - gomnd
  - goprintffuncname
  - gosec
  - gosimple
  - govet
  - importas
  - ineffassign
  - misspell
  - nakedret
  - nilerr
  - noctx
  - nolintlint
  - nonamedreturns
  - nosprintfhostport
  - perfsprint
  - prealloc
  - predeclared
  - revive
  - rowserrcheck
  - staticcheck
  - stylecheck
  - thelper
  - tparallel
  - typecheck
  - unconvert
  - unparam
  - unused
  - whitespace
  # - wsl
  fast: true
