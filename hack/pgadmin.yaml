---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgadmin
  labels:
    name: pgadmin
spec:
  replicas: 1
  selector:
    matchLabels:
      name: pgadmin
  template:
    metadata:
      labels:
        name: pgadmin
    spec:
      containers:
      - name: pgadmin
        image: registry.smtx.io/docker.io/dpage/pgadmin4:7
        volumeMounts:
        - name: pgadmin-data
          mountPath: /var/lib/pgadmin
        env:
        - name: PGADMIN_DEFAULT_EMAIL
          value: <EMAIL>
        - name: PGADMIN_DEFAULT_PASSWORD
          value: HC!r0cks
        - name: PGADMIN_LISTEN_PORT
          value: "8080"
      volumes:
      - name: pgadmin-data
        persistentVolumeClaim:
          claimName: pgadmin-data
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pgadmin-data
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: local-path
  resources:
    requests:
      storage: 2Gi
---
kind: Service
apiVersion: v1
metadata:
  name: pgadmin
spec:
  selector:
    name: pgadmin
  ports:
  - name: pgadmin
    protocol: TCP
    port: 8080
    targetPort: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/configuration-snippet: |
      proxy_set_header Host $host;
      rewrite ^/pgadmin$ /pgadmin/ redirect;
      proxy_set_header X-Script-Name /pgadmin;
  name: pgadmin
spec:
  rules:
  - http:
      paths:
      - backend:
          service:
            name: pgadmin
            port:
              number: 8080
        path: /pgadmin
        pathType: Prefix
