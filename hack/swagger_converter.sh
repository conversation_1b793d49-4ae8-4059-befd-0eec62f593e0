#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

SWAGGER_JSON=$1
OPENAPI_JSON=$2
: "${DEV_TOWER_IP:=**************}"
: "${SWAGGER_IO_API_URL:=https://converter.swagger.io/api/convert}"

DEV_SWAGGER_API_URL="http://${DEV_TOWER_IP}/api/convert"

dev_swagger_converter(){
    curl -sSf \
    -d @"${SWAGGER_JSON}" \
    -H "accept: application/json" \
    -H "Content-Type: application/json" \
    -X POST "${DEV_SWAGGER_API_URL}" | jq '.' > "${OPENAPI_JSON}"
}

swagger_io_converter(){
    curl -sSf \
    -d @"${SWAGGER_JSON}" \
    -H "accept: application/json" \
    -H "Content-Type: application/json" \
    -X POST "${SWAGGER_IO_API_URL}" | jq '.' > "${OPENAPI_JSON}"
}

dev_swagger_converter || swagger_io_converter
