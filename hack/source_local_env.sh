#!/bin/bash


: "${DEV_TOWER_IP:=**************}"
CURRENT_DIR="$( cd "$( dirname "$0" )" && pwd )"

export TEMPORAL_HOST=${DEV_TOWER_IP}
export TEMPORAL_PORT=30333
export TEMPORAL_ADDRESS=${TEMPORAL_HOST}:${TEMPORAL_PORT}
export TEMPORAL_NAMESPACE=default
export CLOUDTOWER_HOST=${DEV_TOWER_IP}
export CLOUDTOWER_PORT=80
export CLOUDTOWER_PRISMA_ENDPOINT=http://${DEV_TOWER_IP}:30335
export HOST_PLUGIN_UPLOAD_URL=http://${DEV_TOWER_IP}:30334/api/v1/packages/upload
export HPO_PKGS_DIR=/home/<USER>/gitrepo/lcm-manager/output
export AGENT_SERVER_ADDR="0.0.0.0:8081"
export DB_BACKEND_DSN="host=${DEV_TOWER_IP} user=prisma password=prisma port=5432 sslmode=disable"
export SERVER_LISTEN_ADDR="0.0.0.0"
export SERVER_LISTEN_PORT=8080
export WORKER_LISTEN_PORT=8081
export LCM_MANAGER_SERVER_PORT="tcp://localhost:${SERVER_LISTEN_PORT}"

# [ ! -L /etc/lcm-manager ] && ln -s ${CURRENT_DIR}/deploy/lcm-manager/config /etc/lcm-manager
