#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

IMAGE_AGENT=registry.smtx.io/lcm/upgrade-agent
IMAGE_SERVER=registry.smtx.io/lcm/upgrade-center
IMAGE_PKGS=registry.smtx.io/lcm/host-plugin-package-bundle
CHART_SERVER=registry.smtx.io/lcm/charts/upgrade-center

delete_image_tag() {
    image=$1
    for tag in $(regctl tag ls "$image" | xargs); do
        if grep -qw "$tag" <<<"$GIT_TAGS"; then
            continue
        fi
        regctl image delete --force-tag-dereference "$image:$tag"
    done
}

cleanup_image() {
    image=$1
    regctl tag ls "$image" | xargs -tr -I {} regctl image delete --force-tag-dereference "$image:{}"
}

git fetch --all --tags --force
GIT_TAGS=$(git tag -l | xargs)

delete_image_tag $CHART_SERVER
delete_image_tag $IMAGE_SERVER
cleanup_image $IMAGE_AGENT
cleanup_image $IMAGE_PKGS
