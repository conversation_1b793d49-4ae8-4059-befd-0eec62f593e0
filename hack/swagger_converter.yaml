apiVersion: apps/v1
kind: Deployment
metadata:
  name: swagger-converter
  labels:
    name: swagger-converter
spec:
  replicas: 1
  selector:
    matchLabels:
      name: swagger-converter
  template:
    metadata:
      labels:
        name: swagger-converter
    spec:
      containers:
      - name: swagger-converter
        image: registry.smtx.io/docker.io/swaggerapi/swagger-converter:v1.0.4
---
kind: Service
apiVersion: v1
metadata:
  name: swagger-converter
spec:
  type: NodePort
  selector:
    name: swagger-converter
  ports:
  - name: swagger-converter
    protocol: TCP
    port: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
  name: swagger-converter
spec:
  rules:
  - http:
      paths:
      - backend:
          service:
            name: swagger-converter
            port:
              number: 8080
        path: /api/convert
        pathType: Prefix
