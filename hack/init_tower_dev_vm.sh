#!/bin/bash

# set kubectl default namespace to cloudtower-system
kubectl config set-context --namespace=cloudtower-system --current
# patch temporal server svc nodeport
kubectl patch svc temporal-frontend --type='json' -p '[{"op":"replace","path":"/spec/type","value":"NodePort"},{"op":"replace","path":"/spec/ports/0/nodePort","value":30333}]'
# patch host plugin operator server svc nodeport
kubectl patch svc host-plugin-rest-svc --type='json' -p '[{"op":"replace","path":"/spec/type","value":"NodePort"},{"op":"replace","path":"/spec/ports/0/nodePort","value":30334}]'
# patch cloudtower prisma svc nodeport
kubectl patch svc cloudtower-cloudtower-prisma --type='json' -p '[{"op":"replace","path":"/spec/type","value":"NodePort"},{"op":"replace","path":"/spec/ports/0/nodePort","value":30335}]'
# patch lcm-manager server svc nodeport
kubectl patch svc lcm-manager --type='json' -p '[{"op":"replace","path":"/spec/type","value":"NodePort"},{"op":"replace","path":"/spec/ports/0/nodePort","value":30337}]'
# patch lcm-manager worker svc nodeport
kubectl patch svc lcm-manager --type='json' -p '[{"op":"replace","path":"/spec/type","value":"NodePort"},{"op":"replace","path":"/spec/ports/1/nodePort","value":30338}]'
