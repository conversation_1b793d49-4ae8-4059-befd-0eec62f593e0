.git
.github
.vscode
.idea

# **/*.yaml
**/*.md

bin/
temp/
tmp/
output/
input/

# ignores changes to test-only code to avoid extra rebuilds
test/e2e/**
test/framework/**
test/infrastructure/docker/e2e/**

.dockerignore
# We want to ignore any frequently modified files to avoid cache-busting the COPY ./ ./
# Binaries for programs and plugins
**/*.exe
**/*.dll
**/*.so
**/*.dylib
**/bin/**
**/out/**

# Test binary, build with `go test -c`
**/*.test

# Output of the go coverage tool, specifically when used with LiteIDE
**/*.out

# Common editor / temporary files
**/*~
**/*.tmp
**/.DS_Store
**/*.swp

resources/metadata_test.json
