version: v1
managed:
  enabled: true
  go_package_prefix:
    default: github.smartx.com/LCM/lcm-manager/gen/
    except:
      - buf.build/googleapis/googleapis
plugins:
  - plugin: go
    out: gen
    opt:
      - paths=source_relative
  - plugin: go-grpc
    out: gen
    opt:
      - paths=source_relative
  - plugin: grpc-gateway
    out: gen
    opt:
      - paths=source_relative
  - plugin: openapiv2
    strategy: all
    out: gen
    opt:
      - generate_unbound_methods=true
      - output_format=json
      - allow_merge=true