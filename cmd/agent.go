package cmd

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/spf13/cobra"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	agentv1 "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/agent/service"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
)

var agentCmd = &cobra.Command{
	Use:   "agent",
	Short: "LCM Manager agent",
	Run: func(_ *cobra.Command, _ []string) {
		slog.Info("LCM Manager agent start...")

		config.InitAgentEnv()
		runAgent()
	},
}

func init() { //nolint: gochecknoinits
	rootCmd.AddCommand(agentCmd)
}

func runAgent() {
	agentListenURL := fmt.Sprintf("%s:%s", config.AgentListenAddr, config.AgentListenPort)

	netListener, err := net.Listen("tcp", ":"+config.AgentListenPort)
	if err != nil {
		slog.Error("Failed to listen:", "error", err)
		os.Exit(1)
	}

	grpcServer := grpc.NewServer()
	agentv1.RegisterTaskManagerServer(grpcServer, service.NewServer())

	gwMux := runtime.NewServeMux()
	grpcDialOption := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}

	err = agentv1.RegisterTaskManagerHandlerFromEndpoint(context.Background(), gwMux, agentListenURL, grpcDialOption)
	if err != nil {
		slog.Error("Failed to register gwmux:", "error", err)
		os.Exit(1)
	}

	httpMux := http.NewServeMux()
	httpMux.Handle("/", gwMux)

	gwServer := &http.Server{
		Addr:              agentListenURL,
		Handler:           grpcHandlerFunc(grpcServer, httpMux),
		ReadHeaderTimeout: time.Duration(time.Duration.Seconds(60)),
	}

	slog.Info("Starting gateway server...")
	err = gwServer.Serve(netListener)
	if err != nil {
		slog.Error("Failed to start gateway server:", "error", err)
		os.Exit(1)
	}
}

func grpcHandlerFunc(grpcServer *grpc.Server, otherHandler http.Handler) http.Handler {
	return h2c.NewHandler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.ProtoMajor == 2 && strings.Contains(r.Header.Get("Content-Type"), "application/grpc") {
			grpcServer.ServeHTTP(w, r)
		} else {
			otherHandler.ServeHTTP(w, r)
		}
	}), &http2.Server{})
}
