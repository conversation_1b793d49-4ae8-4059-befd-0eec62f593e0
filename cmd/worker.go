package cmd

import (
	"context"
	"errors"
	"fmt"
	"log"
	"log/slog"
	"time"

	"github.com/spf13/cobra"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/api/workflowservice/v1"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/worker"
	"gorm.io/gorm"

	towerclient "github.smartx.com/LCM/lcm-manager/pkg/client/tower"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/i18n"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	rdmatoggle "github.smartx.com/LCM/lcm-manager/pkg/workflow/rdma_toggle"
	removehost "github.smartx.com/LCM/lcm-manager/pkg/workflow/remove_host"
	roleconvert "github.smartx.com/LCM/lcm-manager/pkg/workflow/role_convert"
	timesync "github.smartx.com/LCM/lcm-manager/pkg/workflow/time_sync"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
	"github.smartx.com/LCM/lcm-manager/version"
)

// workerCmd represents the worker command
var workerCmd = &cobra.Command{
	Use:   "worker",
	Short: "LCM Manager worker",
	Run: func(_ *cobra.Command, _ []string) {
		slog.Info("LCM Manager worker start...")

		config.InitENV()

		if err := createTemporalNamespace(context.Background()); err != nil {
			log.Fatalf("Failed to create temporal namespace: %v", err)
		}

		runWorker()
	},
}

func init() { //nolint: gochecknoinits
	rootCmd.AddCommand(workerCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// workerCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// workerCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}

func createTemporalNamespace(ctx context.Context) error {
	nc, err := client.NewNamespaceClient(client.Options{
		HostPort: fmt.Sprintf("%s:%s", config.TemporalHost, config.TemporalPort),
	})
	if err != nil {
		return fmt.Errorf("failed to create temporal client: %w", err)
	}
	defer nc.Close()

	retentionTime := config.WorkflowRetentionHour * time.Hour

	req := workflowservice.RegisterNamespaceRequest{
		Namespace:                        config.TemporalNamespace,
		Description:                      "Temporal namespace for LCM Manager",
		WorkflowExecutionRetentionPeriod: &retentionTime,
	}

	if err := nc.Register(ctx, &req); err != nil {
		var namespaceErr *serviceerror.NamespaceAlreadyExists
		if errors.As(err, &namespaceErr) {
			slog.Info(fmt.Sprintf("Temporal namespace %s already exists", config.TemporalNamespace))
			return nil
		}

		return fmt.Errorf("register namespace failed: %w", err)
	}

	return nil
}

func runWorker() {
	c, err := client.Dial(client.Options{
		Namespace: config.TemporalNamespace,
		HostPort:  fmt.Sprintf("%s:%s", config.TemporalHost, config.TemporalPort),
		Logger:    slog.Default(),
	})
	if err != nil {
		log.Fatalf("Unable to connect to temporal server: %v", err)
	}

	w := worker.New(c, config.TemporalWorkerName, worker.Options{
		BuildID:                      version.Get().String(),
		MaxHeartbeatThrottleInterval: 10 * time.Second,
	})

	config.InitENV()

	var db *gorm.DB

	if db, err = connectDB(config.DBbackendDSN, config.DBName); err != nil {
		c.Close()
		log.Fatalf("Failed to init postgres connection, err: %v", err)
	}

	var towerClient tower.Client
	towerClient, err = towerclient.NewTowerClient()
	if err != nil {
		c.Close()
		log.Fatalf("failed to init tower client, err: %v", err)
	}

	hpClient := hpoperator.NewClient(towerClient)

	var jobRepository *postgres.JobRepository
	jobRepository, err = postgres.NewJobRepository(context.Background(), &postgres.RepoParams{
		DB: db,
	})
	if err != nil {
		c.Close()
		log.Fatalf("Failed to init job repository, err: %v", err)
	}

	var checkResultRepository *postgres.CheckResultRepository
	checkResultRepository, err = postgres.NewCheckResultRepository(context.Background(), &postgres.RepoParams{
		DB: db,
	})
	if err != nil {
		c.Close()
		log.Fatalf("Failed to init check result repository, err: %v", err)
	}

	messageI18n := i18n.InitializeI18n()

	roleConvertCheck := roleconvert.NewRoleConvertCheck(jobRepository, checkResultRepository, towerClient, messageI18n)
	roleConvert := roleconvert.NewRoleConvert(jobRepository, checkResultRepository, towerClient, hpClient, messageI18n)

	w.RegisterActivity(roleConvertCheck)
	w.RegisterActivity(roleConvert)

	w.RegisterWorkflow(roleconvert.RoleConvertCheckWorkflow)
	w.RegisterWorkflow(roleconvert.RoleConvertWorkflow)

	removeHostCheck := removehost.NewRemoveHostCheck(jobRepository, checkResultRepository, towerClient, messageI18n)
	removeHost := removehost.NewRemoveHost(jobRepository, checkResultRepository, towerClient, hpClient, messageI18n)

	w.RegisterActivity(removeHostCheck)
	w.RegisterActivity(removeHost)

	w.RegisterWorkflow(removehost.RemoveHostCheckWorkflow)
	w.RegisterWorkflow(removehost.RemoveHostWorkflow)

	timeSyncActivities := timesync.NewTimeSyncActivities(jobRepository, checkResultRepository, towerClient, hpClient, messageI18n)
	w.RegisterActivity(timeSyncActivities)
	w.RegisterWorkflow(timesync.TimeSyncWorkflow)

	rdmaToggle := rdmatoggle.NewRdmaToggleActivities(jobRepository, checkResultRepository, towerClient, hpClient, messageI18n)
	w.RegisterActivity(rdmaToggle)
	w.RegisterWorkflow(rdmatoggle.RdmaToggleWorkflow)

	if err = w.Run(worker.InterruptCh()); err != nil {
		c.Close()
		log.Fatalf("Unable to connect to temporal server: %v", err)
	}
}
