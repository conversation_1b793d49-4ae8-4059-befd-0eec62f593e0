package cmd

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/hashicorp/logutils"
	"github.com/pkg/errors"
	"github.com/soheilhy/cmux"
	"github.com/spf13/cobra"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
	"google.golang.org/grpc/credentials/insecure"
	pg "gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	towerclient "github.smartx.com/LCM/lcm-manager/pkg/client/tower"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/server"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	"github.smartx.com/LCM/lcm-manager/pkg/workflow"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
)

// serverCmd represents the server command
var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "LCM Manager server",
	Run: func(_ *cobra.Command, _ []string) {
		slog.Info("LCM Manager server start...")
		runServer()
	},
}

func init() { //nolint: gochecknoinits
	rootCmd.AddCommand(serverCmd)
}

func runServer() {
	config.InitENV()

	f := &logutils.LevelFilter{
		MinLevel: logutils.LogLevel(strings.ToUpper("INFO")[:1]),
		Writer:   os.Stderr,
	}
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.SetOutput(f)

	var (
		err error
		db  *gorm.DB
		// packagePath = ""
	)

	if db, err = connectDB(config.DBbackendDSN, config.DBName); err != nil {
		log.Fatalf("Failed to init postgres connection, err: %v", err)
	}

	var towerClient tower.Client
	towerClient, err = towerclient.NewTowerClient()
	if err != nil {
		log.Fatalf("Failed to init tower client, err: %v", err)
	}

	var jobRepository *postgres.JobRepository
	jobRepository, err = postgres.NewJobRepository(context.Background(), &postgres.RepoParams{
		DB: db,
	})
	if err != nil {
		log.Fatalf("Failed to init job repository, err: %v", err)
	}

	var checkResultRepository *postgres.CheckResultRepository
	checkResultRepository, err = postgres.NewCheckResultRepository(context.Background(), &postgres.RepoParams{
		DB: db,
	})
	if err != nil {
		log.Fatalf("Failed to init check result repository, err: %v", err)
	}

	var wfClient *workflow.Client
	wfClient, err = workflow.NewClient(fmt.Sprintf("%s:%s", config.TemporalHost, config.TemporalPort), config.TemporalNamespace)
	if err != nil {
		log.Fatalf("Failed to init workflow client, err: %v", err)
	}

	LCMManagerServer := server.NewServer(jobRepository, checkResultRepository, wfClient, towerClient)
	if err != nil {
		log.Fatalf("Failed to init LCMManager server, err: %v", err)
	}

	l, err := net.Listen("tcp", fmt.Sprintf("%s:%s", config.ServerListenAddr, config.ServerListenPort))
	if err != nil {
		log.Fatalf("Failed to listen on port %s, err: %v", config.ServerListenPort, err)
	}
	m := cmux.New(l)
	grpcListener := m.MatchWithWriters(cmux.HTTP2MatchHeaderFieldSendSettings("content-type", "application/grpc"))
	httpListener := m.Match(cmux.HTTP1Fast())

	g := new(errgroup.Group)
	g.Go(server.StartLogCleanupScheduler)
	g.Go(func() error { return grpcServe(grpcListener, LCMManagerServer) })
	g.Go(func() error {
		return httpServe(httpListener, "localhost:"+config.ServerListenPort, LCMManagerServer)
	})
	g.Go(func() error { return m.Serve() })

	log.Fatal(g.Wait())
}

func grpcServe(l net.Listener, svr server_pb.ManagerServer) error {
	opts := []grpc.ServerOption{
		// recover panic to error
		grpc.ChainUnaryInterceptor(
			grpc_recovery.UnaryServerInterceptor(),
		),
		grpc.ChainStreamInterceptor(
			grpc_recovery.StreamServerInterceptor(),
		),
	}
	s := grpc.NewServer(opts...)
	server_pb.RegisterManagerServer(s, svr)
	return s.Serve(l)
}

func httpServe(l net.Listener, endpoint string, _ *server.Server) error {
	mux := runtime.NewServeMux(
		runtime.WithIncomingHeaderMatcher(func(s string) (string, bool) {
			switch strings.ToLower(s) {
			case config.UserIDHeader, config.UserIPHeader:
				return s, true
			}
			return runtime.DefaultHeaderMatcher(s)
		}),
		runtime.WithOutgoingTrailerMatcher(func(s string) (string, bool) {
			switch strings.ToLower(s) {
			case config.UserIDHeader, config.UserIPHeader:
				return s, true
			}
			return runtime.DefaultHeaderMatcher(s)
		}),
	)
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithConnectParams(grpc.ConnectParams{
			Backoff: backoff.Config{
				BaseDelay:  time.Second,
				Multiplier: 1.6,
				MaxDelay:   10 * time.Second,
			},
			MinConnectTimeout: 5 * time.Second,
		}),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(10 * 1024 * 1024)),
	}

	if err := server_pb.RegisterManagerHandlerFromEndpoint(context.Background(), mux, endpoint, opts); err != nil {
		return err
	}

	prefixMux := http.NewServeMux()
	// prefixMux.Handle("/api/", http.StripPrefix("/api", mux))
	prefixMux.Handle("/", mux)

	s := &http.Server{
		Handler:           prefixMux,
		ReadHeaderTimeout: 5 * time.Second,
	}
	return s.Serve(l)
}

func connectDB(backendDSN string, dbName string) (*gorm.DB, error) {
	db, err := gorm.Open(pg.Open(backendDSN), &gorm.Config{
		DisableAutomaticPing: true,
		Logger:               logger.Default.LogMode(logger.Warn),
	})
	if err != nil {
		return nil, errors.Wrap(err, "Failed to open tower pg")
	}
	err = createDBIfNotExist(db, dbName)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to check db exist")
	}
	backendDSN = fmt.Sprintf("%s dbname=%s", backendDSN, dbName)
	if db, err = gorm.Open(pg.Open(backendDSN), &gorm.Config{
		DisableAutomaticPing: true,
		Logger:               logger.Default.LogMode(logger.Warn),
	}); err != nil {
		return nil, errors.Wrap(err, "Failed to open "+dbName)
	}
	return db, nil
}

func createDBIfNotExist(db *gorm.DB, dbName string) error {
	stmt := fmt.Sprintf("SELECT * FROM pg_database WHERE datname = '%s';", dbName)
	rs := db.Raw(stmt)
	if rs.Error != nil {
		return rs.Error
	}
	rec := make(map[string]interface{})
	if rs.Find(rec); len(rec) == 0 {
		slog.Info("Can't found db, create database", "db_name", dbName)
		stmt = fmt.Sprintf("CREATE DATABASE %s;", dbName)
		if rs = db.Exec(stmt); rs.Error != nil {
			return rs.Error
		}
		sql, err := db.DB()
		defer func() {
			_ = sql.Close()
		}()
		if err != nil {
			return err
		}
	}
	return nil
}
