package cmd

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/spf13/cobra"
	"gopkg.in/yaml.v2"

	"github.smartx.com/LCM/lcm-manager/version"
)

var output string

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information",
	Run: func(_ *cobra.Command, _ []string) {
		v := version.Get()

		switch output {
		case "yaml":
			str, err := yaml.Marshal(&v)
			if err != nil {
				log.Fatalf("marshal version info to yaml failed: %v", err)
			}

			fmt.Print(string(str))
		case "json":
			str, err := json.MarshalIndent(&v, "", "  ")
			if err != nil {
				log.Fatalf("marshal version info to json failed: %v", err)
			}

			fmt.Println(string(str))
		default:
			fmt.Println(v.GitVersion)
		}
	},
}

func init() { //nolint: gochecknoinits
	rootCmd.AddCommand(versionCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// versionCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// versionCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")

	versionCmd.Flags().StringVarP(&output, "output", "o", "", "Output format; available options are 'yaml', 'json'")
}
