package cmd

import (
	"context"
	"fmt"
	"log/slog"
	"strings"

	"github.com/spf13/cobra"

	towerclient "github.smartx.com/LCM/lcm-manager/pkg/client/tower"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	"github.smartx.com/LCM/lcm-manager/pkg/tool"
)

var (
	ctx        context.Context
	toolclient *tool.Client
)

var toolCmd = &cobra.Command{
	Use:   "tool",
	Short: "lcm-manager tool commands",
	Long: `commands for updating tower task status,
updating host labels for lcm-manager actions, updating
host labels for host roles`,
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		return initClient()
	},
}

func initClient() error {
	config.InitENV()

	ctx = context.Background()

	db, err := connectDB(config.DBbackendDSN, config.DBName)
	if err != nil {
		slog.Error("Failed to init postgres connection", "error", err)
		return err
	}

	jobRepository, err := postgres.NewJobRepository(ctx, &postgres.RepoParams{DB: db})
	if err != nil {
		slog.Error("Failed to init job repository", "error", err)
		return err
	}

	towerClient, err := towerclient.NewTowerClient()
	if err != nil {
		slog.Error("Failed to init tower client", "error", err)
		return err
	}

	toolclient = tool.NewClient(jobRepository, towerClient)
	return nil
}

func validateEnumFlag(flag string, validValues []string) error {
	for _, v := range validValues {
		if flag == v {
			return nil
		}
	}
	return fmt.Errorf("invalid value: %s. Valid options are: %s", flag, strings.Join(validValues, ", "))
}

var listHost = &cobra.Command{
	Use:   "list-host",
	Short: "list cluster hosts info",
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("get all clusters hosts")
		if err := toolclient.ListClusterHosts(ctx); err != nil {
			return fmt.Errorf("fail to get cluster hosts: %w", err)
		}
		return nil
	},
}

var listJob = &cobra.Command{
	Use:   "list-job",
	Short: "list lcm-manager jobs",
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("get all jobs")
		hostUUID := cmd.Flag("host-uuid").Value.String()
		offset, _ := cmd.Flags().GetInt32("offset")

		if err := toolclient.ListHostJobs(ctx, hostUUID, offset); err != nil {
			return fmt.Errorf("failed to list jobs: %w", err)
		}

		return nil
	},
}

var showTowerTask = &cobra.Command{
	Use:   "show-tower-task",
	Short: "show tower task info",
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("show tower task info")
		taskID := cmd.Flag("task-id").Value.String()
		if err := toolclient.ShowTowerTask(ctx, taskID); err != nil {
			return fmt.Errorf("fail to show tower task info: %w", err)
		}

		return nil
	},
}

var updateTowerTask = &cobra.Command{
	Use:   "update-tower-task",
	Short: "update tower task info",
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		status := cmd.Flag("status").Value.String()
		if err := validateEnumFlag(status, []string{"FAILED", "SUCCESSED"}); err != nil {
			return err
		}

		return cmd.Parent().PersistentPreRunE(cmd, args)
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("update tower task info")
		taskID := cmd.Flag("task-id").Value.String()
		status := cmd.Flag("status").Value.String()

		if err := toolclient.UpdateTowerTask(ctx, taskID, status); err != nil {
			return fmt.Errorf("fail to update tower task info: %w", err)
		}

		return nil
	},
}

var showHostLabel = &cobra.Command{
	Use:   "show-host-label",
	Short: "show host labels",
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("show host label info")
		hostUUID := cmd.Flag("host-uuid").Value.String()
		if err := toolclient.ShowHostlabels(ctx, hostUUID); err != nil {
			return fmt.Errorf("fail to show host label: %w", err)
		}

		return nil
	},
}

var updateHostLabelRole = &cobra.Command{
	Use:   "update-host-role",
	Short: "update host role by updating host labels",
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		role := cmd.Flag("role").Value.String()
		unset, _ := cmd.Flags().GetBool("unset")
		if !unset {
			if err := validateEnumFlag(role, []string{"master", "storage"}); err != nil {
				return err
			}
		}

		return cmd.Parent().PersistentPreRunE(cmd, args)
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("update host label role info")
		hostUUID := cmd.Flag("host-uuid").Value.String()
		role := cmd.Flag("role").Value.String()
		unset, _ := cmd.Flags().GetBool("unset")
		if unset {
			role = ""
		}

		if err := toolclient.UpdateHostLabelRole(ctx, hostUUID, role); err != nil {
			return fmt.Errorf("fail to update host label role: %w", err)
		}

		return nil
	},
}

var updateHostActionLabel = &cobra.Command{
	Use:   "update-action-status",
	Short: "update host LCM_MANAGER_ACTION status by updating host labels",
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		status := cmd.Flag("status").Value.String()
		unset, _ := cmd.Flags().GetBool("unset")
		if !unset {
			if err := validateEnumFlag(status, []string{"ROLE_CONVERT_FAILED", "HOST_REMOVE_FAILED"}); err != nil {
				return err
			}
		}

		return cmd.Parent().PersistentPreRunE(cmd, args)
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		slog.Info("update host LCM_MANAGER_ACTION status by host label")
		hostUUID := cmd.Flag("host-uuid").Value.String()
		status := cmd.Flag("status").Value.String()
		unset, _ := cmd.Flags().GetBool("unset")
		if unset {
			status = ""
		}

		if err := toolclient.UpdateActionStatus(ctx, hostUUID, status); err != nil {
			return fmt.Errorf("fail to update host label LCM_MANAGER_ACTION: %w", err)
		}

		return nil
	},
}

func init() { //nolint: gochecknoinits
	rootCmd.AddCommand(toolCmd)

	toolCmd.AddCommand(listHost)

	listJob.Flags().String("host-uuid", "", "host uuid")
	listJob.Flags().Int32("offset", 0, "offset")
	toolCmd.AddCommand(listJob)

	showTowerTask.Flags().String("task-id", "", "tower task id")
	toolCmd.AddCommand(showTowerTask)

	updateTowerTask.Flags().String("task-id", "", "tower task id")
	updateTowerTask.Flags().String("status", "", "update tower task status: valid options: [FAILED, SUCCESSED]")
	toolCmd.AddCommand(updateTowerTask)

	showHostLabel.Flags().String("host-uuid", "", "host uuid")
	toolCmd.AddCommand(showHostLabel)

	updateHostLabelRole.Flags().String("host-uuid", "", "host uuid")
	updateHostLabelRole.Flags().String("role", "", "update tower task status: valid options: [master, storage]")
	updateHostLabelRole.Flags().Bool("unset", false, "use --unset to remove role label")
	toolCmd.AddCommand(updateHostLabelRole)

	updateHostActionLabel.Flags().String("host-uuid", "", "host uuid")
	updateHostActionLabel.Flags().String("status", "", "update tower task status: valid options: [ROLE_CONVERT_FAILED, HOST_REMOVE_FAILED]")
	updateHostActionLabel.Flags().Bool("unset", false, "use --unset to remove LCM_MANAGER_ACTION label")
	toolCmd.AddCommand(updateHostActionLabel)
}
