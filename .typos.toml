[files]
ignore-files = true
ignore-hidden = false
extend-exclude = [
    "go.mod",
    "go.sum",
    ".git/",
    "vendor/",
    "target/",
    "temp/",
    ".golangci.yaml",
    "third_party/tower/generated.go",
    "third_party/tower/schema.graphql",
]

[default]
extend-ignore-re = [
    '"ba"',
    ":ba\\|z",
]
check-filename = true

[default.extend-words]
# tower task status
Successed = "Successed"
# 2nd is for second
nd = "nd"
