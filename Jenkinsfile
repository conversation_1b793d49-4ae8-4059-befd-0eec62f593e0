def JOB_NAME = "${env.JOB_BASE_NAME}"
def BUILD_NUMBER = "${env.BUILD_NUMBER}"
def POD_IMAGE = "registry.smtx.io/lcm/jenkins-ci-pod:lcm-manager-2024-11-21"
def POD_NAME = "jenkins-${JOB_NAME}-${BUILD_NUMBER}"

def GIT_EMAIL = params.repo_url ?: '<EMAIL>'
def REPO_URL = params.repo_url ?: 'http://newgh.smartx.com/LCM/lcm-manager'
def SLACK_CHANNEL = params.slack_channel ?: '#lcm-project-ci'
def TAG = params.tag ?: ''
def PLATFORMS = params.platforms ?: 'linux/amd64'

def PUB_FILESERVER = "192.168.17.20"
def PUB_FILESERVER_NFS_PATH = "/data/distros"

// Kubernetes pod template to run.
podTemplate(
    cloud: "kubernetes",
    namespace: "default",
    name: POD_NAME,
    label: POD_NAME,
    yaml: """
apiVersion: v1
kind: Pod
spec:
  nodeSelector:
    kubernetes.io/arch: amd64
  containers:
  - name: runner
    image: ${POD_IMAGE}
    imagePullPolicy: Always
    tty: true
    env:
    - name: REPO_URL
      value: ${REPO_URL}
    volumeMounts:
    - name: golang-pkg
      mountPath: /go/pkg
    - name: cache
      mountPath: /root/.cache
    - name: data-17-20
      mountPath: ${PUB_FILESERVER_NFS_PATH}
  - name: jnlp
    args: ["\$(JENKINS_SECRET)", "\$(JENKINS_NAME)"]
    image: "jenkins/inbound-agent:4.11.2-4-alpine"
    imagePullPolicy: IfNotPresent
  volumes:
    - name: golang-pkg
      hostPath:
        path: /var/lib/golang
    - name: cache
      hostPath:
        path: /root/.cache
    - name: data-17-20
      nfs:
        server: ${PUB_FILESERVER}
        path: ${PUB_FILESERVER_NFS_PATH}
""",
) {
    node(POD_NAME) {
        try {
            container("runner") {
                stage("Checkout") {
                    retry(10) {
                        checkout([
                            $class: 'GitSCM',
                            branches: scm.branches,
                            doGenerateSubmoduleConfigurations: scm.doGenerateSubmoduleConfigurations,
                            extensions: [[$class: 'CloneOption', noTags: false, shallow: false, depth: 0, reference: '']],
                            userRemoteConfigs: scm.userRemoteConfigs,
                        ])
                        sh """
                        git config --global --add safe.directory \$PWD
                        """
                    }
                }
                stage("Init") {
                    withCredentials([usernamePassword(credentialsId: "lcm-harbor", passwordVariable: "HARBOR_PASSWORD", usernameVariable: "HARBOR_USERNAME")]) {
                        sh """
                        make vendor
                        docker buildx install && docker buildx use kube
                        docker login registry.smtx.io -u '${HARBOR_USERNAME}' -p '${HARBOR_PASSWORD}'
                        """
                    }
                }
                if (params.tag) {
                    stage("Tag") {
                        withCredentials([usernamePassword(credentialsId: "lcm-github-token", passwordVariable: "GITHUB_PASSWORD", usernameVariable: "GITHUB_USERNAME")]) {
                            sh """#!/bin/bash
                                set -o errexit
                                git config --global user.email ${GIT_EMAIL}
                                git config --global url."https://${GITHUB_USERNAME}:${GITHUB_PASSWORD}@newgh.smartx.com".insteadOf "http://newgh.smartx.com"
                                git remote remove origin
                                git remote add origin \${REPO_URL/newgh.smartx.com/${GITHUB_USERNAME}:${GITHUB_PASSWORD}@newgh.smartx.com}
                                if ${params.force}; then FORCE="--force"; fi
                                git tag ${params.tag} -m "Release OVA for ${params.tag}" \${FORCE}
                                git push origin --tags \${FORCE}
                            """
                        }
                    }
                }
                stage("Format") {
                    sh """
                    make format
                    make buf-format
                    if git status -s | grep 'M'; then
                        echo "Format failed, please fix the format errors and try again."
                        exit 1
                    fi
                    """
                }
                stage("Lint") {
                    sh """
                    make lint-cache-clean
                    make lint
                    make typos
                    """
                }
                stage("Unit Test") {
                    sh """
                    make test
                    """
                }
                stage("Build") {
                    if (TAG == '') {
                        sh """
                        make docker-build DEV_ENV="false"
                        make cleanup-images DEV_ENV="false"
                        """
                    } else {
                        sh """
                        make build-and-push-charts PLATFORMS="${PLATFORMS}" TAG="${TAG}" DEV_ENV="false"
                        """
                    }
                }

            }
            stage("Success"){
                MESSAGE = "【Succeed】Jenkins Job ${JOB_NAME}-${BUILD_NUMBER} Link: ${BUILD_URL}"
                slackSendWithRetry("${SLACK_CHANNEL}", "${MESSAGE}", "good")
            }
        } catch (Exception e) {
            MESSAGE = "【Failed】Jenkins Job ${JOB_NAME}-${BUILD_NUMBER} Link: ${BUILD_URL}"
            slackSendWithRetry("${SLACK_CHANNEL}", "${MESSAGE}", "warning")
            throw e
        }
    }
}


def slackSendWithRetry(SLACK_CHANNEL, MESSAGE, COLOR) {
    retry(5) {
        try {
            timeout(time: 1, unit: 'MINUTES') {
                slackSend(channel: "${SLACK_CHANNEL}", color: "${COLOR}", message: "${MESSAGE}")
            }
        } catch(err) {
            echo "Failed to send slack message error: ${err}"
        }
    }
}