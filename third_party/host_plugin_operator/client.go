package host_plugin_operator

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"regexp"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.smartx.com/LCM/lcm-manager/third_party/tower"
	"github.smartx.com/LCM/lcm-manager/third_party/tower/schema"
)

type Client interface {
	GetHostPlugin(ctx context.Context, id string) (*tower.QueryHostPluginByIdsHostPluginsHostPlugin, error)
	CreateHostPlugin(ctx context.Context, input *schema.HostPluginCreateInput, extEnvs map[string]string, timeoutMinutes int) (*tower.CreateHostPluginCreateHostPlugin, string, error)
	UpgradeHostPlugin(ctx context.Context, hostPlugin *tower.QueryHostPluginByIdsHostPluginsHostPlugin, targetPackage string, extEnvs map[string]string, timeoutMinutes int) (*tower.UpgradeHostPluginUpdateHostPlugin, string, error)
	UpdateHostPlugin(ctx context.Context, hostPlugin *tower.QueryHostPluginByIdsHostPluginsHostPlugin, extEnvs map[string]string, timeoutMinutes int) (*tower.UpgradeHostPluginUpdateHostPlugin, string, error)
	DeleteHostPlugin(ctx context.Context, id string, force bool) (*tower.DeleteHostPluginDeleteHostPlugin, string, error)
	QuerySpecificHostPluginsByClusters(ctx context.Context, namespace string, hostsID []string) ([]tower.QuerySpecificHostPluginsByClustersHostPluginsHostPlugin, error)

	GetHostPluginPackage(ctx context.Context, id string) (*tower.QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage, error)
	QueryHostPluginPackage(ctx context.Context, name string, version string, arch tower.Architecture) ([]tower.QueryHostPluginPackagesHostPluginPackagesHostPluginPackage, error)
	DeleteHostPluginPackage(ctx context.Context, id string) error
	UploadHostPluginPackage(url string, filePath string, fileSize int64) (string, error)

	GetTask(ctx context.Context, taskId string) (*tower.QueryTaskByIdTasksTask, error)
}

type client struct {
	towerClient tower.Client
}

var _ Client = &client{}

func NewClient(towerClient tower.Client) Client {
	return &client{
		towerClient: towerClient,
	}
}

func (c *client) CreateHostPlugin(ctx context.Context, input *schema.HostPluginCreateInput, extEnvs map[string]string, timeoutMinutes int) (*tower.CreateHostPluginCreateHostPlugin, string, error) {
	b, err := json.Marshal(input)
	if err != nil {
		return nil, "", errors.Wrap(err, "marshal input failed")
	}
	input.Values.Set = parseEnvsToValues(extEnvs, input.Values.Set)
	log.Printf("HostPluginOperator: create host plugin, input: %+v", string(b))
	graphqlReq := graphqlRequest{
		Query: "mutation CreateHostPlugin($input:HostPluginCreateInput!,$timeout_minutes:Int!){" +
			"createHostPlugin(" +
			"    data:$input," +
			"    effect:{timeout_minutes:$timeout_minutes,internal_task:true}" +
			"){id}}",
		Variables: map[string]any{
			"input":           input,
			"timeout_minutes": timeoutMinutes,
		},
	}
	httpResp, graphResp, err := makeRawRequest(ctx, c.towerClient, &graphqlReq)
	if err != nil {
		return nil, "", errors.Wrap(err, "create host plugin failed")
	}

	var data tower.CreateHostPluginResponse
	b, err = json.Marshal(graphResp.Data)
	if err != nil {
		return nil, "", errors.Wrap(err, "marshal response data failed")
	}
	err = json.Unmarshal(b, &data)
	if err != nil {
		return nil, "", errors.Wrap(err, "unmarshal response data failed")
	}

	taskId := httpResp.Header.Get("X-Task-Id")
	if taskId == "" {
		return nil, "", errors.New("create host plugin failed, task id is empty")
	}

	return &data.CreateHostPlugin, taskId, nil
}

func (c *client) GetHostPluginPackage(ctx context.Context, id string) (*tower.QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage, error) {
	resp, err := tower.QueryHostPluginPackagesByIds(ctx, c.towerClient.C(), []string{id})
	if err != nil {
		return nil, errors.Wrap(err, "query host plugin package failed")
	}
	if len(resp.HostPluginPackages) == 0 {
		return nil, nil
	}
	return &resp.HostPluginPackages[0], nil
}

func (c *client) QueryHostPluginPackage(ctx context.Context, name string, version string, arch tower.Architecture) ([]tower.QueryHostPluginPackagesHostPluginPackagesHostPluginPackage, error) {
	resp, err := tower.QueryHostPluginPackages(ctx, c.towerClient.C(), name, version, arch)
	if err != nil {
		return nil, errors.Wrap(err, "query host plugin package failed")
	}
	return resp.HostPluginPackages, nil
}

func (c *client) DeleteHostPluginPackage(ctx context.Context, id string) error {
	log.Printf("HostPluginOperator: delete host plugin package, package id: %s", id)
	_, err := tower.DeleteHostPluginPackage(ctx, c.towerClient.C(), id)
	if err != nil {
		if ok, _ := regexp.MatchString("No Node .* found", err.Error()); ok {
			return nil
		}
		return errors.Wrap(err, "delete host plugin package failed")
	}
	return nil
}

func (c *client) QuerySpecificHostPluginsByClusters(ctx context.Context, namespace string, hostsID []string) ([]tower.QuerySpecificHostPluginsByClustersHostPluginsHostPlugin, error) {
	resp, err := tower.QuerySpecificHostPluginsByClusters(ctx, c.towerClient.C(), namespace, hostsID)
	if err != nil {
		return nil, errors.Wrap(err, "query host plugins failed")
	}
	return resp.HostPlugins, nil
}

func (c *client) GetHostPlugin(ctx context.Context, id string) (*tower.QueryHostPluginByIdsHostPluginsHostPlugin, error) {
	resp, err := tower.QueryHostPluginByIds(ctx, c.towerClient.C(), []string{id})
	if err != nil {
		return nil, errors.Wrap(err, "query host plugin failed")
	}
	if len(resp.HostPlugins) == 0 {
		return nil, nil
	}
	return &resp.HostPlugins[0], nil
}

func (c *client) UpgradeHostPlugin(ctx context.Context, hostPlugin *tower.QueryHostPluginByIdsHostPluginsHostPlugin, targetPackage string, extEnvs map[string]string, timeoutMinutes int) (*tower.UpgradeHostPluginUpdateHostPlugin, string, error) {
	log.Printf("HostPluginOperator: upgrade host plugin, id: %s, targetPackage: %s, extEnvs: %+v", hostPlugin.Id, targetPackage, extEnvs)

	graphqlReq := graphqlRequest{
		Query: "mutation UpgradeHostPlugin($id:ID!, $target_package:ID!, $values: [String!], $timeout_minutes:Int!) {" +
			"updateHostPlugin(" +
			"    data:{" +
			"        host_plugin_package:{connect:{id:$target_package}}," +
			"        values:{set:$values}" +
			"    }," +
			"    where:{id:$id}," +
			"    effect:{timeout_minutes:$timeout_minutes,internal_task:true}" +
			"){id}}",
		Variables: map[string]any{
			"id":              hostPlugin.Id,
			"target_package":  targetPackage,
			"values":          parseEnvsToValues(extEnvs, hostPlugin.Values),
			"timeout_minutes": timeoutMinutes,
		},
	}
	httpResp, graphResp, err := makeRawRequest(ctx, c.towerClient, &graphqlReq)
	if err != nil {
		return nil, "", errors.Wrap(err, "upgrade host plugin failed")
	}

	var data tower.UpgradeHostPluginResponse
	b, err := json.Marshal(graphResp.Data)
	if err != nil {
		return nil, "", errors.Wrap(err, "marshal response data failed")
	}
	err = json.Unmarshal(b, &data)
	if err != nil {
		return nil, "", errors.Wrap(err, "unmarshal response data failed")
	}

	taskId := httpResp.Header.Get("X-Task-Id")
	if taskId == "" {
		return nil, "", errors.New("upgrade host plugin failed, task id is empty")
	}

	return &data.UpdateHostPlugin, taskId, nil
}

func (c *client) UpdateHostPlugin(ctx context.Context, hostPlugin *tower.QueryHostPluginByIdsHostPluginsHostPlugin, extEnvs map[string]string, timeoutMinutes int) (*tower.UpgradeHostPluginUpdateHostPlugin, string, error) {
	log.Printf("HostPluginOperator: update host plugin, id: %s, extEnvs: %+v", hostPlugin.Id, extEnvs)

	graphqlReq := graphqlRequest{
		Query: "mutation UpdateHostPlugin($id:ID!, $values: [String!], $timeout_minutes:Int!) {" +
			"updateHostPlugin(" +
			"    data:{" +
			"        values:{set:$values}" +
			"    }," +
			"    where:{id:$id}," +
			"    effect:{timeout_minutes:$timeout_minutes,internal_task:true}" +
			"){id}}",
		Variables: map[string]any{
			"id":              hostPlugin.Id,
			"values":          parseEnvsToValues(extEnvs, hostPlugin.Values),
			"timeout_minutes": timeoutMinutes,
		},
	}
	httpResp, graphResp, err := makeRawRequest(ctx, c.towerClient, &graphqlReq)
	if err != nil {
		return nil, "", errors.Wrap(err, "update host plugin failed")
	}

	var data tower.UpgradeHostPluginResponse
	b, err := json.Marshal(graphResp.Data)
	if err != nil {
		return nil, "", errors.Wrap(err, "marshal response data failed")
	}
	err = json.Unmarshal(b, &data)
	if err != nil {
		return nil, "", errors.Wrap(err, "unmarshal response data failed")
	}

	taskId := httpResp.Header.Get("X-Task-Id")
	if taskId == "" {
		return nil, "", errors.New("update host plugin failed, task id is empty")
	}

	return &data.UpdateHostPlugin, taskId, nil
}

func (c *client) DeleteHostPlugin(ctx context.Context, id string, force bool) (*tower.DeleteHostPluginDeleteHostPlugin, string, error) {
	log.Printf("HostPluginOperator: delete host plugin, id: %s, force: %t", id, force)

	graphqlReq := graphqlRequest{
		Query: "mutation DeleteHostPlugin($id:ID!,$force:Boolean!){deleteHostPlugin(where:{id:$id},effect:{force:$force,internal_task:true}){id}}",
		Variables: map[string]any{
			"id":    id,
			"force": force,
		},
	}
	httpResp, graphResp, err := makeRawRequest(ctx, c.towerClient, &graphqlReq)
	if err != nil {
		return nil, "", errors.Wrap(err, "create host plugin failed")
	}

	var data tower.DeleteHostPluginResponse
	b, err := json.Marshal(graphResp.Data)
	if err != nil {
		return nil, "", errors.Wrap(err, "marshal response data failed")
	}
	err = json.Unmarshal(b, &data)
	if err != nil {
		return nil, "", errors.Wrap(err, "unmarshal response data failed")
	}

	taskId := httpResp.Header.Get("X-Task-Id")
	if taskId == "" {
		return nil, "", errors.New("create host plugin failed, task id is empty")
	}

	return &data.DeleteHostPlugin, taskId, nil
}

func (c *client) UploadHostPluginPackage(url string, filePath string, fileSize int64) (string, error) {
	log.Printf("HostPluginOperator: upload host plugin package, url: %s, filePath: %s", url, filePath)
	if _, stat := os.Stat(filePath); os.IsNotExist(stat) {
		return "", errors.New("file not exist")
	}
	args := fmt.Sprintf(`--location -X POST %s?size=%d -F file=@"%s"`, url, fileSize, filePath)
	cmd := exec.Command("curl", strings.Split(args, " ")...)
	output, err := cmd.Output()
	if err != nil {
		return "", errors.Wrap(err, "upload host plugin package failed")
	}
	type response struct {
		ErrorCode    string `json:"error_code"`
		ErrorMessage string `json:"error_message"`
		PackageId    string `json:"package_id"`
	}
	var res response
	err = json.Unmarshal(output, &res)
	if err != nil {
		return "", errors.Wrap(err, "unmarshal response data failed, output: "+string(output))
	}
	if res.ErrorCode != "" {
		return "", errors.New(fmt.Sprintf("upload host plugin package failed, %s: %s", res.ErrorCode, res.ErrorMessage))
	}
	return res.PackageId, nil
}

func (c *client) GetTask(ctx context.Context, taskId string) (*tower.QueryTaskByIdTasksTask, error) {
	resp, err := tower.QueryTaskById(ctx, c.towerClient.C(), taskId)
	if err != nil {
		return nil, errors.Wrap(err, "query task failed")
	}
	if len(resp.Tasks) == 0 {
		return nil, nil
	}
	return &resp.Tasks[0], nil
}

func ParseCreateInput(name string, namespace string, packageId string, clusterId string, hostId string) *schema.HostPluginCreateInput {
	input := &schema.HostPluginCreateInput{
		Name:      name,
		Namespace: namespace,
		Values: &schema.Values{
			Set: []string{},
		},
		HostPluginPackage: &schema.HostPluginPackageInput{
			Connect: &schema.Connect{
				ID: packageId,
			},
		},
		Cluster: &schema.ClusterInput{
			Connect: &schema.Connect{
				ID: clusterId,
			},
		},
		Hosts: &schema.HostInput{
			Connect: []*schema.Connect{
				{ID: hostId},
			},
		},
		DisableSelector: true,
	}
	return input
}

func parseEnvsToValues(envs map[string]string, values []string) []string {
	newValues := make([]string, len(values))
	copy(newValues, values)
	for k, v := range envs {
		if _, i, found := lo.FindIndexOf(newValues, func(item string) bool {
			return strings.HasPrefix(item, fmt.Sprintf("%s=", k))
		}); found {
			newValues[i] = fmt.Sprintf("%s=%s", k, v)
		} else {
			newValues = append(newValues, fmt.Sprintf("%s=%s", k, v))
		}
	}
	return newValues
}

type graphqlRequest struct {
	Query     string         `json:"query"`
	Variables map[string]any `json:"variables"`
}

type graphqlResponse struct {
	Data   any            `json:"data"`
	Errors []graphqlError `json:"errors"`
}

type graphqlError struct {
	Message string   `json:"message"`
	Path    []string `json:"path"`
	Code    string   `json:"code"`
}

func makeRawRequest(ctx context.Context, client tower.Client, request *graphqlRequest) (*http.Response, *graphqlResponse, error) {
	body, err := json.Marshal(request)
	if err != nil {
		return nil, nil, errors.Wrap(err, "marshal request body failed")
	}
	c := &http.Client{}
	req, err := http.NewRequest("POST", client.GetEndpoint(), bytes.NewBuffer(body))
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", client.GetToken())
	resp, err := c.Do(req)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to send request")
	}
	defer resp.Body.Close()
	body, err = io.ReadAll(resp.Body)

	if err != nil {
		return nil, nil, errors.Wrap(err, "read response body failed")
	}
	if resp.StatusCode/100 != 2 {
		return nil, nil, errors.New(fmt.Sprintf("request failed, status code: %d, body: %s", resp.StatusCode, string(body)))
	}
	var graphqlResp graphqlResponse
	err = json.Unmarshal(body, &graphqlResp)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshal response body failed")
	}
	if len(graphqlResp.Errors) > 0 {
		return nil, nil, errors.New(graphqlResp.Errors[0].Message)
	}
	return resp, &graphqlResp, nil
}
