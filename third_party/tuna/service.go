package tuna

import (
	"path"
)

type HostServiceName string

const (
	HostServiceNameZbsMeta HostServiceName = "zbs-metad.service"
)

type HostServiceRole string

const (
	HostServiceRoleLeader   HostServiceRole = "leader"
	HostServiceRoleFollower HostServiceRole = "follower"
)

type HostServiceRoleState string

const (
	HostServiceRoleStateOK HostServiceRoleState = "ok"
)

type HostServiceInfo struct {
	ServiceName   string  `json:"service_name"`
	ServiceState  string  `json:"service_state"`
	RunningState  string  `json:"running_state"`
	StateDuration float64 `json:"state_duration"`
	ExecPath      string  `json:"exec_path"`
	CurrentPid    int     `json:"current_pid"`
	LastPid       int     `json:"last_pid"`
	Pinfo         struct {
		Name      string               `json:"name"`
		Status    string               `json:"status"`
		IsRunning bool                 `json:"is_running"`
		Uptime    int                  `json:"uptime"`
		User      string               `json:"user"`
		Role      HostServiceRole      `json:"role"`
		RoleState HostServiceRoleState `json:"role_state"`
	} `json:"pinfo"`
}

func (h *HostServiceInfo) IsMetaLeader() bool {
	if h == nil {
		return false
	}

	return h.Pinfo.Role == HostServiceRoleLeader
}

func (c *Client) GetServiceInfo(serviceName string) (*HostServiceInfo, error) {
	var resp HostServiceInfo

	queryParams := map[string]string{
		"show_process_info": "true",
	}

	if err := c.doRequest("GET", path.Join("services", serviceName), queryParams, nil, &resp); err != nil {
		return nil, err
	}

	if resp.ServiceName != serviceName {
		return c.getServiceInfo(serviceName)
	}

	return &resp, nil
}

func (c *Client) getServiceInfo(serviceName string) (*HostServiceInfo, error) {
	services, err := c.GetServices()
	if err != nil {
		return nil, err
	}

	for _, service := range services {
		if service.ServiceName == serviceName {
			return service, nil
		}
	}

	return nil, nil
}

func (c *Client) GetServices() ([]*HostServiceInfo, error) {
	var resp []*HostServiceInfo

	queryParams := map[string]string{
		"show_process_info": "true",
	}

	if err := c.doRequest("GET", "services", queryParams, nil, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

// /api/v2/services
