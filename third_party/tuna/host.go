package tuna

import (
	"fmt"
	"net/http"
)

type HostState string

const (
	HostStateInuse                   HostState = "in_use"
	HostStateEnteringMaintenanceMode HostState = "entering_maintenance_mode"
	HostStateMaintenanceMode         HostState = "maintenance_mode"
	HostStateRemoving                HostState = "removing"
)

type GetHostStateResponse struct {
	HostUUID  string `json:"host_uuid"`
	HostState struct {
		UUID             string    `json:"uuid"`
		State            HostState `json:"state"`
		MaintenanceJobID string    `json:"maintenance_job_id"`
	} `json:"host_state"`
}

func (c *Client) GetHostState(hostUUID string) (*GetHostStateResponse, error) {
	params := map[string]string{
		"host_uuid":        hostUUID,
		"fields":           "host_state",
		"show_host_states": "true",
	}

	var resp []GetHostStateResponse
	if err := c.doRequest(http.MethodGet, "management/hosts", params, nil, &resp); err != nil {
		return nil, err
	}

	for _, host := range resp {
		if host.HostUUID == hostUUID {
			return &host, nil
		}
	}

	return nil, fmt.Errorf("host %s not found", hostUUID)
}

type GetHostLabelsResponse struct {
	Labels  map[string]interface{} `json:"labels"`
	Version int                    `json:"version"`
}

func (c *Client) GetHostLabels(hostUUID string) (*GetHostLabelsResponse, error) {
	var resp GetHostLabelsResponse
	if err := c.doRequest(http.MethodGet, fmt.Sprintf("management/hosts/%s/labels", hostUUID), nil, nil, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

type UpdateHostLabelsRequest struct {
	Labels  map[string]interface{} `json:"labels"`
	Version int                    `json:"version"`
}

func (c *Client) UpdateHostLabels(hostUUID string, labels map[string]interface{}, version int) (*GetHostLabelsResponse, error) {
	body := UpdateHostLabelsRequest{
		Labels:  labels,
		Version: version,
	}

	var resp GetHostLabelsResponse
	if err := c.doRequest(http.MethodPost, fmt.Sprintf("management/hosts/%s/labels", hostUUID), nil, body, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

type ExtendState string

const (
	ExtendStateProcessing ExtendState = "processing"
	ExtendStateDone       ExtendState = "done"
	ExtendStateFailed     ExtendState = "failed"
)

type ExtendHost struct {
	HostName string `json:"hostname"`
	HostIP   string `json:"host_ip"`
}

type GetClusterExtendResponse struct {
	UUID        string       `json:"uuid"`
	ExtendState ExtendState  `json:"extend_state"`
	Hosts       []ExtendHost `json:"hosts"`
}

func (c *Client) GetClusterExtendindItem() (*GetClusterExtendResponse, error) {
	var resp []GetClusterExtendResponse

	if err := c.doRequest(http.MethodGet, "management/cluster_extend/hosts", nil, nil, &resp); err != nil {
		return nil, err
	}

	for _, extend := range resp {
		if extend.ExtendState == ExtendStateProcessing {
			return &extend, nil
		}
	}

	return nil, nil
}
