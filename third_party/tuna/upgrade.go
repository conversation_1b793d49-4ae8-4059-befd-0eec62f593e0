package tuna

import (
	"net/http"
	"strings"
)

type ClusterUpgradeStatus string

const (
	ClusterUpgradeStatusRunning ClusterUpgradeStatus = "running"
	ClusterUpgradeStatusFailed  ClusterUpgradeStatus = "failed"
	ClusterUpgradeStatusSuccess ClusterUpgradeStatus = "successful"
)

type GetClusterUpgradeProgressResponse struct {
	ID                    string               `json:"_id" validate:"required"`
	EndTime               interface{}          `json:"end_time"`
	FailedMessage         interface{}          `json:"failed_message"`
	PreUpgradeProgress    interface{}          `json:"pre_upgrade_progress"`
	RunningNodeIP         string               `json:"running_node_ip" validate:"required"`
	SrcIsoVersion         string               `json:"src_iso_version" validate:"required"`
	StartTimestamp        int                  `json:"start_timestamp" validate:"required"`
	Status                ClusterUpgradeStatus `json:"status" validate:"required"`
	TargetIsoFile         string               `json:"target_iso_file" validate:"required"`
	TargetIsoMetadataFile string               `json:"target_iso_metadata_file" validate:"required"`
	UpgradeNodeIps        []interface{}        `json:"upgrade_node_ips" validate:"required"`
	UpgradeProgress       UpgradeProgress      `json:"upgrade_progress" validate:"required"`
	UUID                  string               `json:"uuid" validate:"required"`
}

type UpgradeProgress struct {
	ActualStage  string         `json:"actual_stage" validate:"required"`
	CurrentStep  int            `json:"current_step" validate:"required"`
	NodeProgress []NodeProgress `json:"node_progress" validate:"required"`
	Progress     float64        `json:"progress" validate:"required"`
	Stage        string         `json:"stage" validate:"required"`
	TotalStep    int            `json:"total_step" validate:"required"`
}

type NodeProgress struct {
	ActualStage   string        `json:"actual_stage" validate:"required"`
	CurrentStep   int           `json:"current_step" validate:"required"`
	FinishedStage []interface{} `json:"finished_stage" validate:"required"`
	NodeIP        string        `json:"node_ip" validate:"required"`
	Progress      float64       `json:"progress" validate:"required"`
	Stage         string        `json:"stage" validate:"required"`
	TotalStep     int           `json:"total_step" validate:"required"`
}

func (c *Client) GetClusterUpgraderProgress() (*GetClusterUpgradeProgressResponse, error) {
	var resp *GetClusterUpgradeProgressResponse
	if err := c.doRequest(http.MethodGet, "cluster_upgrader/progress", nil, nil, &resp); err != nil {
		if strings.HasPrefix(err.Error(), "[E_NOT_FOUND]") {
			return nil, nil
		}

		return nil, err
	}

	return resp, nil
}
