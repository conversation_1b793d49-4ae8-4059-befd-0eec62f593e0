package tuna

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	retryCount       = 6
	retryWaitTime    = 100 * time.Millisecond  // use default retry wait time
	retryMaxWaitTime = 2000 * time.Millisecond // use default retry max wait time
	defaultTimeout   = 10 * time.Second
)

type Client struct {
	// token is maintained by caller, if expired sdk would return an error to the caller
	token string
	// internalToken is maintained by sdk, when token is not provided, sdk would fetch internalToken by username and password. when expired sdk would refresh it, instead of returning an error to caller
	internalToken string
	restyClient   *resty.Client
	// scheme default is http, can call SetSchemeToHttps change to https
	scheme string
	host   string
}

// NewClient new a defual client
func NewClient(host string, token string) *Client {
	restyClient := resty.New().
		SetTimeout(defaultTimeout).
		SetRetryCount(retryCount).
		SetRetryWaitTime(retryWaitTime).
		SetRetryMaxWaitTime(retryMaxWaitTime).
		AddRetryCondition(defaultRetryCondition())

	return &Client{
		restyClient: restyClient,
		token:       token,
		scheme:      "http",
		host:        host,
	}
}

const (
	EcEOK = "EOK"
)

func defaultRetryCondition() resty.RetryConditionFunc {
	return func(resp *resty.Response, err error) bool {
		if resp == nil || err != nil {
			return true
		}

		if resp.StatusCode() == http.StatusUnauthorized {
			return true
		}

		if resp.StatusCode() == http.StatusNotFound {
			return false
		}

		if resp.IsError() {
			return true
		}

		var result result
		if err := json.Unmarshal(resp.Body(), &result); err != nil {
			return true
		}

		return false
	}
}

type result struct {
	EC    string          `json:"ec"`
	Data  json.RawMessage `json:"data"`
	Error interface{}     `json:"error"`
}

func (c *Client) doRequest(method string, path string, params map[string]string, body interface{}, data interface{}) error {
	resp, err := c.sendRequest(method, path, params, body)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		return errors.New(http.StatusText(resp.StatusCode()))
	}

	r := resp.Result().(*result) //nolint: errcheck
	if r.EC != EcEOK {
		return fmt.Errorf("[%s]: %s", r.EC, r.Error)
	}

	if data == nil {
		return nil
	}

	if err := json.Unmarshal([]byte(r.Data), data); err != nil {
		return err
	}

	return nil
}

func (c *Client) sendRequest(method string, path string, params map[string]string, body interface{}) (*resty.Response, error) {
	c.restyClient.SetBaseURL(fmt.Sprintf("%s://%s/api/v2", c.scheme, c.host))

	req := c.restyClient.R().SetQueryParams(params).SetBody(body).SetResult(&result{})

	if c.token != "" {
		req = req.SetHeader("X-SmartX-Token", c.token)
	} else if c.internalToken != "" {
		req = req.SetHeader("X-SmartX-Token", c.internalToken)
	}

	switch method {
	case "GET":
		return req.Get(path)
	case "POST":
		return req.Post(path)
	case "PUT":
		return req.Put(path)
	case "PATCH":
		return req.Patch(path)
	case "DELETE":
		return req.Delete(path)
	default:
		panic(method)
	}
}

func (c *Client) SetTimeout(duration time.Duration) {
	c.restyClient.SetTimeout(duration)
}
