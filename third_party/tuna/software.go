package tuna

type GetSoftwareResponse struct {
	Hypervisor          string `json:"hypervisor"`
	LatestVersion       string `json:"latest_version"`
	LatestOemVersion    string `json:"latest_oem_version"`
	PatchVersion        string `json:"patch_version"`
	RdmaEnabled         bool   `json:"rdma_enabled"`
	VhostEnabled        bool   `json:"vhost_enabled"`
	NvmeOverRdmaEnabled bool   `json:"nvme_over_rdma_enabled"`
	NvmeOverTCPEnabled  bool   `json:"nvme_over_tcp_enabled"`
	Distribution        string `json:"distribution"`
	ReleaseDate         string `json:"release_date"`
	ReleaseVersion      string `json:"rc_version"`
}

func (c *Client) GetSoftware() (*GetSoftwareResponse, error) {
	var resp GetSoftwareResponse
	if err := c.doRequest("GET", "cluster/software", nil, nil, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}
