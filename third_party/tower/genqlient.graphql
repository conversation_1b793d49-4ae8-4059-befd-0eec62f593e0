mutation Login($login: LoginInput!) {
  login(data: $login) {
    token
  }
}

query QueryCloudTowerVersion {
  deploys(first: 1) {
    version
  }
}

query QueryHostPluginPackages(
  $name: String!
  $version: String!
  $arch: Architecture!
) {
  hostPluginPackages(where: { name: $name, version: $version, arch: $arch }) {
    id
    name
    version
    arch
    size
  }
}

query QueryHostPluginPackagesByIds($ids: [ID!]) {
  hostPluginPackages(where: { id_in: $ids }) {
    id
    name
    version
    arch
    size
  }
}

mutation DeleteHostPluginPackage($id: ID!) {
  deleteHostPluginPackage(where: { id: $id }) {
    id
  }
}

query QueryHostPlugins(
  $cluster: ID!
  $package_name: String!
  $package_version: String!
  $package_arch: Architecture!
) {
  hostPlugins(
    where: {
      cluster: { id: $cluster }
      host_plugin_package: {
        name: $package_name
        version: $package_version
        arch: $package_arch
      }
    }
  ) {
    id
    name
    namespace
    values
    cluster {
      id
      name
    }
    host_plugin_package {
      id
      name
      version
      arch
      size
    }
    host_plugin_instances
    hosts {
      management_ip
    }
  }
}

query QueryHostPluginByIds($ids: [ID!]) {
  hostPlugins(where: { id_in: $ids }) {
    id
    name
    namespace
    values
    cluster {
      id
      name
    }
    host_plugin_package {
      id
      name
      version
      arch
      size
    }
    host_plugin_instances
    hosts {
      management_ip
    }
  }
}

query QuerySpecificHostPluginsByClusters(
  $namespace: String!
  $cluster_ids: [ID!]
) {
  hostPlugins(
    where: { namespace: $namespace, cluster: { id_in: $cluster_ids } }
  ) {
    id
    name
    namespace
    values
    cluster {
      id
      name
    }
    host_plugin_package {
      id
      name
      version
      arch
      size
    }
    host_plugin_instances
    hosts {
      management_ip
    }
  }
}

mutation CreateHostPlugin($input: HostPluginCreateInput!) {
  createHostPlugin(data: $input) {
    id
  }
}

mutation UpgradeHostPlugin($id: ID!, $target_package: ID!, $values: [String!]) {
  updateHostPlugin(
    data: {
      host_plugin_package: { connect: { id: $target_package } }
      values: { set: $values }
    }
    where: { id: $id }
  ) {
    id
  }
}

mutation UpdateHostPlugin($id: ID!, $values: [String!]) {
  updateHostPlugin(data: { values: { set: $values } }, where: { id: $id }) {
    id
  }
}

mutation DeleteHostPlugin($id: ID!) {
  deleteHostPlugin(where: { id: $id }) {
    id
  }
}

query QueryClustersByIds($ids: [ID!]) {
  clusters(where: { id_in: $ids }) {
    id
    local_id
    ip
    architecture
    version
    name
    hosts {
      id
      local_id
      name
      scvm_name
      management_ip
      data_ip
      state
      status
      merged_status
      connect_status
      host_state {
        state
      }
    }
    applications {
      id
      type
      version
    }
  }
}

query QueryClustersByLocalIds($local_ids: [String!]) {
  clusters(where: { local_id_in: $local_ids }) {
    id
    local_id
    ip
    type
    architecture
    version
    name
    hypervisor
    hosts {
      id
      local_id
      name
      scvm_name
      management_ip
      data_ip
      state
      status
      merged_status
      connect_status
      host_state {
        state
      }
    }
  }
}

query QueryTaskById($id: ID!) {
  tasks(where: { id: $id }) {
    id
    status
    args
    progress
    started_at
    finished_at
    description
    error_code
    error_message
    resource_id
    resource_mutation
    steps {
      key
      finished
    }
  }
}

query QueryTaskStartedAt($id: ID!) {
  tasks(where: { id: $id }) {
    started_at
  }
}

query QueryVlansByIds($ids: [ID!]) {
  vlans(where: { id_in: $ids }) {
    id
    vlan_id
    name
  }
}

mutation CreateTask($input: TaskCreateInput!) {
  createTask(data: $input) {
    id
    status
  }
}

mutation UpdateTask($id: ID!, $input: TaskUpdateInput!) {
  updateTask(data: $input, where: { id: $id }) {
    id
    status
  }
}

mutation CreateUserAuditLog($input: UserAuditLogCreateInput!) {
  createUserAuditLog(data: $input) {
    id
    status
  }
}

query QueryHostPluginsByPackageName($package_name: String!) {
  hostPlugins(where: { host_plugin_package: { name: $package_name } }) {
    host_plugin_package {
      id
      name
      version
      arch
    }
  }
}

query QueryHostPluginPackagesByPackageName($package_name: String!) {
  hostPluginPackages(where: { name: $package_name }) {
    id
    name
    version
    arch
    size
  }
}

query QueryClustersInfo {
  clusters {
    name
    id
    local_id
    architecture
    cpu_vendor
    ip
    management_vip
    connect_state
    type
    hypervisor
    version
    patch_version
    upgrade_tool_version
    hosts {
      id
      name
      scvm_name
      local_id
      data_ip
      management_ip
      state
      merged_status
      connect_status
      host_state {
        state
      }
      chunk_id
      os_version
      role
      gpu_devices {
        user_usage
      }
    }
    witness {
      id
      name
      local_id
      data_ip
      management_ip
    }
  }
}

query QueryClusterInfoById($id: ID!) {
  cluster(where: { id: $id }) {
    name
    id
    local_id
    architecture
    cpu_vendor
    ip
    management_vip
    connect_state
    type
    hypervisor
    version
    patch_version
    upgrade_tool_version
    hosts {
      id
      name
      scvm_name
      local_id
      data_ip
      management_ip
      state
      connect_status
      merged_status
      os_version
      role
      chunk_id
      gpu_devices {
        user_usage
      }
    }
    witness {
      id
      name
      local_id
      data_ip
      management_ip
    }
  }
}

query QueryClusterInfoByLocalId($local_id: String!) {
  cluster(where: { local_id: $local_id }) {
    name
    id
    local_id
    architecture
    cpu_vendor
    ip
    management_vip
    connect_state
    type
    hypervisor
    version
    patch_version
    upgrade_tool_version
    stretch
    hosts {
      id
      name
      scvm_name
      local_id
      data_ip
      management_ip
      connect_status
      merged_status
      os_version
      role
      zone {
        is_preferred
      }
      chunk_id
      state
      status
      merged_status
      connect_status
      gpu_devices {
        user_usage
      }
    }
    witness {
      id
      name
      local_id
      data_ip
      management_ip
    }
    ntp_servers
  }
}

query QueryHostInfoById($id: ID!) {
  host(where: { id: $id }) {
    id
    name
    scvm_name
    local_id
    data_ip
    management_ip
    connect_status
    merged_status
    chunk_id
    role
    state
    status
    merged_status
    connect_status
  }
}

query QueryHostInfoByLocalId($local_id: String!) {
  host(where: { local_id: $local_id }) {
    id
    name
    scvm_name
    local_id
    data_ip
    management_ip
    connect_status
    merged_status
    chunk_id
    role
    state
    status
    merged_status
    connect_status
  }
}

query QueryClusterVMsStatus($id: ID!) {
  clusters(where: { id: $id }) {
    id
    vms {
      id
      name
      status
      host {
        id
        name
      }
    }
  }
}

query QueryInstalledHostPluginPackagesByPackageName($package_name: String!) {
  hostPlugins(where: { host_plugin_package: { name: $package_name } }) {
    host_plugin_package {
      id
    }
  }
}

query QueryUserIDByUserName($username: String!) {
  users(where: { username: $username }, first: 1) {
    id
  }
}

query QuerySystemVMsByHostID($host_id: ID) {
  hosts(where: { id: $host_id }) {
    id
    local_id
    vms {
      internal
      vm_usage
      local_id
    }
  }
}

query QueryHostVMsByHostLocalID($local_id: String) {
  host(where: { local_id: $local_id }) {
    id
    local_id
    name
    vm_num
    vms {
        id
        local_id
        name
        internal
        vm_usage
        in_recycle_bin
    }
  }
}

query QueryInRecycleBinVMsByClusterID($id: ID!) {
  vms(where: { in_recycle_bin: true, cluster: { id: $id } }) {
    local_id
  }
}

query QueryUpgradeCenterTasks {
  tasks(where: { resource_type: "upgradeCenterTask" }) {
    id
    status
    started_at
    finished_at
    resource_mutation
  }
}

query QueryUsbDevicesByHostLocalId ($host_local_id: String!) {
	usbDevices(where: {host:{local_id:$host_local_id}}) {
		name
		status
		host {
			name
			local_id
		}
		vms(where: {in_recycle_bin:false}) {
			local_id
			name
			in_recycle_bin
		}
	}
}

query QueryPlacementGroupsByHostLocalID ($host_local_id: String!) {
	vmPlacementGroups(where: {OR:[{vm_host_must_host_uuids_some:{local_id:$host_local_id}},{vm_host_prefer_host_uuids_some:{local_id:$host_local_id}}]}) {
		id
		name
		cluster {
			name
		}
		vm_host_must_host_uuids {
			id
			local_id
			name
		}
		vm_host_prefer_host_uuids {
			id
			local_id
			name
		}
	}
}

query QueryClusterWitnessStatus($local_id: String) {
  cluster(where: {local_id: $local_id}) {
    id
    local_id
    name
    stretch
    witness {
      id
      name
      local_id
      data_ip
      management_ip
    }
    metro_availability_checklist {
      witness {
        status
      }
    }
  }
}

mutation DeleteVMByID($id: ID!) {
  deleteVm(where: {id: $id }) {
    id
    name
    in_recycle_bin
  }
}

mutation UpdateVmHost($id: ID!, $node_ip: String) {
  updateVm(data: {node_ip: $node_ip}, effect: {remove_unmovable_devices_before_migrate: true}, where: {id: $id }) {
    id
    local_id
    name
    status
    hostname
    in_recycle_bin
  }
}
