package tower

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/Khan/genqlient/graphql"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"

	"github.smartx.com/LCM/lcm-manager/third_party/tower/schema"
)

const (
	towerAdminAPITimeout         = 90 * time.Second
	towerSyncHostResourceTimeout = 3 * time.Minute
	towerAdminAPIRetryCount      = 3
	ClusterResourceType          = "Cluster"
	TowerAdminAPIUserName        = "admin"
	TowerAdminAPIPassword        = "cloudtower"
)

type Client interface {
	C() graphql.Client
	PrismaC() graphql.Client
	GetToken() string
	GetEndpoint() string
	GetServer() string
	GetUsername() string
	GetPassword() string
	GetUserID() string
	QueryClustersByIds(ctx context.Context, ids []string) ([]QueryClustersByIdsClustersCluster, error)
	QueryClustersByLocalIds(ctx context.Context, ids []string) ([]QueryClustersByLocalIdsClustersCluster, error)
	QueryVlansByIds(ctx context.Context, ids []string) ([]QueryVlansByIdsVlansVlan, error)
	QueryCloudTowerVersion() (string, error)
	GetTask(ctx context.Context, taskID string) (*QueryTaskByIdTasksTask, error)
	CreateTask(ctx context.Context, input schema.TaskCreateInput) (*CreateTaskCreateTask, error)
	UpdateTask(ctx context.Context, id string, input schema.TaskUpdateInput) (*UpdateTaskUpdateTask, error)
	CreateUserAuditLog(ctx context.Context, input schema.UserAuditLogCreateInput) (*CreateUserAuditLogCreateUserAuditLog, error)
	GetClusterWitnessStatus(ctx context.Context, localID string) (*QueryClusterWitnessStatusCluster, error)

	GetClustersInfo(ctx context.Context) ([]QueryClustersInfoClustersCluster, error)
	GetClusterInfoByID(ctx context.Context, id string) (*QueryClusterInfoByIdCluster, error)
	GetClusterInfoByLocalID(ctx context.Context, local_id string) (*QueryClusterInfoByLocalIdCluster, error)
	GetHostInfoByID(ctx context.Context, id string) (*QueryHostInfoByIdHost, error)
	GetHostInfoByLocalID(ctx context.Context, local_id string) (*QueryHostInfoByLocalIdHost, error)
	GetHostVMsByHostLocalID(ctx context.Context, local_id string) (*QueryHostVMsByHostLocalIDHost, error)
	GetClusterVMsStatus(ctx context.Context, id string) ([]QueryClusterVMsStatusClustersClusterVmsVm, error)
	DeleteUnusedHostPluginPackages(ctx context.Context, packageName string) error
	UpdateClusterByAdminAPI(ctx context.Context, clusterID string) error
	GetSystemVMsByHostID(ctx context.Context, hostID string) ([]string, error)
	QueryInRecycleBinVMsByClusterID(ctx context.Context, clusterID string) ([]string, error)
	QueryTaskStartedAt(ctx context.Context, taskID string) (time.Time, error)
	QueryUpgradeCenterTasks(ctx context.Context) ([]QueryUpgradeCenterTasksTasksTask, error)
	GetUsbDevicesByHostLocalId(ctx context.Context, localID string) ([]QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice, error)
	GetPlacementGroupsByHostLocalID(ctx context.Context, localID string) ([]QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup, error)
	DeleteVMByID(ctx context.Context, vmID string) error
	UpdateVMHost(ctx context.Context, vmID string, nodeIP string) error
}

type client struct {
	c              graphql.Client
	transport      *authedTransport
	token          string
	endpoint       string
	server         string
	username       string
	password       string
	userID         string
	prismaEndpoint string
	prismaC        graphql.Client
	adminClient    *resty.Client
}

type authedTransport struct {
	token   string
	wrapped http.RoundTripper
}

func (t *authedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Set("Authorization", t.token)
	return t.wrapped.RoundTrip(req)
}

func NewClient(endpoint, server, username, password string, usersource UserSource, prismaEndpoint string, adminEndpoint string) (Client, error) {
	resp, err := Login(context.Background(), graphql.NewClient(endpoint, http.DefaultClient), LoginInput{
		Username: username,
		Password: password,
		Source:   usersource,
		Mfa_type: MfaTypeMail,
	})
	if err != nil {
		return nil, err
	}

	c := &client{
		endpoint:       endpoint,
		server:         server,
		username:       username,
		password:       password,
		prismaEndpoint: prismaEndpoint,
	}

	c.transport = &authedTransport{
		token:   resp.Login.Token,
		wrapped: http.DefaultTransport,
	}

	c.token = resp.Login.Token
	c.c = graphql.NewClient(endpoint, &http.Client{Transport: c.transport})
	c.prismaC = graphql.NewClient(prismaEndpoint, http.DefaultClient)

	resp2, err := QueryUserIDByUserName(context.Background(), c.prismaC, username)
	if err != nil {
		return nil, err
	}

	if len(resp2.Users) == 0 {
		return nil, ErrorNotFound
	}

	c.userID = resp2.Users[0].Id

	c.adminClient = resty.New().
		SetDisableWarn(true).
		SetTimeout(towerAdminAPITimeout).
		SetBaseURL(adminEndpoint).
		SetRetryCount(towerAdminAPIRetryCount).
		SetBasicAuth(TowerAdminAPIUserName, TowerAdminAPIPassword)

	return c, nil
}

func (c *client) C() graphql.Client {
	return c.c
}

func (c *client) PrismaC() graphql.Client {
	return c.prismaC
}

func (c *client) GetToken() string {
	return c.token
}

func (c *client) GetEndpoint() string {
	return c.endpoint
}

func (c *client) GetServer() string {
	return c.server
}

func (c *client) GetUsername() string {
	return c.username
}

func (c *client) GetPassword() string {
	return c.password
}

func (c *client) GetUserID() string {
	return c.userID
}

func (c *client) QueryCloudTowerVersion() (string, error) {
	resp, err := QueryCloudTowerVersion(context.Background(), c.c)
	if err != nil {
		return "", err
	}

	if len(resp.Deploys) == 0 {
		return "", ErrorNotFound
	}

	return resp.Deploys[0].Version, nil
}

func (c *client) QueryClustersByIds(ctx context.Context, ids []string) ([]QueryClustersByIdsClustersCluster, error) {
	resp, err := QueryClustersByIds(ctx, c.c, ids)
	if err != nil {
		return nil, err
	}

	if len(resp.Clusters) == 0 {
		return nil, ErrorNotFound
	}

	return resp.Clusters, nil
}

func (c *client) QueryClustersByLocalIds(ctx context.Context, ids []string) ([]QueryClustersByLocalIdsClustersCluster, error) {
	resp, err := QueryClustersByLocalIds(ctx, c.c, ids)
	if err != nil {
		return nil, err
	}

	if len(resp.Clusters) == 0 {
		return nil, ErrorNotFound
	}

	return resp.Clusters, nil
}

func (c *client) QueryVlansByIds(ctx context.Context, ids []string) ([]QueryVlansByIdsVlansVlan, error) {
	resp, err := QueryVlansByIds(ctx, c.c, ids)
	if err != nil {
		return nil, err
	}

	if len(resp.Vlans) == 0 {
		return nil, ErrorNotFound
	}

	return resp.Vlans, nil
}

func (c *client) CreateTask(ctx context.Context, input schema.TaskCreateInput) (*CreateTaskCreateTask, error) {
	resp, err := CreateTask(ctx, c.c, input)
	if err != nil {
		return nil, err
	}

	return &resp.CreateTask, nil
}

func (c *client) UpdateTask(ctx context.Context, id string, input schema.TaskUpdateInput) (*UpdateTaskUpdateTask, error) {
	resp, err := UpdateTask(ctx, c.c, id, input)
	if err != nil {
		return nil, err
	}

	return &resp.UpdateTask, nil
}

func (c *client) CreateUserAuditLog(ctx context.Context, input schema.UserAuditLogCreateInput) (*CreateUserAuditLogCreateUserAuditLog, error) {
	resp, err := CreateUserAuditLog(ctx, c.prismaC, input)
	if err != nil {
		return nil, err
	}

	return &resp.CreateUserAuditLog, nil
}

func (c *client) GetClustersInfo(ctx context.Context) ([]QueryClustersInfoClustersCluster, error) {
	resp, err := QueryClustersInfo(ctx, c.c)
	if err != nil {
		return nil, err
	}

	if len(resp.Clusters) == 0 {
		return nil, ErrorNotFound
	}

	return resp.Clusters, nil
}

func (c *client) GetClusterInfoByID(ctx context.Context, id string) (*QueryClusterInfoByIdCluster, error) {
	resp, err := QueryClusterInfoById(ctx, c.c, id)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, ErrorNotFound
	}

	return &resp.Cluster, nil
}

func (c *client) GetClusterInfoByLocalID(ctx context.Context, localID string) (*QueryClusterInfoByLocalIdCluster, error) {
	resp, err := QueryClusterInfoByLocalId(ctx, c.PrismaC(), localID)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, ErrorNotFound
	}

	return &resp.Cluster, nil
}

func (c *client) GetClusterVMsStatus(ctx context.Context, id string) ([]QueryClusterVMsStatusClustersClusterVmsVm, error) {
	resp, err := QueryClusterVMsStatus(ctx, c.c, id)
	if err != nil {
		return nil, err
	}

	if resp.Clusters == nil || len(resp.Clusters) == 0 {
		return nil, ErrorNotFound
	}

	return resp.Clusters[0].Vms, nil
}

func (c *client) DeleteUnusedHostPluginPackages(ctx context.Context, packageName string) error {
	resp1, err := QueryHostPluginPackagesByPackageName(ctx, c.c, packageName)
	if err != nil {
		return err
	}

	if len(resp1.HostPluginPackages) == 0 {
		return nil
	}

	resp2, err := QueryInstalledHostPluginPackagesByPackageName(ctx, c.c, packageName)
	if err != nil {
		return err
	}

	installedPackages := make([]string, 0)
	for _, plugin := range resp2.HostPlugins {
		installedPackages = append(installedPackages, plugin.Host_plugin_package.Id)
	}

	errors := make([]error, 0)

	for _, pkg := range resp1.HostPluginPackages {
		if StringSliceContains(installedPackages, pkg.Id) {
			continue
		}

		if _, err := DeleteHostPluginPackage(ctx, c.c, pkg.Id); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("delete unused host plugin packages failed: %v", errors)
	}

	return nil
}

func (c *client) GetTask(ctx context.Context, taskID string) (*QueryTaskByIdTasksTask, error) {
	resp, err := QueryTaskById(ctx, c.c, taskID)
	if err != nil {
		return nil, errors.Wrap(err, "query task failed")
	}

	if len(resp.Tasks) == 0 {
		return nil, nil
	}

	return &resp.Tasks[0], nil
}

func (c *client) UpdateClusterByAdminAPI(ctx context.Context, clusterID string) error {
	tasks := []string{
		// sync host info for update host version
		"UPSERT_HOST_DISK_NIC",
		// sync cluster info for update cluster version
		"UPDATE_CLUSTER",
	}

	for _, task := range tasks {
		var timeout []time.Duration
		if task == "UPSERT_HOST_DISK_NIC" {
			timeout = []time.Duration{towerSyncHostResourceTimeout}
		}

		if err := c.executeAdminTask(ctx, task, clusterID, timeout...); err != nil {
			log.Errorf("execute %s task by tower admin api failed: %v", task, err)
		}
	}

	log.Infof("update %s cluster info by tower admin api success", clusterID)

	return nil
}

func (c *client) executeAdminTask(ctx context.Context, key, clusterID string, timeout ...time.Duration) error {
	params := map[string]string{
		"key":       key,
		"clusterId": clusterID,
	}

	if len(timeout) > 0 {
		c.adminClient.SetTimeout(timeout[0])
		defer c.adminClient.SetTimeout(towerAdminAPITimeout)
	}

	resp, err := c.adminClient.R().SetContext(ctx).SetQueryParams(params).Get("execute-task")
	if err != nil {
		log.Errorf("execute %s task by tower admin api failed: %v", key, err)
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("[%d]: %s", resp.StatusCode(), resp.String())
	}

	log.Infof("execute %s task by tower admin api response: %s", key, resp.String())

	return nil
}

func (c *client) GetHostVMsByHostLocalID(ctx context.Context, id string) (*QueryHostVMsByHostLocalIDHost, error) {
	resp, err := QueryHostVMsByHostLocalID(ctx, c.c, id)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, ErrorNotFound
	}

	return &resp.Host, nil
}

func (c *client) GetSystemVMsByHostID(ctx context.Context, hostID string) ([]string, error) {
	resp, err := QuerySystemVMsByHostID(ctx, c.c, hostID)
	if err != nil {
		return nil, err
	}

	if len(resp.Hosts) == 0 {
		return nil, ErrorNotFound
	}

	vms := make([]string, 0)

	for _, vm := range resp.Hosts[0].Vms {
		if vm.Internal || len(vm.Vm_usage) != 0 {
			vms = append(vms, vm.Local_id)
		}
	}

	return vms, nil
}

func (c *client) QueryInRecycleBinVMsByClusterID(ctx context.Context, clusterID string) ([]string, error) {
	resp, err := QueryInRecycleBinVMsByClusterID(ctx, c.c, clusterID)
	if err != nil {
		return nil, err
	}

	if len(resp.Vms) == 0 {
		return nil, nil
	}

	vms := make([]string, 0, len(resp.Vms))

	for _, vm := range resp.Vms {
		vms = append(vms, vm.Local_id)
	}

	return vms, nil
}

func (c *client) QueryTaskStartedAt(ctx context.Context, taskID string) (time.Time, error) {
	resp, err := QueryTaskStartedAt(ctx, c.c, taskID)
	if err != nil {
		return time.Time{}, err
	}

	if len(resp.Tasks) == 0 {
		return time.Time{}, ErrorNotFound
	}

	return resp.Tasks[0].Started_at, nil
}

func (c *client) QueryUpgradeCenterTasks(ctx context.Context) ([]QueryUpgradeCenterTasksTasksTask, error) {
	resp, err := QueryUpgradeCenterTasks(ctx, c.C())
	if err != nil {
		return nil, err
	}

	return resp.Tasks, nil
}

func (c *client) GetHostInfoByID(ctx context.Context, id string) (*QueryHostInfoByIdHost, error) {
	resp, err := QueryHostInfoById(ctx, c.c, id)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.New("not found")
	}

	return &resp.Host, nil
}

func (c *client) GetHostInfoByLocalID(ctx context.Context, localID string) (*QueryHostInfoByLocalIdHost, error) {
	resp, err := QueryHostInfoByLocalId(ctx, c.PrismaC(), localID)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.New("not found")
	}

	return &resp.Host, nil
}

func (c *client) GetUsbDevicesByHostLocalId(ctx context.Context, localID string) ([]QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice, error) {
	resp, err := QueryUsbDevicesByHostLocalId(ctx, c.c, localID)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.New("not found")
	}

	return resp.UsbDevices, nil
}

func (c *client) GetPlacementGroupsByHostLocalID(ctx context.Context, localID string) ([]QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup, error) {
	resp, err := QueryPlacementGroupsByHostLocalID(ctx, c.c, localID)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.New("not found")
	}

	return resp.VmPlacementGroups, nil
}

func (c *client) GetClusterWitnessStatus(ctx context.Context, localID string) (*QueryClusterWitnessStatusCluster, error) {
	resp, err := QueryClusterWitnessStatus(ctx, c.c, localID)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.New("not found")
	}

	return &resp.Cluster, nil
}

func (c *client) DeleteVMByID(ctx context.Context, vmID string) error {
	_, err := DeleteVMByID(ctx, c.c, vmID)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) UpdateVMHost(ctx context.Context, vmID string, nodeIP string) error {
	_, err := UpdateVmHost(ctx, c.c, vmID, nodeIP)
	if err != nil {
		return err
	}

	return nil
}
