package tower

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/pkg/errors"

	"github.smartx.com/LCM/lcm-manager/third_party/tower/schema"
)

type MessageI18n map[string]string

func (m MessageI18n) String() string {
	data, err := json.Marshal(m)
	if err != nil {
		return ""
	}

	return string(data)
}

type TaskHandler interface {
	SetTaskID(id string)
	GetTaskID() string
	GetCreateTime() time.Time
	GetAuditLog() *schema.UserAuditLogCreateInput
	GetArgs(key string) any
	GetTask(ctx context.Context, id string) (*QueryTaskByIdTasksTask, error)
	CreateTask(ctx context.Context, input CreateTaskInput) error
	UpdateTask(ctx context.Context, input UpdateTaskInput) error
	PrepareAuditLog(input *AuditInput)
	SubmitAuditLog(ctx context.Context, input *AuditInput, succeed bool) error
	GetStep() int
	SetStep(step int)
}

type taskHandler struct {
	taskID     string
	client     Client
	step       int
	auditLog   *schema.UserAuditLogCreateInput
	createTime time.Time
	args       map[string]any
}

func NewTaskHandler(client Client, args map[string]any) TaskHandler {
	if args == nil {
		args = make(map[string]any)
	}

	return &taskHandler{
		client: client,
		args:   args,
	}
}

func (h *taskHandler) SetTaskID(id string) {
	h.taskID = id
}

func (h *taskHandler) GetTaskID() string {
	return h.taskID
}

func (h *taskHandler) GetCreateTime() time.Time {
	return h.createTime
}

func (h *taskHandler) GetAuditLog() *schema.UserAuditLogCreateInput {
	return h.auditLog
}

type CreateTaskInput struct {
	Internal         bool
	ClusterID        string
	Description      MessageI18n
	Status           TaskStatus
	Done             bool
	Now              time.Time
	Args             interface{}
	Steps            []*schema.Step
	Snapshot         string // it's useful for tower, e.g. `{"typename":"Host"}`
	ResourceType     string
	ResourceMutation string
}

func (h *taskHandler) GetArgs(key string) any {
	return h.args[key]
}

func (h *taskHandler) CreateTask(ctx context.Context, input CreateTaskInput) error {
	desc, err := json.Marshal(input.Description)
	if err != nil {
		return err
	}

	var now time.Time
	if input.Now.IsZero() {
		now = time.Now()
	} else {
		now = input.Now
	}

	h.createTime = now
	ts := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())

	in := h.fillCreateTaskInput(desc, input, ts)

	task, err := h.client.CreateTask(ctx, in)
	if err != nil {
		return err
	}

	h.taskID = task.Id

	log.Printf("Create tower task %s, description: %s", h.taskID, string(desc))

	return nil
}

func (h *taskHandler) fillCreateTaskInput(desc []byte, input CreateTaskInput, ts time.Time) schema.TaskCreateInput {
	in := schema.TaskCreateInput{
		Internal:       input.Internal,
		Description:    string(desc),
		Status:         string(TaskStatusExecuting),
		Args:           input.Args,
		Steps:          []*schema.Step{},
		Snapshot:       input.Snapshot,
		Progress:       0,
		LocalCreatedAt: &ts,
		StartedAt:      &ts,
	}

	if h.GetArgs("user_id") != "" {
		in.User = &schema.UserInput{Connect: &schema.Connect{ID: h.GetArgs("user_id").(string)}}
	}

	if input.ClusterID != "" {
		in.Cluster = &schema.ClusterInput{Connect: &schema.Connect{ID: input.ClusterID}}
	}

	if input.Status != "" {
		in.Status = string(input.Status)
	}

	if input.Done {
		in.FinishedAt = &ts
	}

	if len(input.Steps) > 0 {
		in.Steps = input.Steps
	}

	if input.ResourceMutation != "" {
		in.ResourceMutation = input.ResourceMutation
	}

	if input.ResourceType != "" {
		in.ResourceType = input.ResourceType
	}

	return in
}

type UpdateTaskInput struct {
	Description      MessageI18n
	Progress         float64
	Status           TaskStatus
	Args             interface{}
	Steps            []*schema.Step
	ErrorCode        string
	ErrorMessage     MessageI18n
	Done             bool
	ResourceMutation string
}

func (h *taskHandler) UpdateTask(ctx context.Context, input UpdateTaskInput) error {
	if h.taskID == "" {
		return errors.New("task id is empty")
	}

	_, err := h.client.UpdateTask(ctx, h.taskID, buildTaskUpdateInput(input))
	if err != nil {
		return err
	}

	log.Printf("Updated tower task %s, status: %s, progress: %f", h.taskID, string(input.Status), input.Progress)

	if h.auditLog != nil && (input.Status == TaskStatusFailed || input.Status == TaskStatusSuccessed) {
		if err := h.SubmitAuditLog(ctx, nil, input.Status == TaskStatusSuccessed); err != nil {
			return errors.Wrap(err, "submit audit log failed")
		}
	}

	return nil
}

type AuditInput struct {
	Code         string
	Message      MessageI18n
	ResourceID   string
	ResourceType string
	ClusterID    string
}

func (h *taskHandler) PrepareAuditLog(input *AuditInput) {
	if input == nil {
		return
	}

	h.auditLog = &schema.UserAuditLogCreateInput{
		Action:  input.Code,
		Message: input.Message.String(),
		User: &schema.ConnectTo{
			Connect: &schema.Connect{
				ID: h.GetArgs("user_id").(string),
			},
		},
		IPAddress: h.GetArgs("user_ip").(string),
	}

	if input.ClusterID != "" {
		h.auditLog.Cluster = &schema.ConnectTo{
			Connect: &schema.Connect{
				ID: input.ClusterID,
			},
		}
	}

	if input.ResourceID != "" {
		h.auditLog.ResourceID = input.ResourceID
	}

	if input.ResourceType != "" {
		h.auditLog.ResourceType = input.ResourceType
	}
}

func (h *taskHandler) SubmitAuditLog(ctx context.Context, input *AuditInput, succeed bool) error {
	if h.auditLog == nil && input == nil {
		return errors.New("audit log is nil")
	}

	if h.auditLog.User == nil || h.auditLog.User.Connect.ID == "" {
		return errors.New("audit log user id is empty")
	}

	if input != nil {
		updateAuditLog(h.auditLog, input)
	}

	if succeed {
		h.auditLog.Status = string(UserAuditLogStatusSuccessed)
	} else {
		h.auditLog.Status = string(UserAuditLogStatusFailed)
	}

	startedAt := time.Now()

	if h.taskID != "" {
		taskStartedAt, err := h.client.QueryTaskStartedAt(ctx, h.taskID)
		if err == nil {
			startedAt = taskStartedAt
		}
	}

	h.auditLog.StartedAt = startedAt
	h.auditLog.FinishedAt = time.Now()

	b, err := json.Marshal(h.auditLog)
	if err == nil {
		log.Printf("Commit user audit log: %s", string(b))
	}

	_, err = h.client.CreateUserAuditLog(ctx, *h.auditLog)
	h.auditLog = nil

	return err
}

func (h *taskHandler) GetStep() int {
	return h.step
}

func (h *taskHandler) SetStep(step int) {
	h.step = step
}

func (h *taskHandler) GetTask(ctx context.Context, id string) (*QueryTaskByIdTasksTask, error) {
	return h.client.GetTask(ctx, id)
}
