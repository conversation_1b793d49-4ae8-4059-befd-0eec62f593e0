# https://github.com/Khan/genqlient/blob/main/docs/genqlient.yaml
schema: schema.graphql
operations:
- genqlient.graphql
generated: generated.go
bindings:
  DateTime:
    type: time.Time
  Json:
    type: encoding/json.RawMessage
  HostPluginCreateInput:
    type: github.smartx.com/LCM/lcm-manager/third_party/tower/schema.HostPluginCreateInput
  Upload:
    type: '[]byte'
  UploadTaskCreateInput:
    type: github.smartx.com/LCM/lcm-manager/third_party/tower/schema.UploadTaskCreateInput
  TaskCreateInput:
    type: github.smartx.com/LCM/lcm-manager/third_party/tower/schema.TaskCreateInput
  TaskUpdateInput:
    type: github.smartx.com/LCM/lcm-manager/third_party/tower/schema.TaskUpdateInput
  UserAuditLogCreateInput:
    type: github.smartx.com/LCM/lcm-manager/third_party/tower/schema.UserAuditLogCreateInput
