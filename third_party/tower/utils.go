package tower

import (
	"encoding/json"
	"time"

	"github.smartx.com/LCM/lcm-manager/third_party/tower/schema"
)

func buildTaskUpdateInput(input UpdateTaskInput) schema.TaskUpdateInput {
	taskUpdateInput := schema.TaskUpdateInput{
		Status:           string(input.Status),
		Progress:         input.Progress,
		Args:             input.Args,
		ResourceMutation: input.ResourceMutation,
	}

	if input.ErrorCode != "" {
		taskUpdateInput.ErrorCode = input.ErrorCode
		// taskUpdateInput.ErrorMessage = i18n.NewErrorMessageByErrorCode(common.ErrorCode(input.ErrorCode))
		taskUpdateInput.ErrorMessage = input.ErrorCode
	}

	if input.Done {
		now := time.Now()
		ts := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
		taskUpdateInput.FinishedAt = &ts
	}

	if input.Description != nil {
		desc, err := json.Marshal(input.Description)
		if err == nil {
			taskUpdateInput.Description = string(desc)
		}
	}

	if len(input.Steps) > 0 {
		taskUpdateInput.Steps = input.Steps
	}

	return taskUpdateInput
}

func updateAuditLog(auditLog *schema.UserAuditLogCreateInput, input *AuditInput) {
	auditLog.Action = input.Code

	if input.Message != nil {
		msg, err := json.Marshal(input.Message)
		if err == nil {
			auditLog.Message = string(msg)
		}
	}

	if input.ResourceID != "" {
		auditLog.ResourceID = input.ResourceID
	}

	if input.ResourceType != "" {
		auditLog.ResourceType = input.ResourceType
	}
}

func StringSliceContains(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}
