// Code generated by github.com/Khan/genqlient, DO NOT EDIT.

package tower

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Khan/genqlient/graphql"
	"github.smartx.com/LCM/lcm-manager/third_party/tower/schema"
)

type ApplicationType string

const (
	ApplicationTypeMonitor ApplicationType = "MONITOR"
)

type Architecture string

const (
	ArchitectureAarch64 Architecture = "AARCH64"
	ArchitectureX8664   Architecture = "X86_64"
)

type ClusterType string

const (
	ClusterTypeBlueshark ClusterType = "BLUESHARK"
	ClusterTypeSmtxElf   ClusterType = "SMTX_ELF"
	ClusterTypeSmtxOs    ClusterType = "SMTX_OS"
	ClusterTypeSmtxZbs   ClusterType = "SMTX_ZBS"
)

type ConnectState string

const (
	ConnectStateConnected    ConnectState = "CONNECTED"
	ConnectStateDisconnected ConnectState = "DISCONNECTED"
	ConnectStateInitializing ConnectState = "INITIALIZING"
	ConnectStateRemoving     ConnectState = "REMOVING"
)

// CreateHostPluginCreateHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type CreateHostPluginCreateHostPlugin struct {
	Id string `json:"id"`
}

// GetId returns CreateHostPluginCreateHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *CreateHostPluginCreateHostPlugin) GetId() string { return v.Id }

// CreateHostPluginResponse is returned by CreateHostPlugin on success.
type CreateHostPluginResponse struct {
	CreateHostPlugin CreateHostPluginCreateHostPlugin `json:"createHostPlugin"`
}

// GetCreateHostPlugin returns CreateHostPluginResponse.CreateHostPlugin, and is useful for accessing the field via an interface.
func (v *CreateHostPluginResponse) GetCreateHostPlugin() CreateHostPluginCreateHostPlugin {
	return v.CreateHostPlugin
}

// CreateTaskCreateTask includes the requested fields of the GraphQL type Task.
type CreateTaskCreateTask struct {
	Id     string     `json:"id"`
	Status TaskStatus `json:"status"`
}

// GetId returns CreateTaskCreateTask.Id, and is useful for accessing the field via an interface.
func (v *CreateTaskCreateTask) GetId() string { return v.Id }

// GetStatus returns CreateTaskCreateTask.Status, and is useful for accessing the field via an interface.
func (v *CreateTaskCreateTask) GetStatus() TaskStatus { return v.Status }

// CreateTaskResponse is returned by CreateTask on success.
type CreateTaskResponse struct {
	CreateTask CreateTaskCreateTask `json:"createTask"`
}

// GetCreateTask returns CreateTaskResponse.CreateTask, and is useful for accessing the field via an interface.
func (v *CreateTaskResponse) GetCreateTask() CreateTaskCreateTask { return v.CreateTask }

// CreateUserAuditLogCreateUserAuditLog includes the requested fields of the GraphQL type UserAuditLog.
type CreateUserAuditLogCreateUserAuditLog struct {
	Id     string             `json:"id"`
	Status UserAuditLogStatus `json:"status"`
}

// GetId returns CreateUserAuditLogCreateUserAuditLog.Id, and is useful for accessing the field via an interface.
func (v *CreateUserAuditLogCreateUserAuditLog) GetId() string { return v.Id }

// GetStatus returns CreateUserAuditLogCreateUserAuditLog.Status, and is useful for accessing the field via an interface.
func (v *CreateUserAuditLogCreateUserAuditLog) GetStatus() UserAuditLogStatus { return v.Status }

// CreateUserAuditLogResponse is returned by CreateUserAuditLog on success.
type CreateUserAuditLogResponse struct {
	CreateUserAuditLog CreateUserAuditLogCreateUserAuditLog `json:"createUserAuditLog"`
}

// GetCreateUserAuditLog returns CreateUserAuditLogResponse.CreateUserAuditLog, and is useful for accessing the field via an interface.
func (v *CreateUserAuditLogResponse) GetCreateUserAuditLog() CreateUserAuditLogCreateUserAuditLog {
	return v.CreateUserAuditLog
}

// DeleteHostPluginDeleteHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type DeleteHostPluginDeleteHostPlugin struct {
	Id string `json:"id"`
}

// GetId returns DeleteHostPluginDeleteHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *DeleteHostPluginDeleteHostPlugin) GetId() string { return v.Id }

// DeleteHostPluginPackageDeleteHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type DeleteHostPluginPackageDeleteHostPluginPackage struct {
	Id string `json:"id"`
}

// GetId returns DeleteHostPluginPackageDeleteHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *DeleteHostPluginPackageDeleteHostPluginPackage) GetId() string { return v.Id }

// DeleteHostPluginPackageResponse is returned by DeleteHostPluginPackage on success.
type DeleteHostPluginPackageResponse struct {
	DeleteHostPluginPackage DeleteHostPluginPackageDeleteHostPluginPackage `json:"deleteHostPluginPackage"`
}

// GetDeleteHostPluginPackage returns DeleteHostPluginPackageResponse.DeleteHostPluginPackage, and is useful for accessing the field via an interface.
func (v *DeleteHostPluginPackageResponse) GetDeleteHostPluginPackage() DeleteHostPluginPackageDeleteHostPluginPackage {
	return v.DeleteHostPluginPackage
}

// DeleteHostPluginResponse is returned by DeleteHostPlugin on success.
type DeleteHostPluginResponse struct {
	DeleteHostPlugin DeleteHostPluginDeleteHostPlugin `json:"deleteHostPlugin"`
}

// GetDeleteHostPlugin returns DeleteHostPluginResponse.DeleteHostPlugin, and is useful for accessing the field via an interface.
func (v *DeleteHostPluginResponse) GetDeleteHostPlugin() DeleteHostPluginDeleteHostPlugin {
	return v.DeleteHostPlugin
}

// DeleteVMByIDDeleteVm includes the requested fields of the GraphQL type Vm.
type DeleteVMByIDDeleteVm struct {
	Id             string `json:"id"`
	Name           string `json:"name"`
	In_recycle_bin bool   `json:"in_recycle_bin"`
}

// GetId returns DeleteVMByIDDeleteVm.Id, and is useful for accessing the field via an interface.
func (v *DeleteVMByIDDeleteVm) GetId() string { return v.Id }

// GetName returns DeleteVMByIDDeleteVm.Name, and is useful for accessing the field via an interface.
func (v *DeleteVMByIDDeleteVm) GetName() string { return v.Name }

// GetIn_recycle_bin returns DeleteVMByIDDeleteVm.In_recycle_bin, and is useful for accessing the field via an interface.
func (v *DeleteVMByIDDeleteVm) GetIn_recycle_bin() bool { return v.In_recycle_bin }

// DeleteVMByIDResponse is returned by DeleteVMByID on success.
type DeleteVMByIDResponse struct {
	DeleteVm DeleteVMByIDDeleteVm `json:"deleteVm"`
}

// GetDeleteVm returns DeleteVMByIDResponse.DeleteVm, and is useful for accessing the field via an interface.
func (v *DeleteVMByIDResponse) GetDeleteVm() DeleteVMByIDDeleteVm { return v.DeleteVm }

type GpuDeviceUsage string

const (
	GpuDeviceUsagePassThrough GpuDeviceUsage = "PASS_THROUGH"
	GpuDeviceUsageVgpu        GpuDeviceUsage = "VGPU"
)

type HostConnectStatus string

const (
	HostConnectStatusConnected    HostConnectStatus = "CONNECTED"
	HostConnectStatusDisconnected HostConnectStatus = "DISCONNECTED"
)

type HostMergedStatus string

const (
	HostMergedStatusDisconnected HostMergedStatus = "DISCONNECTED"
	HostMergedStatusHealthy      HostMergedStatus = "HEALTHY"
	HostMergedStatusInitializing HostMergedStatus = "INITIALIZING"
	HostMergedStatusUnusual      HostMergedStatus = "UNUSUAL"
)

type HostState string

const (
	HostStateIdle     HostState = "IDLE"
	HostStateInUse    HostState = "IN_USE"
	HostStateRemoving HostState = "REMOVING"
)

type HostStatus string

const (
	HostStatusConnectedError   HostStatus = "CONNECTED_ERROR"
	HostStatusConnectedHealthy HostStatus = "CONNECTED_HEALTHY"
	HostStatusConnectedWarning HostStatus = "CONNECTED_WARNING"
	HostStatusConnecting       HostStatus = "CONNECTING"
	HostStatusInitializing     HostStatus = "INITIALIZING"
	HostStatusSessionExpired   HostStatus = "SESSION_EXPIRED"
)

type Hypervisor string

const (
	HypervisorBlueshark Hypervisor = "BLUESHARK"
	HypervisorElf       Hypervisor = "ELF"
	HypervisorVmware    Hypervisor = "VMWARE"
	HypervisorXenserver Hypervisor = "XENSERVER"
)

type LoginInput struct {
	Auth_config_id string     `json:"auth_config_id"`
	Mfa_type       MfaType    `json:"mfa_type"`
	Password       string     `json:"password"`
	Source         UserSource `json:"source"`
	Username       string     `json:"username"`
}

// GetAuth_config_id returns LoginInput.Auth_config_id, and is useful for accessing the field via an interface.
func (v *LoginInput) GetAuth_config_id() string { return v.Auth_config_id }

// GetMfa_type returns LoginInput.Mfa_type, and is useful for accessing the field via an interface.
func (v *LoginInput) GetMfa_type() MfaType { return v.Mfa_type }

// GetPassword returns LoginInput.Password, and is useful for accessing the field via an interface.
func (v *LoginInput) GetPassword() string { return v.Password }

// GetSource returns LoginInput.Source, and is useful for accessing the field via an interface.
func (v *LoginInput) GetSource() UserSource { return v.Source }

// GetUsername returns LoginInput.Username, and is useful for accessing the field via an interface.
func (v *LoginInput) GetUsername() string { return v.Username }

// LoginLogin includes the requested fields of the GraphQL type Login.
type LoginLogin struct {
	Token string `json:"token"`
}

// GetToken returns LoginLogin.Token, and is useful for accessing the field via an interface.
func (v *LoginLogin) GetToken() string { return v.Token }

// LoginResponse is returned by Login on success.
type LoginResponse struct {
	Login LoginLogin `json:"login"`
}

// GetLogin returns LoginResponse.Login, and is useful for accessing the field via an interface.
func (v *LoginResponse) GetLogin() LoginLogin { return v.Login }

type MaintenanceModeEnum string

const (
	MaintenanceModeEnumEnteringMaintenanceMode MaintenanceModeEnum = "ENTERING_MAINTENANCE_MODE"
	MaintenanceModeEnumInUse                   MaintenanceModeEnum = "IN_USE"
	MaintenanceModeEnumMaintenanceMode         MaintenanceModeEnum = "MAINTENANCE_MODE"
	MaintenanceModeEnumRemoving                MaintenanceModeEnum = "REMOVING"
)

type MetroCheckStatusEnum string

const (
	MetroCheckStatusEnumCritical MetroCheckStatusEnum = "CRITICAL"
	MetroCheckStatusEnumHealthy  MetroCheckStatusEnum = "HEALTHY"
	MetroCheckStatusEnumInfo     MetroCheckStatusEnum = "INFO"
	MetroCheckStatusEnumNotice   MetroCheckStatusEnum = "NOTICE"
)

type MfaType string

const (
	MfaTypeMail MfaType = "Mail"
)

// QueryCloudTowerVersionDeploysDeploy includes the requested fields of the GraphQL type Deploy.
type QueryCloudTowerVersionDeploysDeploy struct {
	Version string `json:"version"`
}

// GetVersion returns QueryCloudTowerVersionDeploysDeploy.Version, and is useful for accessing the field via an interface.
func (v *QueryCloudTowerVersionDeploysDeploy) GetVersion() string { return v.Version }

// QueryCloudTowerVersionResponse is returned by QueryCloudTowerVersion on success.
type QueryCloudTowerVersionResponse struct {
	Deploys []QueryCloudTowerVersionDeploysDeploy `json:"deploys"`
}

// GetDeploys returns QueryCloudTowerVersionResponse.Deploys, and is useful for accessing the field via an interface.
func (v *QueryCloudTowerVersionResponse) GetDeploys() []QueryCloudTowerVersionDeploysDeploy {
	return v.Deploys
}

// QueryClusterInfoByIdCluster includes the requested fields of the GraphQL type Cluster.
type QueryClusterInfoByIdCluster struct {
	Name                 string                                 `json:"name"`
	Id                   string                                 `json:"id"`
	Local_id             string                                 `json:"local_id"`
	Architecture         Architecture                           `json:"architecture"`
	Cpu_vendor           string                                 `json:"cpu_vendor"`
	Ip                   string                                 `json:"ip"`
	Management_vip       string                                 `json:"management_vip"`
	Connect_state        ConnectState                           `json:"connect_state"`
	Type                 ClusterType                            `json:"type"`
	Hypervisor           Hypervisor                             `json:"hypervisor"`
	Version              string                                 `json:"version"`
	Patch_version        string                                 `json:"patch_version"`
	Upgrade_tool_version string                                 `json:"upgrade_tool_version"`
	Hosts                []QueryClusterInfoByIdClusterHostsHost `json:"hosts"`
	Witness              QueryClusterInfoByIdClusterWitness     `json:"witness"`
}

// GetName returns QueryClusterInfoByIdCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetName() string { return v.Name }

// GetId returns QueryClusterInfoByIdCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetId() string { return v.Id }

// GetLocal_id returns QueryClusterInfoByIdCluster.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetLocal_id() string { return v.Local_id }

// GetArchitecture returns QueryClusterInfoByIdCluster.Architecture, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetArchitecture() Architecture { return v.Architecture }

// GetCpu_vendor returns QueryClusterInfoByIdCluster.Cpu_vendor, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetCpu_vendor() string { return v.Cpu_vendor }

// GetIp returns QueryClusterInfoByIdCluster.Ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetIp() string { return v.Ip }

// GetManagement_vip returns QueryClusterInfoByIdCluster.Management_vip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetManagement_vip() string { return v.Management_vip }

// GetConnect_state returns QueryClusterInfoByIdCluster.Connect_state, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetConnect_state() ConnectState { return v.Connect_state }

// GetType returns QueryClusterInfoByIdCluster.Type, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetType() ClusterType { return v.Type }

// GetHypervisor returns QueryClusterInfoByIdCluster.Hypervisor, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetHypervisor() Hypervisor { return v.Hypervisor }

// GetVersion returns QueryClusterInfoByIdCluster.Version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetVersion() string { return v.Version }

// GetPatch_version returns QueryClusterInfoByIdCluster.Patch_version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetPatch_version() string { return v.Patch_version }

// GetUpgrade_tool_version returns QueryClusterInfoByIdCluster.Upgrade_tool_version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetUpgrade_tool_version() string { return v.Upgrade_tool_version }

// GetHosts returns QueryClusterInfoByIdCluster.Hosts, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetHosts() []QueryClusterInfoByIdClusterHostsHost {
	return v.Hosts
}

// GetWitness returns QueryClusterInfoByIdCluster.Witness, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdCluster) GetWitness() QueryClusterInfoByIdClusterWitness {
	return v.Witness
}

// QueryClusterInfoByIdClusterHostsHost includes the requested fields of the GraphQL type Host.
type QueryClusterInfoByIdClusterHostsHost struct {
	Id             string                                                     `json:"id"`
	Name           string                                                     `json:"name"`
	Scvm_name      string                                                     `json:"scvm_name"`
	Local_id       string                                                     `json:"local_id"`
	Data_ip        string                                                     `json:"data_ip"`
	Management_ip  string                                                     `json:"management_ip"`
	State          HostState                                                  `json:"state"`
	Connect_status HostConnectStatus                                          `json:"connect_status"`
	Merged_status  HostMergedStatus                                           `json:"merged_status"`
	Os_version     string                                                     `json:"os_version"`
	Role           string                                                     `json:"role"`
	Chunk_id       string                                                     `json:"chunk_id"`
	Gpu_devices    []QueryClusterInfoByIdClusterHostsHostGpu_devicesGpuDevice `json:"gpu_devices"`
}

// GetId returns QueryClusterInfoByIdClusterHostsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetId() string { return v.Id }

// GetName returns QueryClusterInfoByIdClusterHostsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetName() string { return v.Name }

// GetScvm_name returns QueryClusterInfoByIdClusterHostsHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetScvm_name() string { return v.Scvm_name }

// GetLocal_id returns QueryClusterInfoByIdClusterHostsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClusterInfoByIdClusterHostsHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClusterInfoByIdClusterHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetManagement_ip() string { return v.Management_ip }

// GetState returns QueryClusterInfoByIdClusterHostsHost.State, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetState() HostState { return v.State }

// GetConnect_status returns QueryClusterInfoByIdClusterHostsHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetConnect_status() HostConnectStatus {
	return v.Connect_status
}

// GetMerged_status returns QueryClusterInfoByIdClusterHostsHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetMerged_status() HostMergedStatus {
	return v.Merged_status
}

// GetOs_version returns QueryClusterInfoByIdClusterHostsHost.Os_version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetOs_version() string { return v.Os_version }

// GetRole returns QueryClusterInfoByIdClusterHostsHost.Role, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetRole() string { return v.Role }

// GetChunk_id returns QueryClusterInfoByIdClusterHostsHost.Chunk_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetChunk_id() string { return v.Chunk_id }

// GetGpu_devices returns QueryClusterInfoByIdClusterHostsHost.Gpu_devices, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHost) GetGpu_devices() []QueryClusterInfoByIdClusterHostsHostGpu_devicesGpuDevice {
	return v.Gpu_devices
}

// QueryClusterInfoByIdClusterHostsHostGpu_devicesGpuDevice includes the requested fields of the GraphQL type GpuDevice.
type QueryClusterInfoByIdClusterHostsHostGpu_devicesGpuDevice struct {
	User_usage GpuDeviceUsage `json:"user_usage"`
}

// GetUser_usage returns QueryClusterInfoByIdClusterHostsHostGpu_devicesGpuDevice.User_usage, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterHostsHostGpu_devicesGpuDevice) GetUser_usage() GpuDeviceUsage {
	return v.User_usage
}

// QueryClusterInfoByIdClusterWitness includes the requested fields of the GraphQL type Witness.
type QueryClusterInfoByIdClusterWitness struct {
	Id            string `json:"id"`
	Name          string `json:"name"`
	Local_id      string `json:"local_id"`
	Data_ip       string `json:"data_ip"`
	Management_ip string `json:"management_ip"`
}

// GetId returns QueryClusterInfoByIdClusterWitness.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterWitness) GetId() string { return v.Id }

// GetName returns QueryClusterInfoByIdClusterWitness.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterWitness) GetName() string { return v.Name }

// GetLocal_id returns QueryClusterInfoByIdClusterWitness.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterWitness) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClusterInfoByIdClusterWitness.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterWitness) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClusterInfoByIdClusterWitness.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdClusterWitness) GetManagement_ip() string { return v.Management_ip }

// QueryClusterInfoByIdResponse is returned by QueryClusterInfoById on success.
type QueryClusterInfoByIdResponse struct {
	Cluster QueryClusterInfoByIdCluster `json:"cluster"`
}

// GetCluster returns QueryClusterInfoByIdResponse.Cluster, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByIdResponse) GetCluster() QueryClusterInfoByIdCluster { return v.Cluster }

// QueryClusterInfoByLocalIdCluster includes the requested fields of the GraphQL type Cluster.
type QueryClusterInfoByLocalIdCluster struct {
	Name                 string                                      `json:"name"`
	Id                   string                                      `json:"id"`
	Local_id             string                                      `json:"local_id"`
	Architecture         Architecture                                `json:"architecture"`
	Cpu_vendor           string                                      `json:"cpu_vendor"`
	Ip                   string                                      `json:"ip"`
	Management_vip       string                                      `json:"management_vip"`
	Connect_state        ConnectState                                `json:"connect_state"`
	Type                 ClusterType                                 `json:"type"`
	Hypervisor           Hypervisor                                  `json:"hypervisor"`
	Version              string                                      `json:"version"`
	Patch_version        string                                      `json:"patch_version"`
	Upgrade_tool_version string                                      `json:"upgrade_tool_version"`
	Stretch              bool                                        `json:"stretch"`
	Hosts                []QueryClusterInfoByLocalIdClusterHostsHost `json:"hosts"`
	Witness              QueryClusterInfoByLocalIdClusterWitness     `json:"witness"`
	Ntp_servers          []string                                    `json:"ntp_servers"`
}

// GetName returns QueryClusterInfoByLocalIdCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetName() string { return v.Name }

// GetId returns QueryClusterInfoByLocalIdCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetId() string { return v.Id }

// GetLocal_id returns QueryClusterInfoByLocalIdCluster.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetLocal_id() string { return v.Local_id }

// GetArchitecture returns QueryClusterInfoByLocalIdCluster.Architecture, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetArchitecture() Architecture { return v.Architecture }

// GetCpu_vendor returns QueryClusterInfoByLocalIdCluster.Cpu_vendor, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetCpu_vendor() string { return v.Cpu_vendor }

// GetIp returns QueryClusterInfoByLocalIdCluster.Ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetIp() string { return v.Ip }

// GetManagement_vip returns QueryClusterInfoByLocalIdCluster.Management_vip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetManagement_vip() string { return v.Management_vip }

// GetConnect_state returns QueryClusterInfoByLocalIdCluster.Connect_state, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetConnect_state() ConnectState { return v.Connect_state }

// GetType returns QueryClusterInfoByLocalIdCluster.Type, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetType() ClusterType { return v.Type }

// GetHypervisor returns QueryClusterInfoByLocalIdCluster.Hypervisor, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetHypervisor() Hypervisor { return v.Hypervisor }

// GetVersion returns QueryClusterInfoByLocalIdCluster.Version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetVersion() string { return v.Version }

// GetPatch_version returns QueryClusterInfoByLocalIdCluster.Patch_version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetPatch_version() string { return v.Patch_version }

// GetUpgrade_tool_version returns QueryClusterInfoByLocalIdCluster.Upgrade_tool_version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetUpgrade_tool_version() string {
	return v.Upgrade_tool_version
}

// GetStretch returns QueryClusterInfoByLocalIdCluster.Stretch, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetStretch() bool { return v.Stretch }

// GetHosts returns QueryClusterInfoByLocalIdCluster.Hosts, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetHosts() []QueryClusterInfoByLocalIdClusterHostsHost {
	return v.Hosts
}

// GetWitness returns QueryClusterInfoByLocalIdCluster.Witness, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetWitness() QueryClusterInfoByLocalIdClusterWitness {
	return v.Witness
}

// GetNtp_servers returns QueryClusterInfoByLocalIdCluster.Ntp_servers, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdCluster) GetNtp_servers() []string { return v.Ntp_servers }

// QueryClusterInfoByLocalIdClusterHostsHost includes the requested fields of the GraphQL type Host.
type QueryClusterInfoByLocalIdClusterHostsHost struct {
	Id             string                                                          `json:"id"`
	Name           string                                                          `json:"name"`
	Scvm_name      string                                                          `json:"scvm_name"`
	Local_id       string                                                          `json:"local_id"`
	Data_ip        string                                                          `json:"data_ip"`
	Management_ip  string                                                          `json:"management_ip"`
	Connect_status HostConnectStatus                                               `json:"connect_status"`
	Merged_status  HostMergedStatus                                                `json:"merged_status"`
	Os_version     string                                                          `json:"os_version"`
	Role           string                                                          `json:"role"`
	Zone           QueryClusterInfoByLocalIdClusterHostsHostZone                   `json:"zone"`
	Chunk_id       string                                                          `json:"chunk_id"`
	State          HostState                                                       `json:"state"`
	Status         HostStatus                                                      `json:"status"`
	Gpu_devices    []QueryClusterInfoByLocalIdClusterHostsHostGpu_devicesGpuDevice `json:"gpu_devices"`
}

// GetId returns QueryClusterInfoByLocalIdClusterHostsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetId() string { return v.Id }

// GetName returns QueryClusterInfoByLocalIdClusterHostsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetName() string { return v.Name }

// GetScvm_name returns QueryClusterInfoByLocalIdClusterHostsHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetScvm_name() string { return v.Scvm_name }

// GetLocal_id returns QueryClusterInfoByLocalIdClusterHostsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClusterInfoByLocalIdClusterHostsHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClusterInfoByLocalIdClusterHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetManagement_ip() string { return v.Management_ip }

// GetConnect_status returns QueryClusterInfoByLocalIdClusterHostsHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetConnect_status() HostConnectStatus {
	return v.Connect_status
}

// GetMerged_status returns QueryClusterInfoByLocalIdClusterHostsHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetMerged_status() HostMergedStatus {
	return v.Merged_status
}

// GetOs_version returns QueryClusterInfoByLocalIdClusterHostsHost.Os_version, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetOs_version() string { return v.Os_version }

// GetRole returns QueryClusterInfoByLocalIdClusterHostsHost.Role, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetRole() string { return v.Role }

// GetZone returns QueryClusterInfoByLocalIdClusterHostsHost.Zone, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetZone() QueryClusterInfoByLocalIdClusterHostsHostZone {
	return v.Zone
}

// GetChunk_id returns QueryClusterInfoByLocalIdClusterHostsHost.Chunk_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetChunk_id() string { return v.Chunk_id }

// GetState returns QueryClusterInfoByLocalIdClusterHostsHost.State, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetState() HostState { return v.State }

// GetStatus returns QueryClusterInfoByLocalIdClusterHostsHost.Status, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetStatus() HostStatus { return v.Status }

// GetGpu_devices returns QueryClusterInfoByLocalIdClusterHostsHost.Gpu_devices, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHost) GetGpu_devices() []QueryClusterInfoByLocalIdClusterHostsHostGpu_devicesGpuDevice {
	return v.Gpu_devices
}

// QueryClusterInfoByLocalIdClusterHostsHostGpu_devicesGpuDevice includes the requested fields of the GraphQL type GpuDevice.
type QueryClusterInfoByLocalIdClusterHostsHostGpu_devicesGpuDevice struct {
	User_usage GpuDeviceUsage `json:"user_usage"`
}

// GetUser_usage returns QueryClusterInfoByLocalIdClusterHostsHostGpu_devicesGpuDevice.User_usage, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHostGpu_devicesGpuDevice) GetUser_usage() GpuDeviceUsage {
	return v.User_usage
}

// QueryClusterInfoByLocalIdClusterHostsHostZone includes the requested fields of the GraphQL type Zone.
type QueryClusterInfoByLocalIdClusterHostsHostZone struct {
	Is_preferred bool `json:"is_preferred"`
}

// GetIs_preferred returns QueryClusterInfoByLocalIdClusterHostsHostZone.Is_preferred, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterHostsHostZone) GetIs_preferred() bool { return v.Is_preferred }

// QueryClusterInfoByLocalIdClusterWitness includes the requested fields of the GraphQL type Witness.
type QueryClusterInfoByLocalIdClusterWitness struct {
	Id            string `json:"id"`
	Name          string `json:"name"`
	Local_id      string `json:"local_id"`
	Data_ip       string `json:"data_ip"`
	Management_ip string `json:"management_ip"`
}

// GetId returns QueryClusterInfoByLocalIdClusterWitness.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterWitness) GetId() string { return v.Id }

// GetName returns QueryClusterInfoByLocalIdClusterWitness.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterWitness) GetName() string { return v.Name }

// GetLocal_id returns QueryClusterInfoByLocalIdClusterWitness.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterWitness) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClusterInfoByLocalIdClusterWitness.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterWitness) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClusterInfoByLocalIdClusterWitness.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdClusterWitness) GetManagement_ip() string { return v.Management_ip }

// QueryClusterInfoByLocalIdResponse is returned by QueryClusterInfoByLocalId on success.
type QueryClusterInfoByLocalIdResponse struct {
	Cluster QueryClusterInfoByLocalIdCluster `json:"cluster"`
}

// GetCluster returns QueryClusterInfoByLocalIdResponse.Cluster, and is useful for accessing the field via an interface.
func (v *QueryClusterInfoByLocalIdResponse) GetCluster() QueryClusterInfoByLocalIdCluster {
	return v.Cluster
}

// QueryClusterVMsStatusClustersCluster includes the requested fields of the GraphQL type Cluster.
type QueryClusterVMsStatusClustersCluster struct {
	Id  string                                      `json:"id"`
	Vms []QueryClusterVMsStatusClustersClusterVmsVm `json:"vms"`
}

// GetId returns QueryClusterVMsStatusClustersCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersCluster) GetId() string { return v.Id }

// GetVms returns QueryClusterVMsStatusClustersCluster.Vms, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersCluster) GetVms() []QueryClusterVMsStatusClustersClusterVmsVm {
	return v.Vms
}

// QueryClusterVMsStatusClustersClusterVmsVm includes the requested fields of the GraphQL type Vm.
type QueryClusterVMsStatusClustersClusterVmsVm struct {
	Id     string                                        `json:"id"`
	Name   string                                        `json:"name"`
	Status VmStatus                                      `json:"status"`
	Host   QueryClusterVMsStatusClustersClusterVmsVmHost `json:"host"`
}

// GetId returns QueryClusterVMsStatusClustersClusterVmsVm.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersClusterVmsVm) GetId() string { return v.Id }

// GetName returns QueryClusterVMsStatusClustersClusterVmsVm.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersClusterVmsVm) GetName() string { return v.Name }

// GetStatus returns QueryClusterVMsStatusClustersClusterVmsVm.Status, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersClusterVmsVm) GetStatus() VmStatus { return v.Status }

// GetHost returns QueryClusterVMsStatusClustersClusterVmsVm.Host, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersClusterVmsVm) GetHost() QueryClusterVMsStatusClustersClusterVmsVmHost {
	return v.Host
}

// QueryClusterVMsStatusClustersClusterVmsVmHost includes the requested fields of the GraphQL type Host.
type QueryClusterVMsStatusClustersClusterVmsVmHost struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

// GetId returns QueryClusterVMsStatusClustersClusterVmsVmHost.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersClusterVmsVmHost) GetId() string { return v.Id }

// GetName returns QueryClusterVMsStatusClustersClusterVmsVmHost.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusClustersClusterVmsVmHost) GetName() string { return v.Name }

// QueryClusterVMsStatusResponse is returned by QueryClusterVMsStatus on success.
type QueryClusterVMsStatusResponse struct {
	Clusters []QueryClusterVMsStatusClustersCluster `json:"clusters"`
}

// GetClusters returns QueryClusterVMsStatusResponse.Clusters, and is useful for accessing the field via an interface.
func (v *QueryClusterVMsStatusResponse) GetClusters() []QueryClusterVMsStatusClustersCluster {
	return v.Clusters
}

// QueryClusterWitnessStatusCluster includes the requested fields of the GraphQL type Cluster.
type QueryClusterWitnessStatusCluster struct {
	Id                           string                                                                                 `json:"id"`
	Local_id                     string                                                                                 `json:"local_id"`
	Name                         string                                                                                 `json:"name"`
	Stretch                      bool                                                                                   `json:"stretch"`
	Witness                      QueryClusterWitnessStatusClusterWitness                                                `json:"witness"`
	Metro_availability_checklist QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklist `json:"metro_availability_checklist"`
}

// GetId returns QueryClusterWitnessStatusCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusCluster) GetId() string { return v.Id }

// GetLocal_id returns QueryClusterWitnessStatusCluster.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusCluster) GetLocal_id() string { return v.Local_id }

// GetName returns QueryClusterWitnessStatusCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusCluster) GetName() string { return v.Name }

// GetStretch returns QueryClusterWitnessStatusCluster.Stretch, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusCluster) GetStretch() bool { return v.Stretch }

// GetWitness returns QueryClusterWitnessStatusCluster.Witness, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusCluster) GetWitness() QueryClusterWitnessStatusClusterWitness {
	return v.Witness
}

// GetMetro_availability_checklist returns QueryClusterWitnessStatusCluster.Metro_availability_checklist, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusCluster) GetMetro_availability_checklist() QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklist {
	return v.Metro_availability_checklist
}

// QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklist includes the requested fields of the GraphQL type MetroAvailabilityChecklist.
type QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklist struct {
	Witness QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklistWitnessMetroCheckResult `json:"witness"`
}

// GetWitness returns QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklist.Witness, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklist) GetWitness() QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklistWitnessMetroCheckResult {
	return v.Witness
}

// QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklistWitnessMetroCheckResult includes the requested fields of the GraphQL type MetroCheckResult.
type QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklistWitnessMetroCheckResult struct {
	Status MetroCheckStatusEnum `json:"status"`
}

// GetStatus returns QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklistWitnessMetroCheckResult.Status, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterMetro_availability_checklistMetroAvailabilityChecklistWitnessMetroCheckResult) GetStatus() MetroCheckStatusEnum {
	return v.Status
}

// QueryClusterWitnessStatusClusterWitness includes the requested fields of the GraphQL type Witness.
type QueryClusterWitnessStatusClusterWitness struct {
	Id            string `json:"id"`
	Name          string `json:"name"`
	Local_id      string `json:"local_id"`
	Data_ip       string `json:"data_ip"`
	Management_ip string `json:"management_ip"`
}

// GetId returns QueryClusterWitnessStatusClusterWitness.Id, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterWitness) GetId() string { return v.Id }

// GetName returns QueryClusterWitnessStatusClusterWitness.Name, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterWitness) GetName() string { return v.Name }

// GetLocal_id returns QueryClusterWitnessStatusClusterWitness.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterWitness) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClusterWitnessStatusClusterWitness.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterWitness) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClusterWitnessStatusClusterWitness.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusClusterWitness) GetManagement_ip() string { return v.Management_ip }

// QueryClusterWitnessStatusResponse is returned by QueryClusterWitnessStatus on success.
type QueryClusterWitnessStatusResponse struct {
	Cluster QueryClusterWitnessStatusCluster `json:"cluster"`
}

// GetCluster returns QueryClusterWitnessStatusResponse.Cluster, and is useful for accessing the field via an interface.
func (v *QueryClusterWitnessStatusResponse) GetCluster() QueryClusterWitnessStatusCluster {
	return v.Cluster
}

// QueryClustersByIdsClustersCluster includes the requested fields of the GraphQL type Cluster.
type QueryClustersByIdsClustersCluster struct {
	Id           string                                                     `json:"id"`
	Local_id     string                                                     `json:"local_id"`
	Ip           string                                                     `json:"ip"`
	Architecture Architecture                                               `json:"architecture"`
	Version      string                                                     `json:"version"`
	Name         string                                                     `json:"name"`
	Hosts        []QueryClustersByIdsClustersClusterHostsHost               `json:"hosts"`
	Applications []QueryClustersByIdsClustersClusterApplicationsApplication `json:"applications"`
}

// GetId returns QueryClustersByIdsClustersCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetId() string { return v.Id }

// GetLocal_id returns QueryClustersByIdsClustersCluster.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetLocal_id() string { return v.Local_id }

// GetIp returns QueryClustersByIdsClustersCluster.Ip, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetIp() string { return v.Ip }

// GetArchitecture returns QueryClustersByIdsClustersCluster.Architecture, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetArchitecture() Architecture { return v.Architecture }

// GetVersion returns QueryClustersByIdsClustersCluster.Version, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetVersion() string { return v.Version }

// GetName returns QueryClustersByIdsClustersCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetName() string { return v.Name }

// GetHosts returns QueryClustersByIdsClustersCluster.Hosts, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetHosts() []QueryClustersByIdsClustersClusterHostsHost {
	return v.Hosts
}

// GetApplications returns QueryClustersByIdsClustersCluster.Applications, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersCluster) GetApplications() []QueryClustersByIdsClustersClusterApplicationsApplication {
	return v.Applications
}

// QueryClustersByIdsClustersClusterApplicationsApplication includes the requested fields of the GraphQL type Application.
type QueryClustersByIdsClustersClusterApplicationsApplication struct {
	Id      string          `json:"id"`
	Type    ApplicationType `json:"type"`
	Version string          `json:"version"`
}

// GetId returns QueryClustersByIdsClustersClusterApplicationsApplication.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterApplicationsApplication) GetId() string { return v.Id }

// GetType returns QueryClustersByIdsClustersClusterApplicationsApplication.Type, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterApplicationsApplication) GetType() ApplicationType {
	return v.Type
}

// GetVersion returns QueryClustersByIdsClustersClusterApplicationsApplication.Version, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterApplicationsApplication) GetVersion() string {
	return v.Version
}

// QueryClustersByIdsClustersClusterHostsHost includes the requested fields of the GraphQL type Host.
type QueryClustersByIdsClustersClusterHostsHost struct {
	Id             string                                                                   `json:"id"`
	Local_id       string                                                                   `json:"local_id"`
	Name           string                                                                   `json:"name"`
	Scvm_name      string                                                                   `json:"scvm_name"`
	Management_ip  string                                                                   `json:"management_ip"`
	Data_ip        string                                                                   `json:"data_ip"`
	State          HostState                                                                `json:"state"`
	Status         HostStatus                                                               `json:"status"`
	Merged_status  HostMergedStatus                                                         `json:"merged_status"`
	Connect_status HostConnectStatus                                                        `json:"connect_status"`
	Host_state     QueryClustersByIdsClustersClusterHostsHostHost_stateMaintenanceHostState `json:"host_state"`
}

// GetId returns QueryClustersByIdsClustersClusterHostsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetId() string { return v.Id }

// GetLocal_id returns QueryClustersByIdsClustersClusterHostsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetLocal_id() string { return v.Local_id }

// GetName returns QueryClustersByIdsClustersClusterHostsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetName() string { return v.Name }

// GetScvm_name returns QueryClustersByIdsClustersClusterHostsHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetScvm_name() string { return v.Scvm_name }

// GetManagement_ip returns QueryClustersByIdsClustersClusterHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetManagement_ip() string {
	return v.Management_ip
}

// GetData_ip returns QueryClustersByIdsClustersClusterHostsHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetData_ip() string { return v.Data_ip }

// GetState returns QueryClustersByIdsClustersClusterHostsHost.State, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetState() HostState { return v.State }

// GetStatus returns QueryClustersByIdsClustersClusterHostsHost.Status, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetStatus() HostStatus { return v.Status }

// GetMerged_status returns QueryClustersByIdsClustersClusterHostsHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetMerged_status() HostMergedStatus {
	return v.Merged_status
}

// GetConnect_status returns QueryClustersByIdsClustersClusterHostsHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetConnect_status() HostConnectStatus {
	return v.Connect_status
}

// GetHost_state returns QueryClustersByIdsClustersClusterHostsHost.Host_state, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHost) GetHost_state() QueryClustersByIdsClustersClusterHostsHostHost_stateMaintenanceHostState {
	return v.Host_state
}

// QueryClustersByIdsClustersClusterHostsHostHost_stateMaintenanceHostState includes the requested fields of the GraphQL type MaintenanceHostState.
type QueryClustersByIdsClustersClusterHostsHostHost_stateMaintenanceHostState struct {
	State MaintenanceModeEnum `json:"state"`
}

// GetState returns QueryClustersByIdsClustersClusterHostsHostHost_stateMaintenanceHostState.State, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsClustersClusterHostsHostHost_stateMaintenanceHostState) GetState() MaintenanceModeEnum {
	return v.State
}

// QueryClustersByIdsResponse is returned by QueryClustersByIds on success.
type QueryClustersByIdsResponse struct {
	Clusters []QueryClustersByIdsClustersCluster `json:"clusters"`
}

// GetClusters returns QueryClustersByIdsResponse.Clusters, and is useful for accessing the field via an interface.
func (v *QueryClustersByIdsResponse) GetClusters() []QueryClustersByIdsClustersCluster {
	return v.Clusters
}

// QueryClustersByLocalIdsClustersCluster includes the requested fields of the GraphQL type Cluster.
type QueryClustersByLocalIdsClustersCluster struct {
	Id           string                                            `json:"id"`
	Local_id     string                                            `json:"local_id"`
	Ip           string                                            `json:"ip"`
	Type         ClusterType                                       `json:"type"`
	Architecture Architecture                                      `json:"architecture"`
	Version      string                                            `json:"version"`
	Name         string                                            `json:"name"`
	Hypervisor   Hypervisor                                        `json:"hypervisor"`
	Hosts        []QueryClustersByLocalIdsClustersClusterHostsHost `json:"hosts"`
}

// GetId returns QueryClustersByLocalIdsClustersCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetId() string { return v.Id }

// GetLocal_id returns QueryClustersByLocalIdsClustersCluster.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetLocal_id() string { return v.Local_id }

// GetIp returns QueryClustersByLocalIdsClustersCluster.Ip, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetIp() string { return v.Ip }

// GetType returns QueryClustersByLocalIdsClustersCluster.Type, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetType() ClusterType { return v.Type }

// GetArchitecture returns QueryClustersByLocalIdsClustersCluster.Architecture, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetArchitecture() Architecture {
	return v.Architecture
}

// GetVersion returns QueryClustersByLocalIdsClustersCluster.Version, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetVersion() string { return v.Version }

// GetName returns QueryClustersByLocalIdsClustersCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetName() string { return v.Name }

// GetHypervisor returns QueryClustersByLocalIdsClustersCluster.Hypervisor, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetHypervisor() Hypervisor { return v.Hypervisor }

// GetHosts returns QueryClustersByLocalIdsClustersCluster.Hosts, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersCluster) GetHosts() []QueryClustersByLocalIdsClustersClusterHostsHost {
	return v.Hosts
}

// QueryClustersByLocalIdsClustersClusterHostsHost includes the requested fields of the GraphQL type Host.
type QueryClustersByLocalIdsClustersClusterHostsHost struct {
	Id             string                                                                        `json:"id"`
	Local_id       string                                                                        `json:"local_id"`
	Name           string                                                                        `json:"name"`
	Scvm_name      string                                                                        `json:"scvm_name"`
	Management_ip  string                                                                        `json:"management_ip"`
	Data_ip        string                                                                        `json:"data_ip"`
	State          HostState                                                                     `json:"state"`
	Status         HostStatus                                                                    `json:"status"`
	Merged_status  HostMergedStatus                                                              `json:"merged_status"`
	Connect_status HostConnectStatus                                                             `json:"connect_status"`
	Host_state     QueryClustersByLocalIdsClustersClusterHostsHostHost_stateMaintenanceHostState `json:"host_state"`
}

// GetId returns QueryClustersByLocalIdsClustersClusterHostsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetId() string { return v.Id }

// GetLocal_id returns QueryClustersByLocalIdsClustersClusterHostsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetLocal_id() string { return v.Local_id }

// GetName returns QueryClustersByLocalIdsClustersClusterHostsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetName() string { return v.Name }

// GetScvm_name returns QueryClustersByLocalIdsClustersClusterHostsHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetScvm_name() string { return v.Scvm_name }

// GetManagement_ip returns QueryClustersByLocalIdsClustersClusterHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetManagement_ip() string {
	return v.Management_ip
}

// GetData_ip returns QueryClustersByLocalIdsClustersClusterHostsHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetData_ip() string { return v.Data_ip }

// GetState returns QueryClustersByLocalIdsClustersClusterHostsHost.State, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetState() HostState { return v.State }

// GetStatus returns QueryClustersByLocalIdsClustersClusterHostsHost.Status, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetStatus() HostStatus { return v.Status }

// GetMerged_status returns QueryClustersByLocalIdsClustersClusterHostsHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetMerged_status() HostMergedStatus {
	return v.Merged_status
}

// GetConnect_status returns QueryClustersByLocalIdsClustersClusterHostsHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetConnect_status() HostConnectStatus {
	return v.Connect_status
}

// GetHost_state returns QueryClustersByLocalIdsClustersClusterHostsHost.Host_state, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHost) GetHost_state() QueryClustersByLocalIdsClustersClusterHostsHostHost_stateMaintenanceHostState {
	return v.Host_state
}

// QueryClustersByLocalIdsClustersClusterHostsHostHost_stateMaintenanceHostState includes the requested fields of the GraphQL type MaintenanceHostState.
type QueryClustersByLocalIdsClustersClusterHostsHostHost_stateMaintenanceHostState struct {
	State MaintenanceModeEnum `json:"state"`
}

// GetState returns QueryClustersByLocalIdsClustersClusterHostsHostHost_stateMaintenanceHostState.State, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsClustersClusterHostsHostHost_stateMaintenanceHostState) GetState() MaintenanceModeEnum {
	return v.State
}

// QueryClustersByLocalIdsResponse is returned by QueryClustersByLocalIds on success.
type QueryClustersByLocalIdsResponse struct {
	Clusters []QueryClustersByLocalIdsClustersCluster `json:"clusters"`
}

// GetClusters returns QueryClustersByLocalIdsResponse.Clusters, and is useful for accessing the field via an interface.
func (v *QueryClustersByLocalIdsResponse) GetClusters() []QueryClustersByLocalIdsClustersCluster {
	return v.Clusters
}

// QueryClustersInfoClustersCluster includes the requested fields of the GraphQL type Cluster.
type QueryClustersInfoClustersCluster struct {
	Name                 string                                      `json:"name"`
	Id                   string                                      `json:"id"`
	Local_id             string                                      `json:"local_id"`
	Architecture         Architecture                                `json:"architecture"`
	Cpu_vendor           string                                      `json:"cpu_vendor"`
	Ip                   string                                      `json:"ip"`
	Management_vip       string                                      `json:"management_vip"`
	Connect_state        ConnectState                                `json:"connect_state"`
	Type                 ClusterType                                 `json:"type"`
	Hypervisor           Hypervisor                                  `json:"hypervisor"`
	Version              string                                      `json:"version"`
	Patch_version        string                                      `json:"patch_version"`
	Upgrade_tool_version string                                      `json:"upgrade_tool_version"`
	Hosts                []QueryClustersInfoClustersClusterHostsHost `json:"hosts"`
	Witness              QueryClustersInfoClustersClusterWitness     `json:"witness"`
}

// GetName returns QueryClustersInfoClustersCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetName() string { return v.Name }

// GetId returns QueryClustersInfoClustersCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetId() string { return v.Id }

// GetLocal_id returns QueryClustersInfoClustersCluster.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetLocal_id() string { return v.Local_id }

// GetArchitecture returns QueryClustersInfoClustersCluster.Architecture, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetArchitecture() Architecture { return v.Architecture }

// GetCpu_vendor returns QueryClustersInfoClustersCluster.Cpu_vendor, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetCpu_vendor() string { return v.Cpu_vendor }

// GetIp returns QueryClustersInfoClustersCluster.Ip, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetIp() string { return v.Ip }

// GetManagement_vip returns QueryClustersInfoClustersCluster.Management_vip, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetManagement_vip() string { return v.Management_vip }

// GetConnect_state returns QueryClustersInfoClustersCluster.Connect_state, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetConnect_state() ConnectState { return v.Connect_state }

// GetType returns QueryClustersInfoClustersCluster.Type, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetType() ClusterType { return v.Type }

// GetHypervisor returns QueryClustersInfoClustersCluster.Hypervisor, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetHypervisor() Hypervisor { return v.Hypervisor }

// GetVersion returns QueryClustersInfoClustersCluster.Version, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetVersion() string { return v.Version }

// GetPatch_version returns QueryClustersInfoClustersCluster.Patch_version, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetPatch_version() string { return v.Patch_version }

// GetUpgrade_tool_version returns QueryClustersInfoClustersCluster.Upgrade_tool_version, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetUpgrade_tool_version() string {
	return v.Upgrade_tool_version
}

// GetHosts returns QueryClustersInfoClustersCluster.Hosts, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetHosts() []QueryClustersInfoClustersClusterHostsHost {
	return v.Hosts
}

// GetWitness returns QueryClustersInfoClustersCluster.Witness, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersCluster) GetWitness() QueryClustersInfoClustersClusterWitness {
	return v.Witness
}

// QueryClustersInfoClustersClusterHostsHost includes the requested fields of the GraphQL type Host.
type QueryClustersInfoClustersClusterHostsHost struct {
	Id             string                                                                  `json:"id"`
	Name           string                                                                  `json:"name"`
	Scvm_name      string                                                                  `json:"scvm_name"`
	Local_id       string                                                                  `json:"local_id"`
	Data_ip        string                                                                  `json:"data_ip"`
	Management_ip  string                                                                  `json:"management_ip"`
	State          HostState                                                               `json:"state"`
	Merged_status  HostMergedStatus                                                        `json:"merged_status"`
	Connect_status HostConnectStatus                                                       `json:"connect_status"`
	Host_state     QueryClustersInfoClustersClusterHostsHostHost_stateMaintenanceHostState `json:"host_state"`
	Chunk_id       string                                                                  `json:"chunk_id"`
	Os_version     string                                                                  `json:"os_version"`
	Role           string                                                                  `json:"role"`
	Gpu_devices    []QueryClustersInfoClustersClusterHostsHostGpu_devicesGpuDevice         `json:"gpu_devices"`
}

// GetId returns QueryClustersInfoClustersClusterHostsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetId() string { return v.Id }

// GetName returns QueryClustersInfoClustersClusterHostsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetName() string { return v.Name }

// GetScvm_name returns QueryClustersInfoClustersClusterHostsHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetScvm_name() string { return v.Scvm_name }

// GetLocal_id returns QueryClustersInfoClustersClusterHostsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClustersInfoClustersClusterHostsHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClustersInfoClustersClusterHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetManagement_ip() string { return v.Management_ip }

// GetState returns QueryClustersInfoClustersClusterHostsHost.State, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetState() HostState { return v.State }

// GetMerged_status returns QueryClustersInfoClustersClusterHostsHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetMerged_status() HostMergedStatus {
	return v.Merged_status
}

// GetConnect_status returns QueryClustersInfoClustersClusterHostsHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetConnect_status() HostConnectStatus {
	return v.Connect_status
}

// GetHost_state returns QueryClustersInfoClustersClusterHostsHost.Host_state, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetHost_state() QueryClustersInfoClustersClusterHostsHostHost_stateMaintenanceHostState {
	return v.Host_state
}

// GetChunk_id returns QueryClustersInfoClustersClusterHostsHost.Chunk_id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetChunk_id() string { return v.Chunk_id }

// GetOs_version returns QueryClustersInfoClustersClusterHostsHost.Os_version, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetOs_version() string { return v.Os_version }

// GetRole returns QueryClustersInfoClustersClusterHostsHost.Role, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetRole() string { return v.Role }

// GetGpu_devices returns QueryClustersInfoClustersClusterHostsHost.Gpu_devices, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHost) GetGpu_devices() []QueryClustersInfoClustersClusterHostsHostGpu_devicesGpuDevice {
	return v.Gpu_devices
}

// QueryClustersInfoClustersClusterHostsHostGpu_devicesGpuDevice includes the requested fields of the GraphQL type GpuDevice.
type QueryClustersInfoClustersClusterHostsHostGpu_devicesGpuDevice struct {
	User_usage GpuDeviceUsage `json:"user_usage"`
}

// GetUser_usage returns QueryClustersInfoClustersClusterHostsHostGpu_devicesGpuDevice.User_usage, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHostGpu_devicesGpuDevice) GetUser_usage() GpuDeviceUsage {
	return v.User_usage
}

// QueryClustersInfoClustersClusterHostsHostHost_stateMaintenanceHostState includes the requested fields of the GraphQL type MaintenanceHostState.
type QueryClustersInfoClustersClusterHostsHostHost_stateMaintenanceHostState struct {
	State MaintenanceModeEnum `json:"state"`
}

// GetState returns QueryClustersInfoClustersClusterHostsHostHost_stateMaintenanceHostState.State, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterHostsHostHost_stateMaintenanceHostState) GetState() MaintenanceModeEnum {
	return v.State
}

// QueryClustersInfoClustersClusterWitness includes the requested fields of the GraphQL type Witness.
type QueryClustersInfoClustersClusterWitness struct {
	Id            string `json:"id"`
	Name          string `json:"name"`
	Local_id      string `json:"local_id"`
	Data_ip       string `json:"data_ip"`
	Management_ip string `json:"management_ip"`
}

// GetId returns QueryClustersInfoClustersClusterWitness.Id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterWitness) GetId() string { return v.Id }

// GetName returns QueryClustersInfoClustersClusterWitness.Name, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterWitness) GetName() string { return v.Name }

// GetLocal_id returns QueryClustersInfoClustersClusterWitness.Local_id, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterWitness) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryClustersInfoClustersClusterWitness.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterWitness) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryClustersInfoClustersClusterWitness.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoClustersClusterWitness) GetManagement_ip() string { return v.Management_ip }

// QueryClustersInfoResponse is returned by QueryClustersInfo on success.
type QueryClustersInfoResponse struct {
	Clusters []QueryClustersInfoClustersCluster `json:"clusters"`
}

// GetClusters returns QueryClustersInfoResponse.Clusters, and is useful for accessing the field via an interface.
func (v *QueryClustersInfoResponse) GetClusters() []QueryClustersInfoClustersCluster {
	return v.Clusters
}

// QueryHostInfoByIdHost includes the requested fields of the GraphQL type Host.
type QueryHostInfoByIdHost struct {
	Id             string            `json:"id"`
	Name           string            `json:"name"`
	Scvm_name      string            `json:"scvm_name"`
	Local_id       string            `json:"local_id"`
	Data_ip        string            `json:"data_ip"`
	Management_ip  string            `json:"management_ip"`
	Connect_status HostConnectStatus `json:"connect_status"`
	Merged_status  HostMergedStatus  `json:"merged_status"`
	Chunk_id       string            `json:"chunk_id"`
	Role           string            `json:"role"`
	State          HostState         `json:"state"`
	Status         HostStatus        `json:"status"`
}

// GetId returns QueryHostInfoByIdHost.Id, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetId() string { return v.Id }

// GetName returns QueryHostInfoByIdHost.Name, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetName() string { return v.Name }

// GetScvm_name returns QueryHostInfoByIdHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetScvm_name() string { return v.Scvm_name }

// GetLocal_id returns QueryHostInfoByIdHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryHostInfoByIdHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryHostInfoByIdHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetManagement_ip() string { return v.Management_ip }

// GetConnect_status returns QueryHostInfoByIdHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetConnect_status() HostConnectStatus { return v.Connect_status }

// GetMerged_status returns QueryHostInfoByIdHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetMerged_status() HostMergedStatus { return v.Merged_status }

// GetChunk_id returns QueryHostInfoByIdHost.Chunk_id, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetChunk_id() string { return v.Chunk_id }

// GetRole returns QueryHostInfoByIdHost.Role, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetRole() string { return v.Role }

// GetState returns QueryHostInfoByIdHost.State, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetState() HostState { return v.State }

// GetStatus returns QueryHostInfoByIdHost.Status, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdHost) GetStatus() HostStatus { return v.Status }

// QueryHostInfoByIdResponse is returned by QueryHostInfoById on success.
type QueryHostInfoByIdResponse struct {
	Host QueryHostInfoByIdHost `json:"host"`
}

// GetHost returns QueryHostInfoByIdResponse.Host, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByIdResponse) GetHost() QueryHostInfoByIdHost { return v.Host }

// QueryHostInfoByLocalIdHost includes the requested fields of the GraphQL type Host.
type QueryHostInfoByLocalIdHost struct {
	Id             string            `json:"id"`
	Name           string            `json:"name"`
	Scvm_name      string            `json:"scvm_name"`
	Local_id       string            `json:"local_id"`
	Data_ip        string            `json:"data_ip"`
	Management_ip  string            `json:"management_ip"`
	Connect_status HostConnectStatus `json:"connect_status"`
	Merged_status  HostMergedStatus  `json:"merged_status"`
	Chunk_id       string            `json:"chunk_id"`
	Role           string            `json:"role"`
	State          HostState         `json:"state"`
	Status         HostStatus        `json:"status"`
}

// GetId returns QueryHostInfoByLocalIdHost.Id, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetId() string { return v.Id }

// GetName returns QueryHostInfoByLocalIdHost.Name, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetName() string { return v.Name }

// GetScvm_name returns QueryHostInfoByLocalIdHost.Scvm_name, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetScvm_name() string { return v.Scvm_name }

// GetLocal_id returns QueryHostInfoByLocalIdHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetLocal_id() string { return v.Local_id }

// GetData_ip returns QueryHostInfoByLocalIdHost.Data_ip, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetData_ip() string { return v.Data_ip }

// GetManagement_ip returns QueryHostInfoByLocalIdHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetManagement_ip() string { return v.Management_ip }

// GetConnect_status returns QueryHostInfoByLocalIdHost.Connect_status, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetConnect_status() HostConnectStatus { return v.Connect_status }

// GetMerged_status returns QueryHostInfoByLocalIdHost.Merged_status, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetMerged_status() HostMergedStatus { return v.Merged_status }

// GetChunk_id returns QueryHostInfoByLocalIdHost.Chunk_id, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetChunk_id() string { return v.Chunk_id }

// GetRole returns QueryHostInfoByLocalIdHost.Role, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetRole() string { return v.Role }

// GetState returns QueryHostInfoByLocalIdHost.State, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetState() HostState { return v.State }

// GetStatus returns QueryHostInfoByLocalIdHost.Status, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdHost) GetStatus() HostStatus { return v.Status }

// QueryHostInfoByLocalIdResponse is returned by QueryHostInfoByLocalId on success.
type QueryHostInfoByLocalIdResponse struct {
	Host QueryHostInfoByLocalIdHost `json:"host"`
}

// GetHost returns QueryHostInfoByLocalIdResponse.Host, and is useful for accessing the field via an interface.
func (v *QueryHostInfoByLocalIdResponse) GetHost() QueryHostInfoByLocalIdHost { return v.Host }

// QueryHostPluginByIdsHostPluginsHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type QueryHostPluginByIdsHostPluginsHostPlugin struct {
	Id                    string                                                                        `json:"id"`
	Name                  string                                                                        `json:"name"`
	Namespace             string                                                                        `json:"namespace"`
	Values                []string                                                                      `json:"values"`
	Cluster               QueryHostPluginByIdsHostPluginsHostPluginCluster                              `json:"cluster"`
	Host_plugin_package   QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage `json:"host_plugin_package"`
	Host_plugin_instances json.RawMessage                                                               `json:"host_plugin_instances"`
	Hosts                 []QueryHostPluginByIdsHostPluginsHostPluginHostsHost                          `json:"hosts"`
}

// GetId returns QueryHostPluginByIdsHostPluginsHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetId() string { return v.Id }

// GetName returns QueryHostPluginByIdsHostPluginsHostPlugin.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetName() string { return v.Name }

// GetNamespace returns QueryHostPluginByIdsHostPluginsHostPlugin.Namespace, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetNamespace() string { return v.Namespace }

// GetValues returns QueryHostPluginByIdsHostPluginsHostPlugin.Values, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetValues() []string { return v.Values }

// GetCluster returns QueryHostPluginByIdsHostPluginsHostPlugin.Cluster, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetCluster() QueryHostPluginByIdsHostPluginsHostPluginCluster {
	return v.Cluster
}

// GetHost_plugin_package returns QueryHostPluginByIdsHostPluginsHostPlugin.Host_plugin_package, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetHost_plugin_package() QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage {
	return v.Host_plugin_package
}

// GetHost_plugin_instances returns QueryHostPluginByIdsHostPluginsHostPlugin.Host_plugin_instances, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetHost_plugin_instances() json.RawMessage {
	return v.Host_plugin_instances
}

// GetHosts returns QueryHostPluginByIdsHostPluginsHostPlugin.Hosts, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPlugin) GetHosts() []QueryHostPluginByIdsHostPluginsHostPluginHostsHost {
	return v.Hosts
}

// QueryHostPluginByIdsHostPluginsHostPluginCluster includes the requested fields of the GraphQL type Cluster.
type QueryHostPluginByIdsHostPluginsHostPluginCluster struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

// GetId returns QueryHostPluginByIdsHostPluginsHostPluginCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginCluster) GetId() string { return v.Id }

// GetName returns QueryHostPluginByIdsHostPluginsHostPluginCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginCluster) GetName() string { return v.Name }

// QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
	Size    float64      `json:"size"`
}

// GetId returns QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetId() string {
	return v.Id
}

// GetName returns QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetName() string {
	return v.Name
}

// GetVersion returns QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// GetSize returns QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Size, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetSize() float64 {
	return v.Size
}

// QueryHostPluginByIdsHostPluginsHostPluginHostsHost includes the requested fields of the GraphQL type Host.
type QueryHostPluginByIdsHostPluginsHostPluginHostsHost struct {
	Management_ip string `json:"management_ip"`
}

// GetManagement_ip returns QueryHostPluginByIdsHostPluginsHostPluginHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsHostPluginsHostPluginHostsHost) GetManagement_ip() string {
	return v.Management_ip
}

// QueryHostPluginByIdsResponse is returned by QueryHostPluginByIds on success.
type QueryHostPluginByIdsResponse struct {
	HostPlugins []QueryHostPluginByIdsHostPluginsHostPlugin `json:"hostPlugins"`
}

// GetHostPlugins returns QueryHostPluginByIdsResponse.HostPlugins, and is useful for accessing the field via an interface.
func (v *QueryHostPluginByIdsResponse) GetHostPlugins() []QueryHostPluginByIdsHostPluginsHostPlugin {
	return v.HostPlugins
}

// QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
	Size    float64      `json:"size"`
}

// GetId returns QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage) GetId() string { return v.Id }

// GetName returns QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage) GetName() string {
	return v.Name
}

// GetVersion returns QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// GetSize returns QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage.Size, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage) GetSize() float64 {
	return v.Size
}

// QueryHostPluginPackagesByIdsResponse is returned by QueryHostPluginPackagesByIds on success.
type QueryHostPluginPackagesByIdsResponse struct {
	HostPluginPackages []QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage `json:"hostPluginPackages"`
}

// GetHostPluginPackages returns QueryHostPluginPackagesByIdsResponse.HostPluginPackages, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByIdsResponse) GetHostPluginPackages() []QueryHostPluginPackagesByIdsHostPluginPackagesHostPluginPackage {
	return v.HostPluginPackages
}

// QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
	Size    float64      `json:"size"`
}

// GetId returns QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage) GetId() string {
	return v.Id
}

// GetName returns QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage) GetName() string {
	return v.Name
}

// GetVersion returns QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// GetSize returns QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage.Size, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage) GetSize() float64 {
	return v.Size
}

// QueryHostPluginPackagesByPackageNameResponse is returned by QueryHostPluginPackagesByPackageName on success.
type QueryHostPluginPackagesByPackageNameResponse struct {
	HostPluginPackages []QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage `json:"hostPluginPackages"`
}

// GetHostPluginPackages returns QueryHostPluginPackagesByPackageNameResponse.HostPluginPackages, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesByPackageNameResponse) GetHostPluginPackages() []QueryHostPluginPackagesByPackageNameHostPluginPackagesHostPluginPackage {
	return v.HostPluginPackages
}

// QueryHostPluginPackagesHostPluginPackagesHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryHostPluginPackagesHostPluginPackagesHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
	Size    float64      `json:"size"`
}

// GetId returns QueryHostPluginPackagesHostPluginPackagesHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesHostPluginPackagesHostPluginPackage) GetId() string { return v.Id }

// GetName returns QueryHostPluginPackagesHostPluginPackagesHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesHostPluginPackagesHostPluginPackage) GetName() string { return v.Name }

// GetVersion returns QueryHostPluginPackagesHostPluginPackagesHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesHostPluginPackagesHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QueryHostPluginPackagesHostPluginPackagesHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesHostPluginPackagesHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// GetSize returns QueryHostPluginPackagesHostPluginPackagesHostPluginPackage.Size, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesHostPluginPackagesHostPluginPackage) GetSize() float64 { return v.Size }

// QueryHostPluginPackagesResponse is returned by QueryHostPluginPackages on success.
type QueryHostPluginPackagesResponse struct {
	HostPluginPackages []QueryHostPluginPackagesHostPluginPackagesHostPluginPackage `json:"hostPluginPackages"`
}

// GetHostPluginPackages returns QueryHostPluginPackagesResponse.HostPluginPackages, and is useful for accessing the field via an interface.
func (v *QueryHostPluginPackagesResponse) GetHostPluginPackages() []QueryHostPluginPackagesHostPluginPackagesHostPluginPackage {
	return v.HostPluginPackages
}

// QueryHostPluginsByPackageNameHostPluginsHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type QueryHostPluginsByPackageNameHostPluginsHostPlugin struct {
	Host_plugin_package QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage `json:"host_plugin_package"`
}

// GetHost_plugin_package returns QueryHostPluginsByPackageNameHostPluginsHostPlugin.Host_plugin_package, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsByPackageNameHostPluginsHostPlugin) GetHost_plugin_package() QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage {
	return v.Host_plugin_package
}

// QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
}

// GetId returns QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetId() string {
	return v.Id
}

// GetName returns QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetName() string {
	return v.Name
}

// GetVersion returns QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// QueryHostPluginsByPackageNameResponse is returned by QueryHostPluginsByPackageName on success.
type QueryHostPluginsByPackageNameResponse struct {
	HostPlugins []QueryHostPluginsByPackageNameHostPluginsHostPlugin `json:"hostPlugins"`
}

// GetHostPlugins returns QueryHostPluginsByPackageNameResponse.HostPlugins, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsByPackageNameResponse) GetHostPlugins() []QueryHostPluginsByPackageNameHostPluginsHostPlugin {
	return v.HostPlugins
}

// QueryHostPluginsHostPluginsHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type QueryHostPluginsHostPluginsHostPlugin struct {
	Id                    string                                                                    `json:"id"`
	Name                  string                                                                    `json:"name"`
	Namespace             string                                                                    `json:"namespace"`
	Values                []string                                                                  `json:"values"`
	Cluster               QueryHostPluginsHostPluginsHostPluginCluster                              `json:"cluster"`
	Host_plugin_package   QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage `json:"host_plugin_package"`
	Host_plugin_instances json.RawMessage                                                           `json:"host_plugin_instances"`
	Hosts                 []QueryHostPluginsHostPluginsHostPluginHostsHost                          `json:"hosts"`
}

// GetId returns QueryHostPluginsHostPluginsHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetId() string { return v.Id }

// GetName returns QueryHostPluginsHostPluginsHostPlugin.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetName() string { return v.Name }

// GetNamespace returns QueryHostPluginsHostPluginsHostPlugin.Namespace, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetNamespace() string { return v.Namespace }

// GetValues returns QueryHostPluginsHostPluginsHostPlugin.Values, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetValues() []string { return v.Values }

// GetCluster returns QueryHostPluginsHostPluginsHostPlugin.Cluster, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetCluster() QueryHostPluginsHostPluginsHostPluginCluster {
	return v.Cluster
}

// GetHost_plugin_package returns QueryHostPluginsHostPluginsHostPlugin.Host_plugin_package, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetHost_plugin_package() QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage {
	return v.Host_plugin_package
}

// GetHost_plugin_instances returns QueryHostPluginsHostPluginsHostPlugin.Host_plugin_instances, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetHost_plugin_instances() json.RawMessage {
	return v.Host_plugin_instances
}

// GetHosts returns QueryHostPluginsHostPluginsHostPlugin.Hosts, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPlugin) GetHosts() []QueryHostPluginsHostPluginsHostPluginHostsHost {
	return v.Hosts
}

// QueryHostPluginsHostPluginsHostPluginCluster includes the requested fields of the GraphQL type Cluster.
type QueryHostPluginsHostPluginsHostPluginCluster struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

// GetId returns QueryHostPluginsHostPluginsHostPluginCluster.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginCluster) GetId() string { return v.Id }

// GetName returns QueryHostPluginsHostPluginsHostPluginCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginCluster) GetName() string { return v.Name }

// QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
	Size    float64      `json:"size"`
}

// GetId returns QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetId() string {
	return v.Id
}

// GetName returns QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetName() string {
	return v.Name
}

// GetVersion returns QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// GetSize returns QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Size, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetSize() float64 {
	return v.Size
}

// QueryHostPluginsHostPluginsHostPluginHostsHost includes the requested fields of the GraphQL type Host.
type QueryHostPluginsHostPluginsHostPluginHostsHost struct {
	Management_ip string `json:"management_ip"`
}

// GetManagement_ip returns QueryHostPluginsHostPluginsHostPluginHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsHostPluginsHostPluginHostsHost) GetManagement_ip() string {
	return v.Management_ip
}

// QueryHostPluginsResponse is returned by QueryHostPlugins on success.
type QueryHostPluginsResponse struct {
	HostPlugins []QueryHostPluginsHostPluginsHostPlugin `json:"hostPlugins"`
}

// GetHostPlugins returns QueryHostPluginsResponse.HostPlugins, and is useful for accessing the field via an interface.
func (v *QueryHostPluginsResponse) GetHostPlugins() []QueryHostPluginsHostPluginsHostPlugin {
	return v.HostPlugins
}

// QueryHostVMsByHostLocalIDHost includes the requested fields of the GraphQL type Host.
type QueryHostVMsByHostLocalIDHost struct {
	Id       string                               `json:"id"`
	Local_id string                               `json:"local_id"`
	Name     string                               `json:"name"`
	Vm_num   int                                  `json:"vm_num"`
	Vms      []QueryHostVMsByHostLocalIDHostVmsVm `json:"vms"`
}

// GetId returns QueryHostVMsByHostLocalIDHost.Id, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHost) GetId() string { return v.Id }

// GetLocal_id returns QueryHostVMsByHostLocalIDHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHost) GetLocal_id() string { return v.Local_id }

// GetName returns QueryHostVMsByHostLocalIDHost.Name, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHost) GetName() string { return v.Name }

// GetVm_num returns QueryHostVMsByHostLocalIDHost.Vm_num, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHost) GetVm_num() int { return v.Vm_num }

// GetVms returns QueryHostVMsByHostLocalIDHost.Vms, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHost) GetVms() []QueryHostVMsByHostLocalIDHostVmsVm { return v.Vms }

// QueryHostVMsByHostLocalIDHostVmsVm includes the requested fields of the GraphQL type Vm.
type QueryHostVMsByHostLocalIDHostVmsVm struct {
	Id             string  `json:"id"`
	Local_id       string  `json:"local_id"`
	Name           string  `json:"name"`
	Internal       bool    `json:"internal"`
	Vm_usage       VmUsage `json:"vm_usage"`
	In_recycle_bin bool    `json:"in_recycle_bin"`
}

// GetId returns QueryHostVMsByHostLocalIDHostVmsVm.Id, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHostVmsVm) GetId() string { return v.Id }

// GetLocal_id returns QueryHostVMsByHostLocalIDHostVmsVm.Local_id, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHostVmsVm) GetLocal_id() string { return v.Local_id }

// GetName returns QueryHostVMsByHostLocalIDHostVmsVm.Name, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHostVmsVm) GetName() string { return v.Name }

// GetInternal returns QueryHostVMsByHostLocalIDHostVmsVm.Internal, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHostVmsVm) GetInternal() bool { return v.Internal }

// GetVm_usage returns QueryHostVMsByHostLocalIDHostVmsVm.Vm_usage, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHostVmsVm) GetVm_usage() VmUsage { return v.Vm_usage }

// GetIn_recycle_bin returns QueryHostVMsByHostLocalIDHostVmsVm.In_recycle_bin, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDHostVmsVm) GetIn_recycle_bin() bool { return v.In_recycle_bin }

// QueryHostVMsByHostLocalIDResponse is returned by QueryHostVMsByHostLocalID on success.
type QueryHostVMsByHostLocalIDResponse struct {
	Host QueryHostVMsByHostLocalIDHost `json:"host"`
}

// GetHost returns QueryHostVMsByHostLocalIDResponse.Host, and is useful for accessing the field via an interface.
func (v *QueryHostVMsByHostLocalIDResponse) GetHost() QueryHostVMsByHostLocalIDHost { return v.Host }

// QueryInRecycleBinVMsByClusterIDResponse is returned by QueryInRecycleBinVMsByClusterID on success.
type QueryInRecycleBinVMsByClusterIDResponse struct {
	Vms []QueryInRecycleBinVMsByClusterIDVmsVm `json:"vms"`
}

// GetVms returns QueryInRecycleBinVMsByClusterIDResponse.Vms, and is useful for accessing the field via an interface.
func (v *QueryInRecycleBinVMsByClusterIDResponse) GetVms() []QueryInRecycleBinVMsByClusterIDVmsVm {
	return v.Vms
}

// QueryInRecycleBinVMsByClusterIDVmsVm includes the requested fields of the GraphQL type Vm.
type QueryInRecycleBinVMsByClusterIDVmsVm struct {
	Local_id string `json:"local_id"`
}

// GetLocal_id returns QueryInRecycleBinVMsByClusterIDVmsVm.Local_id, and is useful for accessing the field via an interface.
func (v *QueryInRecycleBinVMsByClusterIDVmsVm) GetLocal_id() string { return v.Local_id }

// QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPlugin struct {
	Host_plugin_package QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage `json:"host_plugin_package"`
}

// GetHost_plugin_package returns QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPlugin.Host_plugin_package, and is useful for accessing the field via an interface.
func (v *QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPlugin) GetHost_plugin_package() QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage {
	return v.Host_plugin_package
}

// QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage struct {
	Id string `json:"id"`
}

// GetId returns QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetId() string {
	return v.Id
}

// QueryInstalledHostPluginPackagesByPackageNameResponse is returned by QueryInstalledHostPluginPackagesByPackageName on success.
type QueryInstalledHostPluginPackagesByPackageNameResponse struct {
	HostPlugins []QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPlugin `json:"hostPlugins"`
}

// GetHostPlugins returns QueryInstalledHostPluginPackagesByPackageNameResponse.HostPlugins, and is useful for accessing the field via an interface.
func (v *QueryInstalledHostPluginPackagesByPackageNameResponse) GetHostPlugins() []QueryInstalledHostPluginPackagesByPackageNameHostPluginsHostPlugin {
	return v.HostPlugins
}

// QueryPlacementGroupsByHostLocalIDResponse is returned by QueryPlacementGroupsByHostLocalID on success.
type QueryPlacementGroupsByHostLocalIDResponse struct {
	VmPlacementGroups []QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup `json:"vmPlacementGroups"`
}

// GetVmPlacementGroups returns QueryPlacementGroupsByHostLocalIDResponse.VmPlacementGroups, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDResponse) GetVmPlacementGroups() []QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup {
	return v.VmPlacementGroups
}

// QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup includes the requested fields of the GraphQL type VmPlacementGroup.
type QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup struct {
	Id                        string                                                                                            `json:"id"`
	Name                      string                                                                                            `json:"name"`
	Cluster                   QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupCluster                         `json:"cluster"`
	Vm_host_must_host_uuids   []QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost   `json:"vm_host_must_host_uuids"`
	Vm_host_prefer_host_uuids []QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost `json:"vm_host_prefer_host_uuids"`
}

// GetId returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup.Id, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup) GetId() string {
	return v.Id
}

// GetName returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup.Name, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup) GetName() string {
	return v.Name
}

// GetCluster returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup.Cluster, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup) GetCluster() QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupCluster {
	return v.Cluster
}

// GetVm_host_must_host_uuids returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup.Vm_host_must_host_uuids, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup) GetVm_host_must_host_uuids() []QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost {
	return v.Vm_host_must_host_uuids
}

// GetVm_host_prefer_host_uuids returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup.Vm_host_prefer_host_uuids, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroup) GetVm_host_prefer_host_uuids() []QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost {
	return v.Vm_host_prefer_host_uuids
}

// QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupCluster includes the requested fields of the GraphQL type Cluster.
type QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupCluster struct {
	Name string `json:"name"`
}

// GetName returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupCluster.Name, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupCluster) GetName() string {
	return v.Name
}

// QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost includes the requested fields of the GraphQL type Host.
type QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost struct {
	Id       string `json:"id"`
	Local_id string `json:"local_id"`
	Name     string `json:"name"`
}

// GetId returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost) GetId() string {
	return v.Id
}

// GetLocal_id returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost) GetLocal_id() string {
	return v.Local_id
}

// GetName returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_must_host_uuidsHost) GetName() string {
	return v.Name
}

// QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost includes the requested fields of the GraphQL type Host.
type QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost struct {
	Id       string `json:"id"`
	Local_id string `json:"local_id"`
	Name     string `json:"name"`
}

// GetId returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost.Id, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost) GetId() string {
	return v.Id
}

// GetLocal_id returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost) GetLocal_id() string {
	return v.Local_id
}

// GetName returns QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost.Name, and is useful for accessing the field via an interface.
func (v *QueryPlacementGroupsByHostLocalIDVmPlacementGroupsVmPlacementGroupVm_host_prefer_host_uuidsHost) GetName() string {
	return v.Name
}

// QuerySpecificHostPluginsByClustersHostPluginsHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type QuerySpecificHostPluginsByClustersHostPluginsHostPlugin struct {
	Id                    string                                                                                      `json:"id"`
	Name                  string                                                                                      `json:"name"`
	Namespace             string                                                                                      `json:"namespace"`
	Values                []string                                                                                    `json:"values"`
	Cluster               QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster                              `json:"cluster"`
	Host_plugin_package   QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage `json:"host_plugin_package"`
	Host_plugin_instances json.RawMessage                                                                             `json:"host_plugin_instances"`
	Hosts                 []QuerySpecificHostPluginsByClustersHostPluginsHostPluginHostsHost                          `json:"hosts"`
}

// GetId returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetId() string { return v.Id }

// GetName returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Name, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetName() string { return v.Name }

// GetNamespace returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Namespace, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetNamespace() string {
	return v.Namespace
}

// GetValues returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Values, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetValues() []string {
	return v.Values
}

// GetCluster returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Cluster, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetCluster() QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster {
	return v.Cluster
}

// GetHost_plugin_package returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Host_plugin_package, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetHost_plugin_package() QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage {
	return v.Host_plugin_package
}

// GetHost_plugin_instances returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Host_plugin_instances, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetHost_plugin_instances() json.RawMessage {
	return v.Host_plugin_instances
}

// GetHosts returns QuerySpecificHostPluginsByClustersHostPluginsHostPlugin.Hosts, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) GetHosts() []QuerySpecificHostPluginsByClustersHostPluginsHostPluginHostsHost {
	return v.Hosts
}

// QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster includes the requested fields of the GraphQL type Cluster.
type QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

// GetId returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster.Id, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster) GetId() string { return v.Id }

// GetName returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster.Name, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginCluster) GetName() string {
	return v.Name
}

// QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage includes the requested fields of the GraphQL type HostPluginPackage.
type QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage struct {
	Id      string       `json:"id"`
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
	Size    float64      `json:"size"`
}

// GetId returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Id, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetId() string {
	return v.Id
}

// GetName returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Name, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetName() string {
	return v.Name
}

// GetVersion returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Version, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetVersion() string {
	return v.Version
}

// GetArch returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Arch, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetArch() Architecture {
	return v.Arch
}

// GetSize returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage.Size, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginHost_plugin_packageHostPluginPackage) GetSize() float64 {
	return v.Size
}

// QuerySpecificHostPluginsByClustersHostPluginsHostPluginHostsHost includes the requested fields of the GraphQL type Host.
type QuerySpecificHostPluginsByClustersHostPluginsHostPluginHostsHost struct {
	Management_ip string `json:"management_ip"`
}

// GetManagement_ip returns QuerySpecificHostPluginsByClustersHostPluginsHostPluginHostsHost.Management_ip, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersHostPluginsHostPluginHostsHost) GetManagement_ip() string {
	return v.Management_ip
}

// QuerySpecificHostPluginsByClustersResponse is returned by QuerySpecificHostPluginsByClusters on success.
type QuerySpecificHostPluginsByClustersResponse struct {
	HostPlugins []QuerySpecificHostPluginsByClustersHostPluginsHostPlugin `json:"hostPlugins"`
}

// GetHostPlugins returns QuerySpecificHostPluginsByClustersResponse.HostPlugins, and is useful for accessing the field via an interface.
func (v *QuerySpecificHostPluginsByClustersResponse) GetHostPlugins() []QuerySpecificHostPluginsByClustersHostPluginsHostPlugin {
	return v.HostPlugins
}

// QuerySystemVMsByHostIDHostsHost includes the requested fields of the GraphQL type Host.
type QuerySystemVMsByHostIDHostsHost struct {
	Id       string                                 `json:"id"`
	Local_id string                                 `json:"local_id"`
	Vms      []QuerySystemVMsByHostIDHostsHostVmsVm `json:"vms"`
}

// GetId returns QuerySystemVMsByHostIDHostsHost.Id, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDHostsHost) GetId() string { return v.Id }

// GetLocal_id returns QuerySystemVMsByHostIDHostsHost.Local_id, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDHostsHost) GetLocal_id() string { return v.Local_id }

// GetVms returns QuerySystemVMsByHostIDHostsHost.Vms, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDHostsHost) GetVms() []QuerySystemVMsByHostIDHostsHostVmsVm {
	return v.Vms
}

// QuerySystemVMsByHostIDHostsHostVmsVm includes the requested fields of the GraphQL type Vm.
type QuerySystemVMsByHostIDHostsHostVmsVm struct {
	Internal bool    `json:"internal"`
	Vm_usage VmUsage `json:"vm_usage"`
	Local_id string  `json:"local_id"`
}

// GetInternal returns QuerySystemVMsByHostIDHostsHostVmsVm.Internal, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDHostsHostVmsVm) GetInternal() bool { return v.Internal }

// GetVm_usage returns QuerySystemVMsByHostIDHostsHostVmsVm.Vm_usage, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDHostsHostVmsVm) GetVm_usage() VmUsage { return v.Vm_usage }

// GetLocal_id returns QuerySystemVMsByHostIDHostsHostVmsVm.Local_id, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDHostsHostVmsVm) GetLocal_id() string { return v.Local_id }

// QuerySystemVMsByHostIDResponse is returned by QuerySystemVMsByHostID on success.
type QuerySystemVMsByHostIDResponse struct {
	Hosts []QuerySystemVMsByHostIDHostsHost `json:"hosts"`
}

// GetHosts returns QuerySystemVMsByHostIDResponse.Hosts, and is useful for accessing the field via an interface.
func (v *QuerySystemVMsByHostIDResponse) GetHosts() []QuerySystemVMsByHostIDHostsHost { return v.Hosts }

// QueryTaskByIdResponse is returned by QueryTaskById on success.
type QueryTaskByIdResponse struct {
	Tasks []QueryTaskByIdTasksTask `json:"tasks"`
}

// GetTasks returns QueryTaskByIdResponse.Tasks, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdResponse) GetTasks() []QueryTaskByIdTasksTask { return v.Tasks }

// QueryTaskByIdTasksTask includes the requested fields of the GraphQL type Task.
type QueryTaskByIdTasksTask struct {
	Id                string                            `json:"id"`
	Status            TaskStatus                        `json:"status"`
	Args              json.RawMessage                   `json:"args"`
	Progress          float64                           `json:"progress"`
	Started_at        time.Time                         `json:"started_at"`
	Finished_at       time.Time                         `json:"finished_at"`
	Description       string                            `json:"description"`
	Error_code        string                            `json:"error_code"`
	Error_message     string                            `json:"error_message"`
	Resource_id       string                            `json:"resource_id"`
	Resource_mutation string                            `json:"resource_mutation"`
	Steps             []QueryTaskByIdTasksTaskStepsStep `json:"steps"`
}

// GetId returns QueryTaskByIdTasksTask.Id, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetId() string { return v.Id }

// GetStatus returns QueryTaskByIdTasksTask.Status, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetStatus() TaskStatus { return v.Status }

// GetArgs returns QueryTaskByIdTasksTask.Args, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetArgs() json.RawMessage { return v.Args }

// GetProgress returns QueryTaskByIdTasksTask.Progress, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetProgress() float64 { return v.Progress }

// GetStarted_at returns QueryTaskByIdTasksTask.Started_at, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetStarted_at() time.Time { return v.Started_at }

// GetFinished_at returns QueryTaskByIdTasksTask.Finished_at, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetFinished_at() time.Time { return v.Finished_at }

// GetDescription returns QueryTaskByIdTasksTask.Description, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetDescription() string { return v.Description }

// GetError_code returns QueryTaskByIdTasksTask.Error_code, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetError_code() string { return v.Error_code }

// GetError_message returns QueryTaskByIdTasksTask.Error_message, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetError_message() string { return v.Error_message }

// GetResource_id returns QueryTaskByIdTasksTask.Resource_id, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetResource_id() string { return v.Resource_id }

// GetResource_mutation returns QueryTaskByIdTasksTask.Resource_mutation, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetResource_mutation() string { return v.Resource_mutation }

// GetSteps returns QueryTaskByIdTasksTask.Steps, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTask) GetSteps() []QueryTaskByIdTasksTaskStepsStep { return v.Steps }

// QueryTaskByIdTasksTaskStepsStep includes the requested fields of the GraphQL type Step.
type QueryTaskByIdTasksTaskStepsStep struct {
	Key      string `json:"key"`
	Finished bool   `json:"finished"`
}

// GetKey returns QueryTaskByIdTasksTaskStepsStep.Key, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTaskStepsStep) GetKey() string { return v.Key }

// GetFinished returns QueryTaskByIdTasksTaskStepsStep.Finished, and is useful for accessing the field via an interface.
func (v *QueryTaskByIdTasksTaskStepsStep) GetFinished() bool { return v.Finished }

// QueryTaskStartedAtResponse is returned by QueryTaskStartedAt on success.
type QueryTaskStartedAtResponse struct {
	Tasks []QueryTaskStartedAtTasksTask `json:"tasks"`
}

// GetTasks returns QueryTaskStartedAtResponse.Tasks, and is useful for accessing the field via an interface.
func (v *QueryTaskStartedAtResponse) GetTasks() []QueryTaskStartedAtTasksTask { return v.Tasks }

// QueryTaskStartedAtTasksTask includes the requested fields of the GraphQL type Task.
type QueryTaskStartedAtTasksTask struct {
	Started_at time.Time `json:"started_at"`
}

// GetStarted_at returns QueryTaskStartedAtTasksTask.Started_at, and is useful for accessing the field via an interface.
func (v *QueryTaskStartedAtTasksTask) GetStarted_at() time.Time { return v.Started_at }

// QueryUpgradeCenterTasksResponse is returned by QueryUpgradeCenterTasks on success.
type QueryUpgradeCenterTasksResponse struct {
	Tasks []QueryUpgradeCenterTasksTasksTask `json:"tasks"`
}

// GetTasks returns QueryUpgradeCenterTasksResponse.Tasks, and is useful for accessing the field via an interface.
func (v *QueryUpgradeCenterTasksResponse) GetTasks() []QueryUpgradeCenterTasksTasksTask {
	return v.Tasks
}

// QueryUpgradeCenterTasksTasksTask includes the requested fields of the GraphQL type Task.
type QueryUpgradeCenterTasksTasksTask struct {
	Id                string     `json:"id"`
	Status            TaskStatus `json:"status"`
	Started_at        time.Time  `json:"started_at"`
	Finished_at       time.Time  `json:"finished_at"`
	Resource_mutation string     `json:"resource_mutation"`
}

// GetId returns QueryUpgradeCenterTasksTasksTask.Id, and is useful for accessing the field via an interface.
func (v *QueryUpgradeCenterTasksTasksTask) GetId() string { return v.Id }

// GetStatus returns QueryUpgradeCenterTasksTasksTask.Status, and is useful for accessing the field via an interface.
func (v *QueryUpgradeCenterTasksTasksTask) GetStatus() TaskStatus { return v.Status }

// GetStarted_at returns QueryUpgradeCenterTasksTasksTask.Started_at, and is useful for accessing the field via an interface.
func (v *QueryUpgradeCenterTasksTasksTask) GetStarted_at() time.Time { return v.Started_at }

// GetFinished_at returns QueryUpgradeCenterTasksTasksTask.Finished_at, and is useful for accessing the field via an interface.
func (v *QueryUpgradeCenterTasksTasksTask) GetFinished_at() time.Time { return v.Finished_at }

// GetResource_mutation returns QueryUpgradeCenterTasksTasksTask.Resource_mutation, and is useful for accessing the field via an interface.
func (v *QueryUpgradeCenterTasksTasksTask) GetResource_mutation() string { return v.Resource_mutation }

// QueryUsbDevicesByHostLocalIdResponse is returned by QueryUsbDevicesByHostLocalId on success.
type QueryUsbDevicesByHostLocalIdResponse struct {
	UsbDevices []QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice `json:"usbDevices"`
}

// GetUsbDevices returns QueryUsbDevicesByHostLocalIdResponse.UsbDevices, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdResponse) GetUsbDevices() []QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice {
	return v.UsbDevices
}

// QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice includes the requested fields of the GraphQL type UsbDevice.
type QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice struct {
	Name   string                                                 `json:"name"`
	Status UsbDeviceStatus                                        `json:"status"`
	Host   QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost    `json:"host"`
	Vms    []QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm `json:"vms"`
}

// GetName returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice.Name, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice) GetName() string { return v.Name }

// GetStatus returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice.Status, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice) GetStatus() UsbDeviceStatus {
	return v.Status
}

// GetHost returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice.Host, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice) GetHost() QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost {
	return v.Host
}

// GetVms returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice.Vms, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDevice) GetVms() []QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm {
	return v.Vms
}

// QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost includes the requested fields of the GraphQL type Host.
type QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost struct {
	Name     string `json:"name"`
	Local_id string `json:"local_id"`
}

// GetName returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost.Name, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost) GetName() string { return v.Name }

// GetLocal_id returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost.Local_id, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceHost) GetLocal_id() string { return v.Local_id }

// QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm includes the requested fields of the GraphQL type Vm.
type QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm struct {
	Local_id       string `json:"local_id"`
	Name           string `json:"name"`
	In_recycle_bin bool   `json:"in_recycle_bin"`
}

// GetLocal_id returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm.Local_id, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm) GetLocal_id() string {
	return v.Local_id
}

// GetName returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm.Name, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm) GetName() string { return v.Name }

// GetIn_recycle_bin returns QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm.In_recycle_bin, and is useful for accessing the field via an interface.
func (v *QueryUsbDevicesByHostLocalIdUsbDevicesUsbDeviceVmsVm) GetIn_recycle_bin() bool {
	return v.In_recycle_bin
}

// QueryUserIDByUserNameResponse is returned by QueryUserIDByUserName on success.
type QueryUserIDByUserNameResponse struct {
	Users []QueryUserIDByUserNameUsersUser `json:"users"`
}

// GetUsers returns QueryUserIDByUserNameResponse.Users, and is useful for accessing the field via an interface.
func (v *QueryUserIDByUserNameResponse) GetUsers() []QueryUserIDByUserNameUsersUser { return v.Users }

// QueryUserIDByUserNameUsersUser includes the requested fields of the GraphQL type User.
type QueryUserIDByUserNameUsersUser struct {
	Id string `json:"id"`
}

// GetId returns QueryUserIDByUserNameUsersUser.Id, and is useful for accessing the field via an interface.
func (v *QueryUserIDByUserNameUsersUser) GetId() string { return v.Id }

// QueryVlansByIdsResponse is returned by QueryVlansByIds on success.
type QueryVlansByIdsResponse struct {
	Vlans []QueryVlansByIdsVlansVlan `json:"vlans"`
}

// GetVlans returns QueryVlansByIdsResponse.Vlans, and is useful for accessing the field via an interface.
func (v *QueryVlansByIdsResponse) GetVlans() []QueryVlansByIdsVlansVlan { return v.Vlans }

// QueryVlansByIdsVlansVlan includes the requested fields of the GraphQL type Vlan.
type QueryVlansByIdsVlansVlan struct {
	Id      string `json:"id"`
	Vlan_id int    `json:"vlan_id"`
	Name    string `json:"name"`
}

// GetId returns QueryVlansByIdsVlansVlan.Id, and is useful for accessing the field via an interface.
func (v *QueryVlansByIdsVlansVlan) GetId() string { return v.Id }

// GetVlan_id returns QueryVlansByIdsVlansVlan.Vlan_id, and is useful for accessing the field via an interface.
func (v *QueryVlansByIdsVlansVlan) GetVlan_id() int { return v.Vlan_id }

// GetName returns QueryVlansByIdsVlansVlan.Name, and is useful for accessing the field via an interface.
func (v *QueryVlansByIdsVlansVlan) GetName() string { return v.Name }

type TaskStatus string

const (
	TaskStatusExecuting TaskStatus = "EXECUTING"
	TaskStatusFailed    TaskStatus = "FAILED"
	TaskStatusPaused    TaskStatus = "PAUSED"
	TaskStatusPending   TaskStatus = "PENDING"
	TaskStatusSuccessed TaskStatus = "SUCCESSED"
)

// UpdateHostPluginResponse is returned by UpdateHostPlugin on success.
type UpdateHostPluginResponse struct {
	UpdateHostPlugin UpdateHostPluginUpdateHostPlugin `json:"updateHostPlugin"`
}

// GetUpdateHostPlugin returns UpdateHostPluginResponse.UpdateHostPlugin, and is useful for accessing the field via an interface.
func (v *UpdateHostPluginResponse) GetUpdateHostPlugin() UpdateHostPluginUpdateHostPlugin {
	return v.UpdateHostPlugin
}

// UpdateHostPluginUpdateHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type UpdateHostPluginUpdateHostPlugin struct {
	Id string `json:"id"`
}

// GetId returns UpdateHostPluginUpdateHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *UpdateHostPluginUpdateHostPlugin) GetId() string { return v.Id }

// UpdateTaskResponse is returned by UpdateTask on success.
type UpdateTaskResponse struct {
	UpdateTask UpdateTaskUpdateTask `json:"updateTask"`
}

// GetUpdateTask returns UpdateTaskResponse.UpdateTask, and is useful for accessing the field via an interface.
func (v *UpdateTaskResponse) GetUpdateTask() UpdateTaskUpdateTask { return v.UpdateTask }

// UpdateTaskUpdateTask includes the requested fields of the GraphQL type Task.
type UpdateTaskUpdateTask struct {
	Id     string     `json:"id"`
	Status TaskStatus `json:"status"`
}

// GetId returns UpdateTaskUpdateTask.Id, and is useful for accessing the field via an interface.
func (v *UpdateTaskUpdateTask) GetId() string { return v.Id }

// GetStatus returns UpdateTaskUpdateTask.Status, and is useful for accessing the field via an interface.
func (v *UpdateTaskUpdateTask) GetStatus() TaskStatus { return v.Status }

// UpdateVmHostResponse is returned by UpdateVmHost on success.
type UpdateVmHostResponse struct {
	UpdateVm UpdateVmHostUpdateVm `json:"updateVm"`
}

// GetUpdateVm returns UpdateVmHostResponse.UpdateVm, and is useful for accessing the field via an interface.
func (v *UpdateVmHostResponse) GetUpdateVm() UpdateVmHostUpdateVm { return v.UpdateVm }

// UpdateVmHostUpdateVm includes the requested fields of the GraphQL type Vm.
type UpdateVmHostUpdateVm struct {
	Id             string   `json:"id"`
	Local_id       string   `json:"local_id"`
	Name           string   `json:"name"`
	Status         VmStatus `json:"status"`
	Hostname       string   `json:"hostname"`
	In_recycle_bin bool     `json:"in_recycle_bin"`
}

// GetId returns UpdateVmHostUpdateVm.Id, and is useful for accessing the field via an interface.
func (v *UpdateVmHostUpdateVm) GetId() string { return v.Id }

// GetLocal_id returns UpdateVmHostUpdateVm.Local_id, and is useful for accessing the field via an interface.
func (v *UpdateVmHostUpdateVm) GetLocal_id() string { return v.Local_id }

// GetName returns UpdateVmHostUpdateVm.Name, and is useful for accessing the field via an interface.
func (v *UpdateVmHostUpdateVm) GetName() string { return v.Name }

// GetStatus returns UpdateVmHostUpdateVm.Status, and is useful for accessing the field via an interface.
func (v *UpdateVmHostUpdateVm) GetStatus() VmStatus { return v.Status }

// GetHostname returns UpdateVmHostUpdateVm.Hostname, and is useful for accessing the field via an interface.
func (v *UpdateVmHostUpdateVm) GetHostname() string { return v.Hostname }

// GetIn_recycle_bin returns UpdateVmHostUpdateVm.In_recycle_bin, and is useful for accessing the field via an interface.
func (v *UpdateVmHostUpdateVm) GetIn_recycle_bin() bool { return v.In_recycle_bin }

// UpgradeHostPluginResponse is returned by UpgradeHostPlugin on success.
type UpgradeHostPluginResponse struct {
	UpdateHostPlugin UpgradeHostPluginUpdateHostPlugin `json:"updateHostPlugin"`
}

// GetUpdateHostPlugin returns UpgradeHostPluginResponse.UpdateHostPlugin, and is useful for accessing the field via an interface.
func (v *UpgradeHostPluginResponse) GetUpdateHostPlugin() UpgradeHostPluginUpdateHostPlugin {
	return v.UpdateHostPlugin
}

// UpgradeHostPluginUpdateHostPlugin includes the requested fields of the GraphQL type HostPlugin.
type UpgradeHostPluginUpdateHostPlugin struct {
	Id string `json:"id"`
}

// GetId returns UpgradeHostPluginUpdateHostPlugin.Id, and is useful for accessing the field via an interface.
func (v *UpgradeHostPluginUpdateHostPlugin) GetId() string { return v.Id }

type UsbDeviceStatus string

const (
	UsbDeviceStatusEjected UsbDeviceStatus = "EJECTED"
	UsbDeviceStatusNormal  UsbDeviceStatus = "NORMAL"
)

type UserAuditLogStatus string

const (
	UserAuditLogStatusFailed    UserAuditLogStatus = "FAILED"
	UserAuditLogStatusSuccessed UserAuditLogStatus = "SUCCESSED"
)

type UserSource string

const (
	UserSourceAuthn UserSource = "AUTHN"
	UserSourceLdap  UserSource = "LDAP"
	UserSourceLocal UserSource = "LOCAL"
)

type VmStatus string

const (
	VmStatusDeleted   VmStatus = "DELETED"
	VmStatusRunning   VmStatus = "RUNNING"
	VmStatusStopped   VmStatus = "STOPPED"
	VmStatusSuspended VmStatus = "SUSPENDED"
	VmStatusUnknown   VmStatus = "UNKNOWN"
)

type VmUsage string

const (
	VmUsageAdvancedMonitoring    VmUsage = "ADVANCED_MONITORING"
	VmUsageAgentMeshNode         VmUsage = "AGENT_MESH_NODE"
	VmUsageBackupController      VmUsage = "BACKUP_CONTROLLER"
	VmUsageBundleApplication     VmUsage = "BUNDLE_APPLICATION"
	VmUsageCloudtower            VmUsage = "CLOUDTOWER"
	VmUsageEverouteController    VmUsage = "EVEROUTE_CONTROLLER"
	VmUsageRegistry              VmUsage = "REGISTRY"
	VmUsageReplicationController VmUsage = "REPLICATION_CONTROLLER"
	VmUsageSfsController         VmUsage = "SFS_CONTROLLER"
	VmUsageShareRegistry         VmUsage = "SHARE_REGISTRY"
	VmUsageSksManagement         VmUsage = "SKS_MANAGEMENT"
)

// __CreateHostPluginInput is used internally by genqlient
type __CreateHostPluginInput struct {
	Input schema.HostPluginCreateInput `json:"input"`
}

// GetInput returns __CreateHostPluginInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateHostPluginInput) GetInput() schema.HostPluginCreateInput { return v.Input }

// __CreateTaskInput is used internally by genqlient
type __CreateTaskInput struct {
	Input schema.TaskCreateInput `json:"input"`
}

// GetInput returns __CreateTaskInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateTaskInput) GetInput() schema.TaskCreateInput { return v.Input }

// __CreateUserAuditLogInput is used internally by genqlient
type __CreateUserAuditLogInput struct {
	Input schema.UserAuditLogCreateInput `json:"input"`
}

// GetInput returns __CreateUserAuditLogInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateUserAuditLogInput) GetInput() schema.UserAuditLogCreateInput { return v.Input }

// __DeleteHostPluginInput is used internally by genqlient
type __DeleteHostPluginInput struct {
	Id string `json:"id"`
}

// GetId returns __DeleteHostPluginInput.Id, and is useful for accessing the field via an interface.
func (v *__DeleteHostPluginInput) GetId() string { return v.Id }

// __DeleteHostPluginPackageInput is used internally by genqlient
type __DeleteHostPluginPackageInput struct {
	Id string `json:"id"`
}

// GetId returns __DeleteHostPluginPackageInput.Id, and is useful for accessing the field via an interface.
func (v *__DeleteHostPluginPackageInput) GetId() string { return v.Id }

// __DeleteVMByIDInput is used internally by genqlient
type __DeleteVMByIDInput struct {
	Id string `json:"id"`
}

// GetId returns __DeleteVMByIDInput.Id, and is useful for accessing the field via an interface.
func (v *__DeleteVMByIDInput) GetId() string { return v.Id }

// __LoginInput is used internally by genqlient
type __LoginInput struct {
	Login LoginInput `json:"login"`
}

// GetLogin returns __LoginInput.Login, and is useful for accessing the field via an interface.
func (v *__LoginInput) GetLogin() LoginInput { return v.Login }

// __QueryClusterInfoByIdInput is used internally by genqlient
type __QueryClusterInfoByIdInput struct {
	Id string `json:"id"`
}

// GetId returns __QueryClusterInfoByIdInput.Id, and is useful for accessing the field via an interface.
func (v *__QueryClusterInfoByIdInput) GetId() string { return v.Id }

// __QueryClusterInfoByLocalIdInput is used internally by genqlient
type __QueryClusterInfoByLocalIdInput struct {
	Local_id string `json:"local_id"`
}

// GetLocal_id returns __QueryClusterInfoByLocalIdInput.Local_id, and is useful for accessing the field via an interface.
func (v *__QueryClusterInfoByLocalIdInput) GetLocal_id() string { return v.Local_id }

// __QueryClusterVMsStatusInput is used internally by genqlient
type __QueryClusterVMsStatusInput struct {
	Id string `json:"id"`
}

// GetId returns __QueryClusterVMsStatusInput.Id, and is useful for accessing the field via an interface.
func (v *__QueryClusterVMsStatusInput) GetId() string { return v.Id }

// __QueryClusterWitnessStatusInput is used internally by genqlient
type __QueryClusterWitnessStatusInput struct {
	Local_id string `json:"local_id"`
}

// GetLocal_id returns __QueryClusterWitnessStatusInput.Local_id, and is useful for accessing the field via an interface.
func (v *__QueryClusterWitnessStatusInput) GetLocal_id() string { return v.Local_id }

// __QueryClustersByIdsInput is used internally by genqlient
type __QueryClustersByIdsInput struct {
	Ids []string `json:"ids"`
}

// GetIds returns __QueryClustersByIdsInput.Ids, and is useful for accessing the field via an interface.
func (v *__QueryClustersByIdsInput) GetIds() []string { return v.Ids }

// __QueryClustersByLocalIdsInput is used internally by genqlient
type __QueryClustersByLocalIdsInput struct {
	Local_ids []string `json:"local_ids"`
}

// GetLocal_ids returns __QueryClustersByLocalIdsInput.Local_ids, and is useful for accessing the field via an interface.
func (v *__QueryClustersByLocalIdsInput) GetLocal_ids() []string { return v.Local_ids }

// __QueryHostInfoByIdInput is used internally by genqlient
type __QueryHostInfoByIdInput struct {
	Id string `json:"id"`
}

// GetId returns __QueryHostInfoByIdInput.Id, and is useful for accessing the field via an interface.
func (v *__QueryHostInfoByIdInput) GetId() string { return v.Id }

// __QueryHostInfoByLocalIdInput is used internally by genqlient
type __QueryHostInfoByLocalIdInput struct {
	Local_id string `json:"local_id"`
}

// GetLocal_id returns __QueryHostInfoByLocalIdInput.Local_id, and is useful for accessing the field via an interface.
func (v *__QueryHostInfoByLocalIdInput) GetLocal_id() string { return v.Local_id }

// __QueryHostPluginByIdsInput is used internally by genqlient
type __QueryHostPluginByIdsInput struct {
	Ids []string `json:"ids"`
}

// GetIds returns __QueryHostPluginByIdsInput.Ids, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginByIdsInput) GetIds() []string { return v.Ids }

// __QueryHostPluginPackagesByIdsInput is used internally by genqlient
type __QueryHostPluginPackagesByIdsInput struct {
	Ids []string `json:"ids"`
}

// GetIds returns __QueryHostPluginPackagesByIdsInput.Ids, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginPackagesByIdsInput) GetIds() []string { return v.Ids }

// __QueryHostPluginPackagesByPackageNameInput is used internally by genqlient
type __QueryHostPluginPackagesByPackageNameInput struct {
	Package_name string `json:"package_name"`
}

// GetPackage_name returns __QueryHostPluginPackagesByPackageNameInput.Package_name, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginPackagesByPackageNameInput) GetPackage_name() string { return v.Package_name }

// __QueryHostPluginPackagesInput is used internally by genqlient
type __QueryHostPluginPackagesInput struct {
	Name    string       `json:"name"`
	Version string       `json:"version"`
	Arch    Architecture `json:"arch"`
}

// GetName returns __QueryHostPluginPackagesInput.Name, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginPackagesInput) GetName() string { return v.Name }

// GetVersion returns __QueryHostPluginPackagesInput.Version, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginPackagesInput) GetVersion() string { return v.Version }

// GetArch returns __QueryHostPluginPackagesInput.Arch, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginPackagesInput) GetArch() Architecture { return v.Arch }

// __QueryHostPluginsByPackageNameInput is used internally by genqlient
type __QueryHostPluginsByPackageNameInput struct {
	Package_name string `json:"package_name"`
}

// GetPackage_name returns __QueryHostPluginsByPackageNameInput.Package_name, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginsByPackageNameInput) GetPackage_name() string { return v.Package_name }

// __QueryHostPluginsInput is used internally by genqlient
type __QueryHostPluginsInput struct {
	Cluster         string       `json:"cluster"`
	Package_name    string       `json:"package_name"`
	Package_version string       `json:"package_version"`
	Package_arch    Architecture `json:"package_arch"`
}

// GetCluster returns __QueryHostPluginsInput.Cluster, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginsInput) GetCluster() string { return v.Cluster }

// GetPackage_name returns __QueryHostPluginsInput.Package_name, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginsInput) GetPackage_name() string { return v.Package_name }

// GetPackage_version returns __QueryHostPluginsInput.Package_version, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginsInput) GetPackage_version() string { return v.Package_version }

// GetPackage_arch returns __QueryHostPluginsInput.Package_arch, and is useful for accessing the field via an interface.
func (v *__QueryHostPluginsInput) GetPackage_arch() Architecture { return v.Package_arch }

// __QueryHostVMsByHostLocalIDInput is used internally by genqlient
type __QueryHostVMsByHostLocalIDInput struct {
	Local_id string `json:"local_id"`
}

// GetLocal_id returns __QueryHostVMsByHostLocalIDInput.Local_id, and is useful for accessing the field via an interface.
func (v *__QueryHostVMsByHostLocalIDInput) GetLocal_id() string { return v.Local_id }

// __QueryInRecycleBinVMsByClusterIDInput is used internally by genqlient
type __QueryInRecycleBinVMsByClusterIDInput struct {
	Id string `json:"id"`
}

// GetId returns __QueryInRecycleBinVMsByClusterIDInput.Id, and is useful for accessing the field via an interface.
func (v *__QueryInRecycleBinVMsByClusterIDInput) GetId() string { return v.Id }

// __QueryInstalledHostPluginPackagesByPackageNameInput is used internally by genqlient
type __QueryInstalledHostPluginPackagesByPackageNameInput struct {
	Package_name string `json:"package_name"`
}

// GetPackage_name returns __QueryInstalledHostPluginPackagesByPackageNameInput.Package_name, and is useful for accessing the field via an interface.
func (v *__QueryInstalledHostPluginPackagesByPackageNameInput) GetPackage_name() string {
	return v.Package_name
}

// __QueryPlacementGroupsByHostLocalIDInput is used internally by genqlient
type __QueryPlacementGroupsByHostLocalIDInput struct {
	Host_local_id string `json:"host_local_id"`
}

// GetHost_local_id returns __QueryPlacementGroupsByHostLocalIDInput.Host_local_id, and is useful for accessing the field via an interface.
func (v *__QueryPlacementGroupsByHostLocalIDInput) GetHost_local_id() string { return v.Host_local_id }

// __QuerySpecificHostPluginsByClustersInput is used internally by genqlient
type __QuerySpecificHostPluginsByClustersInput struct {
	Namespace   string   `json:"namespace"`
	Cluster_ids []string `json:"cluster_ids"`
}

// GetNamespace returns __QuerySpecificHostPluginsByClustersInput.Namespace, and is useful for accessing the field via an interface.
func (v *__QuerySpecificHostPluginsByClustersInput) GetNamespace() string { return v.Namespace }

// GetCluster_ids returns __QuerySpecificHostPluginsByClustersInput.Cluster_ids, and is useful for accessing the field via an interface.
func (v *__QuerySpecificHostPluginsByClustersInput) GetCluster_ids() []string { return v.Cluster_ids }

// __QuerySystemVMsByHostIDInput is used internally by genqlient
type __QuerySystemVMsByHostIDInput struct {
	Host_id string `json:"host_id"`
}

// GetHost_id returns __QuerySystemVMsByHostIDInput.Host_id, and is useful for accessing the field via an interface.
func (v *__QuerySystemVMsByHostIDInput) GetHost_id() string { return v.Host_id }

// __QueryTaskByIdInput is used internally by genqlient
type __QueryTaskByIdInput struct {
	Id string `json:"id"`
}

// GetId returns __QueryTaskByIdInput.Id, and is useful for accessing the field via an interface.
func (v *__QueryTaskByIdInput) GetId() string { return v.Id }

// __QueryTaskStartedAtInput is used internally by genqlient
type __QueryTaskStartedAtInput struct {
	Id string `json:"id"`
}

// GetId returns __QueryTaskStartedAtInput.Id, and is useful for accessing the field via an interface.
func (v *__QueryTaskStartedAtInput) GetId() string { return v.Id }

// __QueryUsbDevicesByHostLocalIdInput is used internally by genqlient
type __QueryUsbDevicesByHostLocalIdInput struct {
	Host_local_id string `json:"host_local_id"`
}

// GetHost_local_id returns __QueryUsbDevicesByHostLocalIdInput.Host_local_id, and is useful for accessing the field via an interface.
func (v *__QueryUsbDevicesByHostLocalIdInput) GetHost_local_id() string { return v.Host_local_id }

// __QueryUserIDByUserNameInput is used internally by genqlient
type __QueryUserIDByUserNameInput struct {
	Username string `json:"username"`
}

// GetUsername returns __QueryUserIDByUserNameInput.Username, and is useful for accessing the field via an interface.
func (v *__QueryUserIDByUserNameInput) GetUsername() string { return v.Username }

// __QueryVlansByIdsInput is used internally by genqlient
type __QueryVlansByIdsInput struct {
	Ids []string `json:"ids"`
}

// GetIds returns __QueryVlansByIdsInput.Ids, and is useful for accessing the field via an interface.
func (v *__QueryVlansByIdsInput) GetIds() []string { return v.Ids }

// __UpdateHostPluginInput is used internally by genqlient
type __UpdateHostPluginInput struct {
	Id     string   `json:"id"`
	Values []string `json:"values"`
}

// GetId returns __UpdateHostPluginInput.Id, and is useful for accessing the field via an interface.
func (v *__UpdateHostPluginInput) GetId() string { return v.Id }

// GetValues returns __UpdateHostPluginInput.Values, and is useful for accessing the field via an interface.
func (v *__UpdateHostPluginInput) GetValues() []string { return v.Values }

// __UpdateTaskInput is used internally by genqlient
type __UpdateTaskInput struct {
	Id    string                 `json:"id"`
	Input schema.TaskUpdateInput `json:"input"`
}

// GetId returns __UpdateTaskInput.Id, and is useful for accessing the field via an interface.
func (v *__UpdateTaskInput) GetId() string { return v.Id }

// GetInput returns __UpdateTaskInput.Input, and is useful for accessing the field via an interface.
func (v *__UpdateTaskInput) GetInput() schema.TaskUpdateInput { return v.Input }

// __UpdateVmHostInput is used internally by genqlient
type __UpdateVmHostInput struct {
	Id      string `json:"id"`
	Node_ip string `json:"node_ip"`
}

// GetId returns __UpdateVmHostInput.Id, and is useful for accessing the field via an interface.
func (v *__UpdateVmHostInput) GetId() string { return v.Id }

// GetNode_ip returns __UpdateVmHostInput.Node_ip, and is useful for accessing the field via an interface.
func (v *__UpdateVmHostInput) GetNode_ip() string { return v.Node_ip }

// __UpgradeHostPluginInput is used internally by genqlient
type __UpgradeHostPluginInput struct {
	Id             string   `json:"id"`
	Target_package string   `json:"target_package"`
	Values         []string `json:"values"`
}

// GetId returns __UpgradeHostPluginInput.Id, and is useful for accessing the field via an interface.
func (v *__UpgradeHostPluginInput) GetId() string { return v.Id }

// GetTarget_package returns __UpgradeHostPluginInput.Target_package, and is useful for accessing the field via an interface.
func (v *__UpgradeHostPluginInput) GetTarget_package() string { return v.Target_package }

// GetValues returns __UpgradeHostPluginInput.Values, and is useful for accessing the field via an interface.
func (v *__UpgradeHostPluginInput) GetValues() []string { return v.Values }

func CreateHostPlugin(
	ctx context.Context,
	client graphql.Client,
	input schema.HostPluginCreateInput,
) (*CreateHostPluginResponse, error) {
	req := &graphql.Request{
		OpName: "CreateHostPlugin",
		Query: `
mutation CreateHostPlugin ($input: HostPluginCreateInput!) {
	createHostPlugin(data: $input) {
		id
	}
}
`,
		Variables: &__CreateHostPluginInput{
			Input: input,
		},
	}
	var err error

	var data CreateHostPluginResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func CreateTask(
	ctx context.Context,
	client graphql.Client,
	input schema.TaskCreateInput,
) (*CreateTaskResponse, error) {
	req := &graphql.Request{
		OpName: "CreateTask",
		Query: `
mutation CreateTask ($input: TaskCreateInput!) {
	createTask(data: $input) {
		id
		status
	}
}
`,
		Variables: &__CreateTaskInput{
			Input: input,
		},
	}
	var err error

	var data CreateTaskResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func CreateUserAuditLog(
	ctx context.Context,
	client graphql.Client,
	input schema.UserAuditLogCreateInput,
) (*CreateUserAuditLogResponse, error) {
	req := &graphql.Request{
		OpName: "CreateUserAuditLog",
		Query: `
mutation CreateUserAuditLog ($input: UserAuditLogCreateInput!) {
	createUserAuditLog(data: $input) {
		id
		status
	}
}
`,
		Variables: &__CreateUserAuditLogInput{
			Input: input,
		},
	}
	var err error

	var data CreateUserAuditLogResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func DeleteHostPlugin(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*DeleteHostPluginResponse, error) {
	req := &graphql.Request{
		OpName: "DeleteHostPlugin",
		Query: `
mutation DeleteHostPlugin ($id: ID!) {
	deleteHostPlugin(where: {id:$id}) {
		id
	}
}
`,
		Variables: &__DeleteHostPluginInput{
			Id: id,
		},
	}
	var err error

	var data DeleteHostPluginResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func DeleteHostPluginPackage(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*DeleteHostPluginPackageResponse, error) {
	req := &graphql.Request{
		OpName: "DeleteHostPluginPackage",
		Query: `
mutation DeleteHostPluginPackage ($id: ID!) {
	deleteHostPluginPackage(where: {id:$id}) {
		id
	}
}
`,
		Variables: &__DeleteHostPluginPackageInput{
			Id: id,
		},
	}
	var err error

	var data DeleteHostPluginPackageResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func DeleteVMByID(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*DeleteVMByIDResponse, error) {
	req := &graphql.Request{
		OpName: "DeleteVMByID",
		Query: `
mutation DeleteVMByID ($id: ID!) {
	deleteVm(where: {id:$id}) {
		id
		name
		in_recycle_bin
	}
}
`,
		Variables: &__DeleteVMByIDInput{
			Id: id,
		},
	}
	var err error

	var data DeleteVMByIDResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func Login(
	ctx context.Context,
	client graphql.Client,
	login LoginInput,
) (*LoginResponse, error) {
	req := &graphql.Request{
		OpName: "Login",
		Query: `
mutation Login ($login: LoginInput!) {
	login(data: $login) {
		token
	}
}
`,
		Variables: &__LoginInput{
			Login: login,
		},
	}
	var err error

	var data LoginResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryCloudTowerVersion(
	ctx context.Context,
	client graphql.Client,
) (*QueryCloudTowerVersionResponse, error) {
	req := &graphql.Request{
		OpName: "QueryCloudTowerVersion",
		Query: `
query QueryCloudTowerVersion {
	deploys(first: 1) {
		version
	}
}
`,
	}
	var err error

	var data QueryCloudTowerVersionResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClusterInfoById(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*QueryClusterInfoByIdResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClusterInfoById",
		Query: `
query QueryClusterInfoById ($id: ID!) {
	cluster(where: {id:$id}) {
		name
		id
		local_id
		architecture
		cpu_vendor
		ip
		management_vip
		connect_state
		type
		hypervisor
		version
		patch_version
		upgrade_tool_version
		hosts {
			id
			name
			scvm_name
			local_id
			data_ip
			management_ip
			state
			connect_status
			merged_status
			os_version
			role
			chunk_id
			gpu_devices {
				user_usage
			}
		}
		witness {
			id
			name
			local_id
			data_ip
			management_ip
		}
	}
}
`,
		Variables: &__QueryClusterInfoByIdInput{
			Id: id,
		},
	}
	var err error

	var data QueryClusterInfoByIdResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClusterInfoByLocalId(
	ctx context.Context,
	client graphql.Client,
	local_id string,
) (*QueryClusterInfoByLocalIdResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClusterInfoByLocalId",
		Query: `
query QueryClusterInfoByLocalId ($local_id: String!) {
	cluster(where: {local_id:$local_id}) {
		name
		id
		local_id
		architecture
		cpu_vendor
		ip
		management_vip
		connect_state
		type
		hypervisor
		version
		patch_version
		upgrade_tool_version
		stretch
		hosts {
			id
			name
			scvm_name
			local_id
			data_ip
			management_ip
			connect_status
			merged_status
			os_version
			role
			zone {
				is_preferred
			}
			chunk_id
			state
			status
			merged_status
			connect_status
			gpu_devices {
				user_usage
			}
		}
		witness {
			id
			name
			local_id
			data_ip
			management_ip
		}
		ntp_servers
	}
}
`,
		Variables: &__QueryClusterInfoByLocalIdInput{
			Local_id: local_id,
		},
	}
	var err error

	var data QueryClusterInfoByLocalIdResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClusterVMsStatus(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*QueryClusterVMsStatusResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClusterVMsStatus",
		Query: `
query QueryClusterVMsStatus ($id: ID!) {
	clusters(where: {id:$id}) {
		id
		vms {
			id
			name
			status
			host {
				id
				name
			}
		}
	}
}
`,
		Variables: &__QueryClusterVMsStatusInput{
			Id: id,
		},
	}
	var err error

	var data QueryClusterVMsStatusResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClusterWitnessStatus(
	ctx context.Context,
	client graphql.Client,
	local_id string,
) (*QueryClusterWitnessStatusResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClusterWitnessStatus",
		Query: `
query QueryClusterWitnessStatus ($local_id: String) {
	cluster(where: {local_id:$local_id}) {
		id
		local_id
		name
		stretch
		witness {
			id
			name
			local_id
			data_ip
			management_ip
		}
		metro_availability_checklist {
			witness {
				status
			}
		}
	}
}
`,
		Variables: &__QueryClusterWitnessStatusInput{
			Local_id: local_id,
		},
	}
	var err error

	var data QueryClusterWitnessStatusResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClustersByIds(
	ctx context.Context,
	client graphql.Client,
	ids []string,
) (*QueryClustersByIdsResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClustersByIds",
		Query: `
query QueryClustersByIds ($ids: [ID!]) {
	clusters(where: {id_in:$ids}) {
		id
		local_id
		ip
		architecture
		version
		name
		hosts {
			id
			local_id
			name
			scvm_name
			management_ip
			data_ip
			state
			status
			merged_status
			connect_status
			host_state {
				state
			}
		}
		applications {
			id
			type
			version
		}
	}
}
`,
		Variables: &__QueryClustersByIdsInput{
			Ids: ids,
		},
	}
	var err error

	var data QueryClustersByIdsResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClustersByLocalIds(
	ctx context.Context,
	client graphql.Client,
	local_ids []string,
) (*QueryClustersByLocalIdsResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClustersByLocalIds",
		Query: `
query QueryClustersByLocalIds ($local_ids: [String!]) {
	clusters(where: {local_id_in:$local_ids}) {
		id
		local_id
		ip
		type
		architecture
		version
		name
		hypervisor
		hosts {
			id
			local_id
			name
			scvm_name
			management_ip
			data_ip
			state
			status
			merged_status
			connect_status
			host_state {
				state
			}
		}
	}
}
`,
		Variables: &__QueryClustersByLocalIdsInput{
			Local_ids: local_ids,
		},
	}
	var err error

	var data QueryClustersByLocalIdsResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryClustersInfo(
	ctx context.Context,
	client graphql.Client,
) (*QueryClustersInfoResponse, error) {
	req := &graphql.Request{
		OpName: "QueryClustersInfo",
		Query: `
query QueryClustersInfo {
	clusters {
		name
		id
		local_id
		architecture
		cpu_vendor
		ip
		management_vip
		connect_state
		type
		hypervisor
		version
		patch_version
		upgrade_tool_version
		hosts {
			id
			name
			scvm_name
			local_id
			data_ip
			management_ip
			state
			merged_status
			connect_status
			host_state {
				state
			}
			chunk_id
			os_version
			role
			gpu_devices {
				user_usage
			}
		}
		witness {
			id
			name
			local_id
			data_ip
			management_ip
		}
	}
}
`,
	}
	var err error

	var data QueryClustersInfoResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostInfoById(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*QueryHostInfoByIdResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostInfoById",
		Query: `
query QueryHostInfoById ($id: ID!) {
	host(where: {id:$id}) {
		id
		name
		scvm_name
		local_id
		data_ip
		management_ip
		connect_status
		merged_status
		chunk_id
		role
		state
		status
		merged_status
		connect_status
	}
}
`,
		Variables: &__QueryHostInfoByIdInput{
			Id: id,
		},
	}
	var err error

	var data QueryHostInfoByIdResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostInfoByLocalId(
	ctx context.Context,
	client graphql.Client,
	local_id string,
) (*QueryHostInfoByLocalIdResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostInfoByLocalId",
		Query: `
query QueryHostInfoByLocalId ($local_id: String!) {
	host(where: {local_id:$local_id}) {
		id
		name
		scvm_name
		local_id
		data_ip
		management_ip
		connect_status
		merged_status
		chunk_id
		role
		state
		status
		merged_status
		connect_status
	}
}
`,
		Variables: &__QueryHostInfoByLocalIdInput{
			Local_id: local_id,
		},
	}
	var err error

	var data QueryHostInfoByLocalIdResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostPluginByIds(
	ctx context.Context,
	client graphql.Client,
	ids []string,
) (*QueryHostPluginByIdsResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostPluginByIds",
		Query: `
query QueryHostPluginByIds ($ids: [ID!]) {
	hostPlugins(where: {id_in:$ids}) {
		id
		name
		namespace
		values
		cluster {
			id
			name
		}
		host_plugin_package {
			id
			name
			version
			arch
			size
		}
		host_plugin_instances
		hosts {
			management_ip
		}
	}
}
`,
		Variables: &__QueryHostPluginByIdsInput{
			Ids: ids,
		},
	}
	var err error

	var data QueryHostPluginByIdsResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostPluginPackages(
	ctx context.Context,
	client graphql.Client,
	name string,
	version string,
	arch Architecture,
) (*QueryHostPluginPackagesResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostPluginPackages",
		Query: `
query QueryHostPluginPackages ($name: String!, $version: String!, $arch: Architecture!) {
	hostPluginPackages(where: {name:$name,version:$version,arch:$arch}) {
		id
		name
		version
		arch
		size
	}
}
`,
		Variables: &__QueryHostPluginPackagesInput{
			Name:    name,
			Version: version,
			Arch:    arch,
		},
	}
	var err error

	var data QueryHostPluginPackagesResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostPluginPackagesByIds(
	ctx context.Context,
	client graphql.Client,
	ids []string,
) (*QueryHostPluginPackagesByIdsResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostPluginPackagesByIds",
		Query: `
query QueryHostPluginPackagesByIds ($ids: [ID!]) {
	hostPluginPackages(where: {id_in:$ids}) {
		id
		name
		version
		arch
		size
	}
}
`,
		Variables: &__QueryHostPluginPackagesByIdsInput{
			Ids: ids,
		},
	}
	var err error

	var data QueryHostPluginPackagesByIdsResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostPluginPackagesByPackageName(
	ctx context.Context,
	client graphql.Client,
	package_name string,
) (*QueryHostPluginPackagesByPackageNameResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostPluginPackagesByPackageName",
		Query: `
query QueryHostPluginPackagesByPackageName ($package_name: String!) {
	hostPluginPackages(where: {name:$package_name}) {
		id
		name
		version
		arch
		size
	}
}
`,
		Variables: &__QueryHostPluginPackagesByPackageNameInput{
			Package_name: package_name,
		},
	}
	var err error

	var data QueryHostPluginPackagesByPackageNameResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostPlugins(
	ctx context.Context,
	client graphql.Client,
	cluster string,
	package_name string,
	package_version string,
	package_arch Architecture,
) (*QueryHostPluginsResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostPlugins",
		Query: `
query QueryHostPlugins ($cluster: ID!, $package_name: String!, $package_version: String!, $package_arch: Architecture!) {
	hostPlugins(where: {cluster:{id:$cluster},host_plugin_package:{name:$package_name,version:$package_version,arch:$package_arch}}) {
		id
		name
		namespace
		values
		cluster {
			id
			name
		}
		host_plugin_package {
			id
			name
			version
			arch
			size
		}
		host_plugin_instances
		hosts {
			management_ip
		}
	}
}
`,
		Variables: &__QueryHostPluginsInput{
			Cluster:         cluster,
			Package_name:    package_name,
			Package_version: package_version,
			Package_arch:    package_arch,
		},
	}
	var err error

	var data QueryHostPluginsResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostPluginsByPackageName(
	ctx context.Context,
	client graphql.Client,
	package_name string,
) (*QueryHostPluginsByPackageNameResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostPluginsByPackageName",
		Query: `
query QueryHostPluginsByPackageName ($package_name: String!) {
	hostPlugins(where: {host_plugin_package:{name:$package_name}}) {
		host_plugin_package {
			id
			name
			version
			arch
		}
	}
}
`,
		Variables: &__QueryHostPluginsByPackageNameInput{
			Package_name: package_name,
		},
	}
	var err error

	var data QueryHostPluginsByPackageNameResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryHostVMsByHostLocalID(
	ctx context.Context,
	client graphql.Client,
	local_id string,
) (*QueryHostVMsByHostLocalIDResponse, error) {
	req := &graphql.Request{
		OpName: "QueryHostVMsByHostLocalID",
		Query: `
query QueryHostVMsByHostLocalID ($local_id: String) {
	host(where: {local_id:$local_id}) {
		id
		local_id
		name
		vm_num
		vms {
			id
			local_id
			name
			internal
			vm_usage
			in_recycle_bin
		}
	}
}
`,
		Variables: &__QueryHostVMsByHostLocalIDInput{
			Local_id: local_id,
		},
	}
	var err error

	var data QueryHostVMsByHostLocalIDResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryInRecycleBinVMsByClusterID(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*QueryInRecycleBinVMsByClusterIDResponse, error) {
	req := &graphql.Request{
		OpName: "QueryInRecycleBinVMsByClusterID",
		Query: `
query QueryInRecycleBinVMsByClusterID ($id: ID!) {
	vms(where: {in_recycle_bin:true,cluster:{id:$id}}) {
		local_id
	}
}
`,
		Variables: &__QueryInRecycleBinVMsByClusterIDInput{
			Id: id,
		},
	}
	var err error

	var data QueryInRecycleBinVMsByClusterIDResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryInstalledHostPluginPackagesByPackageName(
	ctx context.Context,
	client graphql.Client,
	package_name string,
) (*QueryInstalledHostPluginPackagesByPackageNameResponse, error) {
	req := &graphql.Request{
		OpName: "QueryInstalledHostPluginPackagesByPackageName",
		Query: `
query QueryInstalledHostPluginPackagesByPackageName ($package_name: String!) {
	hostPlugins(where: {host_plugin_package:{name:$package_name}}) {
		host_plugin_package {
			id
		}
	}
}
`,
		Variables: &__QueryInstalledHostPluginPackagesByPackageNameInput{
			Package_name: package_name,
		},
	}
	var err error

	var data QueryInstalledHostPluginPackagesByPackageNameResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryPlacementGroupsByHostLocalID(
	ctx context.Context,
	client graphql.Client,
	host_local_id string,
) (*QueryPlacementGroupsByHostLocalIDResponse, error) {
	req := &graphql.Request{
		OpName: "QueryPlacementGroupsByHostLocalID",
		Query: `
query QueryPlacementGroupsByHostLocalID ($host_local_id: String!) {
	vmPlacementGroups(where: {OR:[{vm_host_must_host_uuids_some:{local_id:$host_local_id}},{vm_host_prefer_host_uuids_some:{local_id:$host_local_id}}]}) {
		id
		name
		cluster {
			name
		}
		vm_host_must_host_uuids {
			id
			local_id
			name
		}
		vm_host_prefer_host_uuids {
			id
			local_id
			name
		}
	}
}
`,
		Variables: &__QueryPlacementGroupsByHostLocalIDInput{
			Host_local_id: host_local_id,
		},
	}
	var err error

	var data QueryPlacementGroupsByHostLocalIDResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QuerySpecificHostPluginsByClusters(
	ctx context.Context,
	client graphql.Client,
	namespace string,
	cluster_ids []string,
) (*QuerySpecificHostPluginsByClustersResponse, error) {
	req := &graphql.Request{
		OpName: "QuerySpecificHostPluginsByClusters",
		Query: `
query QuerySpecificHostPluginsByClusters ($namespace: String!, $cluster_ids: [ID!]) {
	hostPlugins(where: {namespace:$namespace,cluster:{id_in:$cluster_ids}}) {
		id
		name
		namespace
		values
		cluster {
			id
			name
		}
		host_plugin_package {
			id
			name
			version
			arch
			size
		}
		host_plugin_instances
		hosts {
			management_ip
		}
	}
}
`,
		Variables: &__QuerySpecificHostPluginsByClustersInput{
			Namespace:   namespace,
			Cluster_ids: cluster_ids,
		},
	}
	var err error

	var data QuerySpecificHostPluginsByClustersResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QuerySystemVMsByHostID(
	ctx context.Context,
	client graphql.Client,
	host_id string,
) (*QuerySystemVMsByHostIDResponse, error) {
	req := &graphql.Request{
		OpName: "QuerySystemVMsByHostID",
		Query: `
query QuerySystemVMsByHostID ($host_id: ID) {
	hosts(where: {id:$host_id}) {
		id
		local_id
		vms {
			internal
			vm_usage
			local_id
		}
	}
}
`,
		Variables: &__QuerySystemVMsByHostIDInput{
			Host_id: host_id,
		},
	}
	var err error

	var data QuerySystemVMsByHostIDResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryTaskById(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*QueryTaskByIdResponse, error) {
	req := &graphql.Request{
		OpName: "QueryTaskById",
		Query: `
query QueryTaskById ($id: ID!) {
	tasks(where: {id:$id}) {
		id
		status
		args
		progress
		started_at
		finished_at
		description
		error_code
		error_message
		resource_id
		resource_mutation
		steps {
			key
			finished
		}
	}
}
`,
		Variables: &__QueryTaskByIdInput{
			Id: id,
		},
	}
	var err error

	var data QueryTaskByIdResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryTaskStartedAt(
	ctx context.Context,
	client graphql.Client,
	id string,
) (*QueryTaskStartedAtResponse, error) {
	req := &graphql.Request{
		OpName: "QueryTaskStartedAt",
		Query: `
query QueryTaskStartedAt ($id: ID!) {
	tasks(where: {id:$id}) {
		started_at
	}
}
`,
		Variables: &__QueryTaskStartedAtInput{
			Id: id,
		},
	}
	var err error

	var data QueryTaskStartedAtResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryUpgradeCenterTasks(
	ctx context.Context,
	client graphql.Client,
) (*QueryUpgradeCenterTasksResponse, error) {
	req := &graphql.Request{
		OpName: "QueryUpgradeCenterTasks",
		Query: `
query QueryUpgradeCenterTasks {
	tasks(where: {resource_type:"upgradeCenterTask"}) {
		id
		status
		started_at
		finished_at
		resource_mutation
	}
}
`,
	}
	var err error

	var data QueryUpgradeCenterTasksResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryUsbDevicesByHostLocalId(
	ctx context.Context,
	client graphql.Client,
	host_local_id string,
) (*QueryUsbDevicesByHostLocalIdResponse, error) {
	req := &graphql.Request{
		OpName: "QueryUsbDevicesByHostLocalId",
		Query: `
query QueryUsbDevicesByHostLocalId ($host_local_id: String!) {
	usbDevices(where: {host:{local_id:$host_local_id}}) {
		name
		status
		host {
			name
			local_id
		}
		vms(where: {in_recycle_bin:false}) {
			local_id
			name
			in_recycle_bin
		}
	}
}
`,
		Variables: &__QueryUsbDevicesByHostLocalIdInput{
			Host_local_id: host_local_id,
		},
	}
	var err error

	var data QueryUsbDevicesByHostLocalIdResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryUserIDByUserName(
	ctx context.Context,
	client graphql.Client,
	username string,
) (*QueryUserIDByUserNameResponse, error) {
	req := &graphql.Request{
		OpName: "QueryUserIDByUserName",
		Query: `
query QueryUserIDByUserName ($username: String!) {
	users(where: {username:$username}, first: 1) {
		id
	}
}
`,
		Variables: &__QueryUserIDByUserNameInput{
			Username: username,
		},
	}
	var err error

	var data QueryUserIDByUserNameResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func QueryVlansByIds(
	ctx context.Context,
	client graphql.Client,
	ids []string,
) (*QueryVlansByIdsResponse, error) {
	req := &graphql.Request{
		OpName: "QueryVlansByIds",
		Query: `
query QueryVlansByIds ($ids: [ID!]) {
	vlans(where: {id_in:$ids}) {
		id
		vlan_id
		name
	}
}
`,
		Variables: &__QueryVlansByIdsInput{
			Ids: ids,
		},
	}
	var err error

	var data QueryVlansByIdsResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func UpdateHostPlugin(
	ctx context.Context,
	client graphql.Client,
	id string,
	values []string,
) (*UpdateHostPluginResponse, error) {
	req := &graphql.Request{
		OpName: "UpdateHostPlugin",
		Query: `
mutation UpdateHostPlugin ($id: ID!, $values: [String!]) {
	updateHostPlugin(data: {values:{set:$values}}, where: {id:$id}) {
		id
	}
}
`,
		Variables: &__UpdateHostPluginInput{
			Id:     id,
			Values: values,
		},
	}
	var err error

	var data UpdateHostPluginResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func UpdateTask(
	ctx context.Context,
	client graphql.Client,
	id string,
	input schema.TaskUpdateInput,
) (*UpdateTaskResponse, error) {
	req := &graphql.Request{
		OpName: "UpdateTask",
		Query: `
mutation UpdateTask ($id: ID!, $input: TaskUpdateInput!) {
	updateTask(data: $input, where: {id:$id}) {
		id
		status
	}
}
`,
		Variables: &__UpdateTaskInput{
			Id:    id,
			Input: input,
		},
	}
	var err error

	var data UpdateTaskResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func UpdateVmHost(
	ctx context.Context,
	client graphql.Client,
	id string,
	node_ip string,
) (*UpdateVmHostResponse, error) {
	req := &graphql.Request{
		OpName: "UpdateVmHost",
		Query: `
mutation UpdateVmHost ($id: ID!, $node_ip: String) {
	updateVm(data: {node_ip:$node_ip}, effect: {remove_unmovable_devices_before_migrate:true}, where: {id:$id}) {
		id
		local_id
		name
		status
		hostname
		in_recycle_bin
	}
}
`,
		Variables: &__UpdateVmHostInput{
			Id:      id,
			Node_ip: node_ip,
		},
	}
	var err error

	var data UpdateVmHostResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}

func UpgradeHostPlugin(
	ctx context.Context,
	client graphql.Client,
	id string,
	target_package string,
	values []string,
) (*UpgradeHostPluginResponse, error) {
	req := &graphql.Request{
		OpName: "UpgradeHostPlugin",
		Query: `
mutation UpgradeHostPlugin ($id: ID!, $target_package: ID!, $values: [String!]) {
	updateHostPlugin(data: {host_plugin_package:{connect:{id:$target_package}},values:{set:$values}}, where: {id:$id}) {
		id
	}
}
`,
		Variables: &__UpgradeHostPluginInput{
			Id:             id,
			Target_package: target_package,
			Values:         values,
		},
	}
	var err error

	var data UpgradeHostPluginResponse
	resp := &graphql.Response{Data: &data}

	err = client.MakeRequest(
		ctx,
		req,
		resp,
	)

	return &data, err
}
