package schema

import "time"

type HostPluginCreateInput struct {
	Name              string                  `json:"name"`
	Namespace         string                  `json:"namespace"`
	Values            *Values                 `json:"values"`
	HostPluginPackage *HostPluginPackageInput `json:"host_plugin_package"`
	Cluster           *ClusterInput           `json:"cluster"`
	Hosts             *HostInput              `json:"hosts"`
	DisableSelector   bool                    `json:"disable_selector"`
}

type Values struct {
	Set []string `json:"set"`
}

type HostPluginPackageInput struct {
	Connect *Connect `json:"connect"`
}

type Connect struct {
	ID string `json:"id"`
}

type ClusterInput struct {
	Connect *Connect `json:"connect"`
}

type HostInput struct {
	Connect []*Connect `json:"connect"`
}

type UploadTaskCreateInput struct {
	Status       string            `json:"status"`
	ChunkSize    float64           `json:"chunk_size"`
	Size         float64           `json:"size"`
	CurrentChunk float64           `json:"current_chunk"`
	ResourceType string            `json:"resource_type"`
	Args         map[string]string `json:"args"`
	StartedAt    time.Time         `json:"started_at"`
}

type TaskCreateInput struct {
	Internal         bool          `json:"internal"`
	Description      string        `json:"description"`
	Status           string        `json:"status"`
	Args             interface{}   `json:"args"`
	Steps            []*Step       `json:"steps"`
	Snapshot         string        `json:"snapshot"`
	ResourceType     string        `json:"resource_type"`
	ResourceMutation string        `json:"resource_mutation"`
	User             *UserInput    `json:"user"`
	Cluster          *ClusterInput `json:"cluster"`
	Progress         float64       `json:"progress"`
	LocalCreatedAt   *time.Time    `json:"local_created_at"`
	StartedAt        *time.Time    `json:"started_at"`
	FinishedAt       *time.Time    `json:"finished_at,omitempty"`
}

type Step struct {
	Key      string `json:"key"`
	Finished bool   `json:"finished"`
}

type UserInput struct {
	Connect *Connect `json:"connect"`
}

type TaskUpdateInput struct {
	Progress         float64     `json:"progress,omitempty"`
	Status           string      `json:"status,omitempty"`
	Description      string      `json:"description,omitempty"`
	Steps            []*Step     `json:"steps,omitempty"`
	Args             interface{} `json:"args"`
	ErrorCode        string      `json:"error_code,omitempty"`
	ErrorMessage     string      `json:"error_message,omitempty"`
	ResourceMutation string      `json:"resource_mutation,omitempty"`
	FinishedAt       *time.Time  `json:"finished_at,omitempty"`
}

type HostPluginInstance struct {
	ID      string `json:"id"`
	Healthy bool   `json:"healthy"`
}

type ConnectTo struct {
	Connect    *Connect `json:"connect,omitempty"`
	Disconnect bool     `json:"disconnect,omitempty"`
}

type ConnectMany struct {
	Connect []*Connect `json:"connect"`
}

type UserAuditLogCreateInput struct {
	Action       string     `json:"action"`
	Message      string     `json:"message"`
	Status       string     `json:"status"`
	StartedAt    time.Time  `json:"started_at"`
	FinishedAt   time.Time  `json:"finished_at"`
	Cluster      *ConnectTo `json:"cluster,omitempty"`
	User         *ConnectTo `json:"user,omitempty"`
	IPAddress    string     `json:"ip_address"`
	ResourceType string     `json:"resource_type"`
	ResourceID   string     `json:"resource_id"`
}
