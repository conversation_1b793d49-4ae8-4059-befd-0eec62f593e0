// Package version implements version handling code.
package version

import (
	"fmt"
	"runtime"
)

var (
	gitMajor     string // major version, always numeric
	gitMinor     string // minor version, numeric possibly followed by "+"
	gitVersion   string // semantic version, derived by build scripts
	gitCommit    string // sha1 from git, output of $(git rev-parse HEAD)
	gitTreeState string // state of git tree, either "clean" or "dirty"
	buildDate    string // build date in ISO8601 format, output of $(date -u +'%Y-%m-%dT%H:%M:%SZ')
)

// Info exposes information about the version used for the current running code.
type Info struct {
	Major string `json:"major,omitempty" example:"1"`
	Minor string `json:"minor,omitempty" example:"0"`
	// release version generate by 'git describe --tags'
	GitVersion string `json:"gitVersion,omitempty" example:"v1.0.0-alpha.1-38-g44bd8db"`
	// git commit id
	GitCommit    string `json:"gitCommit,omitempty" example:"a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0"`
	GitTreeState string `json:"gitTreeState,omitempty" example:"clean"`
	// app build date and time
	BuildDate string `json:"buildDate,omitempty" example:"2024-01-08T00:59:36Z"`
	GoVersion string `json:"goVersion,omitempty" example:"gogo1.21.5"`
	Compiler  string `json:"compiler,omitempty" example:"gc"`
	Platform  string `json:"platform,omitempty" example:"linux/amd64"`
}

// Get returns an Info object with all the information about the current running code.
func Get() Info {
	return Info{
		Major:        gitMajor,
		Minor:        gitMinor,
		GitVersion:   gitVersion,
		GitCommit:    gitCommit,
		GitTreeState: gitTreeState,
		BuildDate:    buildDate,
		GoVersion:    runtime.Version(),
		Compiler:     runtime.Compiler,
		Platform:     fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}

// String returns info as a human-friendly version string.
func (info Info) String() string {
	return info.GitVersion
}
