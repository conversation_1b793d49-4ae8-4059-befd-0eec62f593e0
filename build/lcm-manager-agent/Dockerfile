ARG base_image
ARG golang_builder_image

FROM ${golang_builder_image} as builder
WORKDIR /go/src/github.smartx.com/LCM/lcm-manager
ARG goprivate="github.smartx.com,192.168.50.150,newgh.smartx.com"
ARG goproxy="https://goproxy.cn,direct"
ENV GOPROXY=${goproxy}
ENV CGO_ENABLED=0
ENV GOPRIVATE=${goprivate}
ENV GOINSECURE=${goprivate}
ENV GONOSUMDB=${goprivate}

COPY . .

ARG ldflags

# Do not force rebuild of up-to-date packages (do not use -a) and use the compiler cache folder
RUN --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux \
    go build -mod vendor -trimpath -ldflags "-s -w ${ldflags} -extldflags '-static'" \
    -o /go/bin/lcm-manager-agent main.go

COPY build/lcm-manager-agent/*.sh /go/bin/
RUN chmod +x /go/bin/*

FROM $base_image
WORKDIR /app
COPY --from=builder /go/bin/ .
COPY build/lcm-manager-agent/ssh_config /root/.ssh/config
