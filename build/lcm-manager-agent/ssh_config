Match host * exec "grep '^admin:' /sysroot/etc/passwd"
  User admin
  IdentityFile /home/<USER>/.ssh/admin_id_rsa
  StrictHostKeyChecking no
  ServerAliveInterval 10
  ServerAliveCountMax 3
  ConnectTimeout=10
  UserKnownHostsFile /dev/null
  GSSAPIAuthentication no
  LogLevel ERROR

Match host * exec "grep '^smartx:' /sysroot/etc/passwd"
  User smartx
  IdentityFile /home/<USER>/.ssh/smartx_id_rsa
  StrictHostKeyChecking no
  ServerAliveInterval 10
  ServerAliveCountMax 3
  ConnectTimeout=10
  UserKnownHostsFile /dev/null
  GSSAPIAuthentication no
  LogLevel ERROR
