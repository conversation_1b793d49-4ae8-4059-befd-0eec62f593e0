#!/usr/bin/env bash
set -o errexit
set -o pipefail
set -o nounset
set -o xtrace

# write nginx config to host rootfs and reload nginx
cat << EOF > /sysroot/etc/nginx/default.d/lcm_manager_agent.conf
location ^~ /api/v1/tasks {
    proxy_pass http://127.0.0.1:10420;
    proxy_set_header Host \$http_host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_read_timeout 300s;

    add_header 'Access-Control-Allow-Headers' 'X-Requested-With, Content-Type, Cache-Control, Pragma, If-Modified-Since';
    add_header Access-Control-Allow-Origin *;
    break;
}
EOF

OUTPUT=$(ssh "${DATA_IP}" "sudo nginx -t > /dev/null && echo -n success")
if [[ "${OUTPUT}" == "success" ]];then
    echo "nginx config check success"
    ssh "${DATA_IP}" "sudo systemctl reload nginx"
else
    echo "nginx config check failed"
    rm -f /sysroot/etc/nginx/default.d/lcm_manager_agent.conf
    exit 1
fi
