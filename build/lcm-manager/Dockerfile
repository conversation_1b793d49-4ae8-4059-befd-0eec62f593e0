ARG base_image
ARG golang_builder_image
ARG host_plugin_pkgs_image

FROM ${golang_builder_image} as builder
WORKDIR /go/src/github.smartx.com/LCM/lcm-manager
ARG goprivate="github.smartx.com,**************,newgh.smartx.com"
ARG goproxy="http://goproxy.smartx.com,direct"
ENV GOPROXY=${goproxy}
ENV CGO_ENABLED=0
ENV GOPRIVATE=${goprivate}
ENV GOINSECURE=${goprivate}
ENV GONOSUMDB=${goprivate}

COPY . .

ARG ldflags

# Do not force rebuild of up-to-date packages (do not use -a) and use the compiler cache folder
RUN --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux \
    go build -mod vendor -ldflags "-s -w ${ldflags} -extldflags '-static'" \
    -o /go/bin/lcm-manager main.go

RUN chmod +x /go/bin/*

FROM $host_plugin_pkgs_image as pkgs

FROM $base_image
WORKDIR /app
COPY --from=builder /go/bin/ /app
COPY --from=pkgs / /app/pkgs
ENV HPO_PKGS_DIR=/app/pkgs
