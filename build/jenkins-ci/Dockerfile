ARG golang_builder_image
FROM ${golang_builder_image} as tools
WORKDIR /build

ARG goprivate="github.smartx.com,192.168.50.150,newgh.smartx.com"
ARG goproxy="https://goproxy.cn,direct"
ENV GOPROXY=${goproxy}
ENV CGO_ENABLED=0
ENV GOPRIVATE=${goprivate}
ENV GOINSECURE=${goprivate}
ENV GONOSUMDB=${goprivate}

COPY go.mod Makefile .
RUN make dev-tools

FROM docker.io/library/rust:bullseye as typos-builder
ARG TAG=v1.19.0
ARG REPO=https://github.com/crate-ci/typos.git
RUN cargo install --git ${REPO} --tag ${TAG}

ARG golang_builder_image
FROM ${golang_builder_image}
COPY --from=tools /go/bin /go/bin
COPY --from=typos-builder /usr/local/cargo/bin/typos /usr/local/bin/typos
COPY build/jenkins-ci/buildx.json /root/.docker/buildx/instances/kube
COPY --from=docker.io/library/docker:20.10.12-dind-rootless /usr/local/bin/docker /usr/local/bin/docker
COPY --from=docker.io/docker/buildx-bin:v0.10 /buildx /usr/libexec/docker/cli-plugins/docker-buildx
COPY --from=docker.io/dtzar/helm-kubectl:3.8.0 /usr/local/bin /usr/local/bin

WORKDIR /build

ARG goprivate="github.smartx.com,192.168.50.150,newgh.smartx.com"
ARG goproxy="https://goproxy.cn,direct"
ENV GOPROXY=${goproxy}
ENV CGO_ENABLED=0
ENV GOPRIVATE=${goprivate}
ENV GOINSECURE=${goprivate}
ENV GONOSUMDB=${goprivate}
