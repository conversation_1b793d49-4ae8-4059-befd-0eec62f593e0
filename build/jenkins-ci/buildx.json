{"Name": "kube", "Driver": "kubernetes", "Nodes": [{"Name": "builder-amd64", "Endpoint": "kubernetes:///kube?deployment=builder-amd64", "Platforms": [{"architecture": "amd64", "os": "linux"}], "Flags": null, "DriverOpts": {"namespace": "buildkit", "nodeselector": "kubernetes.io/arch=amd64", "replicas": "4"}, "Files": null}, {"Name": "builder-arm64", "Endpoint": "kubernetes:///kube?deployment=builder-arm64", "Platforms": [{"architecture": "arm64", "os": "linux"}], "Flags": null, "DriverOpts": {"namespace": "buildkit", "nodeselector": "kubernetes.io/arch=arm64", "replicas": "4"}, "Files": null}], "Dynamic": false}