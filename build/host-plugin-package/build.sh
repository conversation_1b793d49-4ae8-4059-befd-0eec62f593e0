#!/bin/bash
set -o errexit
set -o pipefail
set -o nounset

HPO_PKG_NAME=lcm-manager-agent

CURRENT_DIR=$(cd "$(dirname "$0")"; pwd)
INPUT_DIR=/opt/input
OUTPUT_DIR=/opt/output
ARTIFACTS_DIR="${CURRENT_DIR}"/artifacts
BUILD_ARCHITECTURE=$(echo -n "${ARCHITECTURE}" | sed "s/amd64/X86_64/g;s/arm64/AARCH64/g")
PKG_NAME=${HPO_PKG_NAME}-${LCM_MANAGER_AGENT_VERSION}-${ARCHITECTURE}.tgz

rm -rf "${INPUT_DIR}" "${OUTPUT_DIR}"
mkdir -p "${INPUT_DIR}" "${OUTPUT_DIR}" "${ARTIFACTS_DIR}"

cp template.tmpl "${INPUT_DIR}"/template.tmpl

cat << EOF > "${INPUT_DIR}"/manifest.yaml
name: ${HPO_PKG_NAME}
version: ${LCM_MANAGER_AGENT_VERSION}
architecture: ${BUILD_ARCHITECTURE}
EOF

cat << EOF > "${INPUT_DIR}"/values.yaml
lcm_manager_agent_image: ${LCM_MANAGER_AGENT_IMAGE}
EOF

cat << EOF > "${INPUT_DIR}"/images.list
${LCM_MANAGER_AGENT_IMAGE}
EOF

cat ${INPUT_DIR}/{manifest.yaml,values.yaml,images.list}

/usr/local/bin/docker-entrypoint.sh -s "${INPUT_DIR}" -o "${OUTPUT_DIR}" -p "${ARCHITECTURE}"
find "${OUTPUT_DIR}" -name "*.tgz" -exec mv {} "${ARTIFACTS_DIR}/${PKG_NAME}" \;

if ! grep '^pkgs:$' "${ARTIFACTS_DIR}"/pkgs.yaml; then
  echo "pkgs:" > "${ARTIFACTS_DIR}"/pkgs.yaml
fi

cat << EOF >> "${ARTIFACTS_DIR}"/pkgs.yaml
- name: ${HPO_PKG_NAME}
  version: ${LCM_MANAGER_AGENT_VERSION}
  architecture: ${BUILD_ARCHITECTURE}
  filename: ${PKG_NAME}
  md5sum: $(md5sum "${ARTIFACTS_DIR}/${PKG_NAME}" | awk '{print $1}')
  build_date: $(date '+%Y-%m-%d %H:%M:%S')
EOF
