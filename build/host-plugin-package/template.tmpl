init_containers:
- name: init
  image: {{ .values.lcm_manager_agent_image }}
  mounts:
  - destination: /sysroot
    source: /
  - destination: /home
    source: /home
  process:
    command: "/bin/bash"
    args: ["-c", "/app/init.sh"]
    env:
    - DATA_IP={{ .host_info.storage_ip }}

containers:
- name: lcm-manager-agent
  image: {{ .values.lcm_manager_agent_image }}
  mounts:
  - destination: /sysroot
    source: /
  - destination: /tmp
    source: /tmp
  - destination: /etc/zbs
    source: /etc/zbs
  - destination: /home
    source: /home
  process:
    command: "/app/lcm-manager-agent"
    args: ["agent"]
    log_path: /var/log/lcm/lcm-manager-agent.log
    working_dir: /app
    env:
    - DATA_IP={{ .host_info.storage_ip }}
    - AGENT_LISTEN_ADDR=127.0.0.1
    - AGENT_LISTEN_PORT=10420

clean_containers:
- name: cleanup
  image: {{ .values.lcm_manager_agent_image }}
  mounts:
    - destination: /sysroot
      source: /
  process:
    command: "/bin/bash"
    args: ["-c", "/app/cleanup.sh"]
    env:
    - DATA_IP={{ .host_info.storage_ip }}
