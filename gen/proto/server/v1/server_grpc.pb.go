// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: proto/server/v1/server.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Manager_ConvertRoleCheck_FullMethodName       = "/lcm.manager.protobuf.Manager/ConvertRoleCheck"
	Manager_ConvertRoleCheckResult_FullMethodName = "/lcm.manager.protobuf.Manager/ConvertRoleCheckResult"
	Manager_ConvertRole_FullMethodName            = "/lcm.manager.protobuf.Manager/ConvertRole"
	Manager_RemoveHostCheck_FullMethodName        = "/lcm.manager.protobuf.Manager/RemoveHostCheck"
	Manager_RemoveHostCheckResult_FullMethodName  = "/lcm.manager.protobuf.Manager/RemoveHostCheckResult"
	Manager_RemoveHost_FullMethodName             = "/lcm.manager.protobuf.Manager/RemoveHost"
	Manager_TimeSync_FullMethodName               = "/lcm.manager.protobuf.Manager/TimeSync"
	Manager_SetRDMA_FullMethodName                = "/lcm.manager.protobuf.Manager/SetRDMA"
	Manager_ListJobs_FullMethodName               = "/lcm.manager.protobuf.Manager/ListJobs"
	Manager_GetJob_FullMethodName                 = "/lcm.manager.protobuf.Manager/GetJob"
	Manager_GetJobLogs_FullMethodName             = "/lcm.manager.protobuf.Manager/GetJobLogs"
	Manager_GetSupportedActions_FullMethodName    = "/lcm.manager.protobuf.Manager/GetSupportedActions"
	Manager_Healthz_FullMethodName                = "/lcm.manager.protobuf.Manager/Healthz"
)

// ManagerClient is the client API for Manager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ManagerClient interface {
	// 角色转换前置检查
	ConvertRoleCheck(ctx context.Context, in *ConvertRoleCheckRequest, opts ...grpc.CallOption) (*ConvertRoleCheckResponse, error)
	// 获取角色转换前置检查结果
	ConvertRoleCheckResult(ctx context.Context, in *CheckResultRequest, opts ...grpc.CallOption) (*CheckResultResponse, error)
	// 执行角色转换
	ConvertRole(ctx context.Context, in *ConvertRoleRequest, opts ...grpc.CallOption) (*ConvertRoleResponse, error)
	// 主机移除前置检查
	RemoveHostCheck(ctx context.Context, in *RemoveHostCheckRequest, opts ...grpc.CallOption) (*RemoveHostCheckResponse, error)
	// 获取主机移除前置检查结果
	RemoveHostCheckResult(ctx context.Context, in *CheckResultRequest, opts ...grpc.CallOption) (*CheckResultResponse, error)
	// 执行主机移除
	RemoveHost(ctx context.Context, in *RemoveHostRequest, opts ...grpc.CallOption) (*RemoveHostResponse, error)
	// 同步集群时间
	TimeSync(ctx context.Context, in *TimeSyncRequest, opts ...grpc.CallOption) (*TimeSyncResponse, error)
	// 开启或关闭 RDMA
	SetRDMA(ctx context.Context, in *SetRDMARequest, opts ...grpc.CallOption) (*SetRDMAResponse, error)
	// 列出所有 Jobs
	ListJobs(ctx context.Context, in *ListJobsRequest, opts ...grpc.CallOption) (*ListJobsResponse, error)
	// 获取 Job 详情
	GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*Job, error)
	// 获取 Job 日志
	GetJobLogs(ctx context.Context, in *GetJobLogsRequest, opts ...grpc.CallOption) (*JobLogs, error)
	// 获取支持集群变更白名单
	GetSupportedActions(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*AvailabilityMap, error)
	// 健康检查
	Healthz(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HealthzResponse, error)
}

type managerClient struct {
	cc grpc.ClientConnInterface
}

func NewManagerClient(cc grpc.ClientConnInterface) ManagerClient {
	return &managerClient{cc}
}

func (c *managerClient) ConvertRoleCheck(ctx context.Context, in *ConvertRoleCheckRequest, opts ...grpc.CallOption) (*ConvertRoleCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertRoleCheckResponse)
	err := c.cc.Invoke(ctx, Manager_ConvertRoleCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) ConvertRoleCheckResult(ctx context.Context, in *CheckResultRequest, opts ...grpc.CallOption) (*CheckResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckResultResponse)
	err := c.cc.Invoke(ctx, Manager_ConvertRoleCheckResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) ConvertRole(ctx context.Context, in *ConvertRoleRequest, opts ...grpc.CallOption) (*ConvertRoleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertRoleResponse)
	err := c.cc.Invoke(ctx, Manager_ConvertRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) RemoveHostCheck(ctx context.Context, in *RemoveHostCheckRequest, opts ...grpc.CallOption) (*RemoveHostCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveHostCheckResponse)
	err := c.cc.Invoke(ctx, Manager_RemoveHostCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) RemoveHostCheckResult(ctx context.Context, in *CheckResultRequest, opts ...grpc.CallOption) (*CheckResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckResultResponse)
	err := c.cc.Invoke(ctx, Manager_RemoveHostCheckResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) RemoveHost(ctx context.Context, in *RemoveHostRequest, opts ...grpc.CallOption) (*RemoveHostResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveHostResponse)
	err := c.cc.Invoke(ctx, Manager_RemoveHost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) TimeSync(ctx context.Context, in *TimeSyncRequest, opts ...grpc.CallOption) (*TimeSyncResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeSyncResponse)
	err := c.cc.Invoke(ctx, Manager_TimeSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) SetRDMA(ctx context.Context, in *SetRDMARequest, opts ...grpc.CallOption) (*SetRDMAResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetRDMAResponse)
	err := c.cc.Invoke(ctx, Manager_SetRDMA_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) ListJobs(ctx context.Context, in *ListJobsRequest, opts ...grpc.CallOption) (*ListJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobsResponse)
	err := c.cc.Invoke(ctx, Manager_ListJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*Job, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Job)
	err := c.cc.Invoke(ctx, Manager_GetJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) GetJobLogs(ctx context.Context, in *GetJobLogsRequest, opts ...grpc.CallOption) (*JobLogs, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobLogs)
	err := c.cc.Invoke(ctx, Manager_GetJobLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) GetSupportedActions(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*AvailabilityMap, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AvailabilityMap)
	err := c.cc.Invoke(ctx, Manager_GetSupportedActions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerClient) Healthz(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HealthzResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthzResponse)
	err := c.cc.Invoke(ctx, Manager_Healthz_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ManagerServer is the server API for Manager service.
// All implementations must embed UnimplementedManagerServer
// for forward compatibility.
type ManagerServer interface {
	// 角色转换前置检查
	ConvertRoleCheck(context.Context, *ConvertRoleCheckRequest) (*ConvertRoleCheckResponse, error)
	// 获取角色转换前置检查结果
	ConvertRoleCheckResult(context.Context, *CheckResultRequest) (*CheckResultResponse, error)
	// 执行角色转换
	ConvertRole(context.Context, *ConvertRoleRequest) (*ConvertRoleResponse, error)
	// 主机移除前置检查
	RemoveHostCheck(context.Context, *RemoveHostCheckRequest) (*RemoveHostCheckResponse, error)
	// 获取主机移除前置检查结果
	RemoveHostCheckResult(context.Context, *CheckResultRequest) (*CheckResultResponse, error)
	// 执行主机移除
	RemoveHost(context.Context, *RemoveHostRequest) (*RemoveHostResponse, error)
	// 同步集群时间
	TimeSync(context.Context, *TimeSyncRequest) (*TimeSyncResponse, error)
	// 开启或关闭 RDMA
	SetRDMA(context.Context, *SetRDMARequest) (*SetRDMAResponse, error)
	// 列出所有 Jobs
	ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error)
	// 获取 Job 详情
	GetJob(context.Context, *GetJobRequest) (*Job, error)
	// 获取 Job 日志
	GetJobLogs(context.Context, *GetJobLogsRequest) (*JobLogs, error)
	// 获取支持集群变更白名单
	GetSupportedActions(context.Context, *emptypb.Empty) (*AvailabilityMap, error)
	// 健康检查
	Healthz(context.Context, *emptypb.Empty) (*HealthzResponse, error)
	mustEmbedUnimplementedManagerServer()
}

// UnimplementedManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedManagerServer struct{}

func (UnimplementedManagerServer) ConvertRoleCheck(context.Context, *ConvertRoleCheckRequest) (*ConvertRoleCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertRoleCheck not implemented")
}
func (UnimplementedManagerServer) ConvertRoleCheckResult(context.Context, *CheckResultRequest) (*CheckResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertRoleCheckResult not implemented")
}
func (UnimplementedManagerServer) ConvertRole(context.Context, *ConvertRoleRequest) (*ConvertRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertRole not implemented")
}
func (UnimplementedManagerServer) RemoveHostCheck(context.Context, *RemoveHostCheckRequest) (*RemoveHostCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveHostCheck not implemented")
}
func (UnimplementedManagerServer) RemoveHostCheckResult(context.Context, *CheckResultRequest) (*CheckResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveHostCheckResult not implemented")
}
func (UnimplementedManagerServer) RemoveHost(context.Context, *RemoveHostRequest) (*RemoveHostResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveHost not implemented")
}
func (UnimplementedManagerServer) TimeSync(context.Context, *TimeSyncRequest) (*TimeSyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TimeSync not implemented")
}
func (UnimplementedManagerServer) SetRDMA(context.Context, *SetRDMARequest) (*SetRDMAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRDMA not implemented")
}
func (UnimplementedManagerServer) ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJobs not implemented")
}
func (UnimplementedManagerServer) GetJob(context.Context, *GetJobRequest) (*Job, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJob not implemented")
}
func (UnimplementedManagerServer) GetJobLogs(context.Context, *GetJobLogsRequest) (*JobLogs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobLogs not implemented")
}
func (UnimplementedManagerServer) GetSupportedActions(context.Context, *emptypb.Empty) (*AvailabilityMap, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportedActions not implemented")
}
func (UnimplementedManagerServer) Healthz(context.Context, *emptypb.Empty) (*HealthzResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthz not implemented")
}
func (UnimplementedManagerServer) mustEmbedUnimplementedManagerServer() {}
func (UnimplementedManagerServer) testEmbeddedByValue()                 {}

// UnsafeManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ManagerServer will
// result in compilation errors.
type UnsafeManagerServer interface {
	mustEmbedUnimplementedManagerServer()
}

func RegisterManagerServer(s grpc.ServiceRegistrar, srv ManagerServer) {
	// If the following call pancis, it indicates UnimplementedManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Manager_ServiceDesc, srv)
}

func _Manager_ConvertRoleCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertRoleCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).ConvertRoleCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_ConvertRoleCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).ConvertRoleCheck(ctx, req.(*ConvertRoleCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_ConvertRoleCheckResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).ConvertRoleCheckResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_ConvertRoleCheckResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).ConvertRoleCheckResult(ctx, req.(*CheckResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_ConvertRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).ConvertRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_ConvertRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).ConvertRole(ctx, req.(*ConvertRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_RemoveHostCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveHostCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).RemoveHostCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_RemoveHostCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).RemoveHostCheck(ctx, req.(*RemoveHostCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_RemoveHostCheckResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).RemoveHostCheckResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_RemoveHostCheckResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).RemoveHostCheckResult(ctx, req.(*CheckResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_RemoveHost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveHostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).RemoveHost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_RemoveHost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).RemoveHost(ctx, req.(*RemoveHostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_TimeSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeSyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).TimeSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_TimeSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).TimeSync(ctx, req.(*TimeSyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_SetRDMA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRDMARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).SetRDMA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_SetRDMA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).SetRDMA(ctx, req.(*SetRDMARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_ListJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).ListJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_ListJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).ListJobs(ctx, req.(*ListJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_GetJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).GetJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_GetJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).GetJob(ctx, req.(*GetJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_GetJobLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).GetJobLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_GetJobLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).GetJobLogs(ctx, req.(*GetJobLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_GetSupportedActions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).GetSupportedActions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_GetSupportedActions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).GetSupportedActions(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Manager_Healthz_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServer).Healthz(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Manager_Healthz_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServer).Healthz(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Manager_ServiceDesc is the grpc.ServiceDesc for Manager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Manager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "lcm.manager.protobuf.Manager",
	HandlerType: (*ManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ConvertRoleCheck",
			Handler:    _Manager_ConvertRoleCheck_Handler,
		},
		{
			MethodName: "ConvertRoleCheckResult",
			Handler:    _Manager_ConvertRoleCheckResult_Handler,
		},
		{
			MethodName: "ConvertRole",
			Handler:    _Manager_ConvertRole_Handler,
		},
		{
			MethodName: "RemoveHostCheck",
			Handler:    _Manager_RemoveHostCheck_Handler,
		},
		{
			MethodName: "RemoveHostCheckResult",
			Handler:    _Manager_RemoveHostCheckResult_Handler,
		},
		{
			MethodName: "RemoveHost",
			Handler:    _Manager_RemoveHost_Handler,
		},
		{
			MethodName: "TimeSync",
			Handler:    _Manager_TimeSync_Handler,
		},
		{
			MethodName: "SetRDMA",
			Handler:    _Manager_SetRDMA_Handler,
		},
		{
			MethodName: "ListJobs",
			Handler:    _Manager_ListJobs_Handler,
		},
		{
			MethodName: "GetJob",
			Handler:    _Manager_GetJob_Handler,
		},
		{
			MethodName: "GetJobLogs",
			Handler:    _Manager_GetJobLogs_Handler,
		},
		{
			MethodName: "GetSupportedActions",
			Handler:    _Manager_GetSupportedActions_Handler,
		},
		{
			MethodName: "Healthz",
			Handler:    _Manager_Healthz_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/server/v1/server.proto",
}
