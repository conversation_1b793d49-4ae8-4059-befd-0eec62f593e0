// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: proto/server/v1/server.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HostRole int32

const (
	HostRole_ROLE_UNSPECIFIED HostRole = 0
	HostRole_ROLE_MASTER      HostRole = 1
	HostRole_ROLE_STORAGE     HostRole = 2
)

// Enum value maps for HostRole.
var (
	HostRole_name = map[int32]string{
		0: "ROLE_UNSPECIFIED",
		1: "ROLE_MASTER",
		2: "ROLE_STORAGE",
	}
	HostRole_value = map[string]int32{
		"ROLE_UNSPECIFIED": 0,
		"ROLE_MASTER":      1,
		"ROLE_STORAGE":     2,
	}
)

func (x HostRole) Enum() *HostRole {
	p := new(HostRole)
	*p = x
	return p
}

func (x HostRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostRole) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[0].Descriptor()
}

func (HostRole) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[0]
}

func (x HostRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostRole.Descriptor instead.
func (HostRole) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{0}
}

type RDMAState int32

const (
	RDMAState_RDMA_UNKNOWN RDMAState = 0
	RDMAState_RDMA_ON      RDMAState = 1
	RDMAState_RDMA_OFF     RDMAState = 2
)

// Enum value maps for RDMAState.
var (
	RDMAState_name = map[int32]string{
		0: "RDMA_UNKNOWN",
		1: "RDMA_ON",
		2: "RDMA_OFF",
	}
	RDMAState_value = map[string]int32{
		"RDMA_UNKNOWN": 0,
		"RDMA_ON":      1,
		"RDMA_OFF":     2,
	}
)

func (x RDMAState) Enum() *RDMAState {
	p := new(RDMAState)
	*p = x
	return p
}

func (x RDMAState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RDMAState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[1].Descriptor()
}

func (RDMAState) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[1]
}

func (x RDMAState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RDMAState.Descriptor instead.
func (RDMAState) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{1}
}

type JobState int32

const (
	JobState_JOB_STATE_UNSPECIFIED JobState = 0
	JobState_JOB_STATE_PENDING     JobState = 1
	JobState_JOB_STATE_RUNNING     JobState = 2
	JobState_JOB_STATE_SUCCESS     JobState = 3
	JobState_JOB_STATE_FAILED      JobState = 4
)

// Enum value maps for JobState.
var (
	JobState_name = map[int32]string{
		0: "JOB_STATE_UNSPECIFIED",
		1: "JOB_STATE_PENDING",
		2: "JOB_STATE_RUNNING",
		3: "JOB_STATE_SUCCESS",
		4: "JOB_STATE_FAILED",
	}
	JobState_value = map[string]int32{
		"JOB_STATE_UNSPECIFIED": 0,
		"JOB_STATE_PENDING":     1,
		"JOB_STATE_RUNNING":     2,
		"JOB_STATE_SUCCESS":     3,
		"JOB_STATE_FAILED":      4,
	}
)

func (x JobState) Enum() *JobState {
	p := new(JobState)
	*p = x
	return p
}

func (x JobState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[2].Descriptor()
}

func (JobState) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[2]
}

func (x JobState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobState.Descriptor instead.
func (JobState) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{2}
}

type ErrorCode int32

const (
	ErrorCode_EC_UNSPECIFIED          ErrorCode = 0
	ErrorCode_SECOND_PRE_CHECK_FAILED ErrorCode = 1
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0: "EC_UNSPECIFIED",
		1: "SECOND_PRE_CHECK_FAILED",
	}
	ErrorCode_value = map[string]int32{
		"EC_UNSPECIFIED":          0,
		"SECOND_PRE_CHECK_FAILED": 1,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[3].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[3]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{3}
}

type CheckState int32

const (
	CheckState_CHECK_STATE_UNSPECIFIED CheckState = 0
	CheckState_CHECK_STATE_PENDING     CheckState = 1
	CheckState_CHECK_STATE_RUNNING     CheckState = 2
	CheckState_CHECK_STATE_SUCCESS     CheckState = 3
	CheckState_CHECK_STATE_FAILED      CheckState = 4
	CheckState_CHECK_STATE_WARNING     CheckState = 5
)

// Enum value maps for CheckState.
var (
	CheckState_name = map[int32]string{
		0: "CHECK_STATE_UNSPECIFIED",
		1: "CHECK_STATE_PENDING",
		2: "CHECK_STATE_RUNNING",
		3: "CHECK_STATE_SUCCESS",
		4: "CHECK_STATE_FAILED",
		5: "CHECK_STATE_WARNING",
	}
	CheckState_value = map[string]int32{
		"CHECK_STATE_UNSPECIFIED": 0,
		"CHECK_STATE_PENDING":     1,
		"CHECK_STATE_RUNNING":     2,
		"CHECK_STATE_SUCCESS":     3,
		"CHECK_STATE_FAILED":      4,
		"CHECK_STATE_WARNING":     5,
	}
)

func (x CheckState) Enum() *CheckState {
	p := new(CheckState)
	*p = x
	return p
}

func (x CheckState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[4].Descriptor()
}

func (CheckState) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[4]
}

func (x CheckState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckState.Descriptor instead.
func (CheckState) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{4}
}

type MessageLang int32

const (
	MessageLang_LANG_UNSPECIFIED MessageLang = 0
	MessageLang_LANG_EN          MessageLang = 1
	MessageLang_LANG_ZH          MessageLang = 2
)

// Enum value maps for MessageLang.
var (
	MessageLang_name = map[int32]string{
		0: "LANG_UNSPECIFIED",
		1: "LANG_EN",
		2: "LANG_ZH",
	}
	MessageLang_value = map[string]int32{
		"LANG_UNSPECIFIED": 0,
		"LANG_EN":          1,
		"LANG_ZH":          2,
	}
)

func (x MessageLang) Enum() *MessageLang {
	p := new(MessageLang)
	*p = x
	return p
}

func (x MessageLang) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageLang) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[5].Descriptor()
}

func (MessageLang) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[5]
}

func (x MessageLang) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageLang.Descriptor instead.
func (MessageLang) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{5}
}

type ProductName int32

const (
	ProductName_PRODUCT_UNSPECIFIED ProductName = 0
	ProductName_SMTXOS              ProductName = 1
	ProductName_SMTXZBS             ProductName = 2
	ProductName_SMTXELF             ProductName = 3
)

// Enum value maps for ProductName.
var (
	ProductName_name = map[int32]string{
		0: "PRODUCT_UNSPECIFIED",
		1: "SMTXOS",
		2: "SMTXZBS",
		3: "SMTXELF",
	}
	ProductName_value = map[string]int32{
		"PRODUCT_UNSPECIFIED": 0,
		"SMTXOS":              1,
		"SMTXZBS":             2,
		"SMTXELF":             3,
	}
)

func (x ProductName) Enum() *ProductName {
	p := new(ProductName)
	*p = x
	return p
}

func (x ProductName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductName) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[6].Descriptor()
}

func (ProductName) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[6]
}

func (x ProductName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductName.Descriptor instead.
func (ProductName) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{6}
}

type ActionType int32

const (
	ActionType_ACTION_TYPE_UNSPECIFIED ActionType = 0
	ActionType_CONVERT_ROLE            ActionType = 1
	ActionType_REMOVE_HOST             ActionType = 2
	ActionType_TIME_SYNC               ActionType = 3
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "CONVERT_ROLE",
		2: "REMOVE_HOST",
		3: "TIME_SYNC",
	}
	ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED": 0,
		"CONVERT_ROLE":            1,
		"REMOVE_HOST":             2,
		"TIME_SYNC":               3,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[7].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[7]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{7}
}

type ActionFeature int32

const (
	ActionFeature_ACTION_FEATURE_UNSPECIFIED  ActionFeature = 0
	ActionFeature_SUPPORT_MULTI_OFFLINE_HOSTS ActionFeature = 1
)

// Enum value maps for ActionFeature.
var (
	ActionFeature_name = map[int32]string{
		0: "ACTION_FEATURE_UNSPECIFIED",
		1: "SUPPORT_MULTI_OFFLINE_HOSTS",
	}
	ActionFeature_value = map[string]int32{
		"ACTION_FEATURE_UNSPECIFIED":  0,
		"SUPPORT_MULTI_OFFLINE_HOSTS": 1,
	}
)

func (x ActionFeature) Enum() *ActionFeature {
	p := new(ActionFeature)
	*p = x
	return p
}

func (x ActionFeature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionFeature) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_server_v1_server_proto_enumTypes[8].Descriptor()
}

func (ActionFeature) Type() protoreflect.EnumType {
	return &file_proto_server_v1_server_proto_enumTypes[8]
}

func (x ActionFeature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionFeature.Descriptor instead.
func (ActionFeature) EnumDescriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{8}
}

// ConvertRoleCheckRequest
type ConvertRoleCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Target Host cluster uuid
	ClusterUuid string `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	// Target host uuid
	HostUuid string `protobuf:"bytes,2,opt,name=host_uuid,proto3" json:"host_uuid,omitempty"`
	// client id for idempotent request
	ClientId string `protobuf:"bytes,3,opt,name=client_id,proto3" json:"client_id,omitempty"`
	// current host role
	CurrentRole HostRole `protobuf:"varint,4,opt,name=current_role,proto3,enum=lcm.manager.protobuf.HostRole" json:"current_role,omitempty"`
	// target host role
	TargetRole HostRole `protobuf:"varint,5,opt,name=target_role,proto3,enum=lcm.manager.protobuf.HostRole" json:"target_role,omitempty"`
}

func (x *ConvertRoleCheckRequest) Reset() {
	*x = ConvertRoleCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertRoleCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertRoleCheckRequest) ProtoMessage() {}

func (x *ConvertRoleCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertRoleCheckRequest.ProtoReflect.Descriptor instead.
func (*ConvertRoleCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{0}
}

func (x *ConvertRoleCheckRequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *ConvertRoleCheckRequest) GetHostUuid() string {
	if x != nil {
		return x.HostUuid
	}
	return ""
}

func (x *ConvertRoleCheckRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ConvertRoleCheckRequest) GetCurrentRole() HostRole {
	if x != nil {
		return x.CurrentRole
	}
	return HostRole_ROLE_UNSPECIFIED
}

func (x *ConvertRoleCheckRequest) GetTargetRole() HostRole {
	if x != nil {
		return x.TargetRole
	}
	return HostRole_ROLE_UNSPECIFIED
}

type ConvertRoleCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId    string `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *ConvertRoleCheckResponse) Reset() {
	*x = ConvertRoleCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertRoleCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertRoleCheckResponse) ProtoMessage() {}

func (x *ConvertRoleCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertRoleCheckResponse.ProtoReflect.Descriptor instead.
func (*ConvertRoleCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{1}
}

func (x *ConvertRoleCheckResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ConvertRoleCheckResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type CheckResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterUuid string `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	HostUuid    string `protobuf:"bytes,2,opt,name=host_uuid,proto3" json:"host_uuid,omitempty"`
	ClientId    string `protobuf:"bytes,3,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId       string `protobuf:"bytes,4,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *CheckResultRequest) Reset() {
	*x = CheckResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultRequest) ProtoMessage() {}

func (x *CheckResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultRequest.ProtoReflect.Descriptor instead.
func (*CheckResultRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{2}
}

func (x *CheckResultRequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *CheckResultRequest) GetHostUuid() string {
	if x != nil {
		return x.HostUuid
	}
	return ""
}

func (x *CheckResultRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *CheckResultRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type CheckResultResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId     string         `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId        string         `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
	Job          *Job           `protobuf:"bytes,3,opt,name=job,proto3" json:"job,omitempty"`
	CheckResults []*CheckResult `protobuf:"bytes,4,rep,name=check_results,proto3" json:"check_results,omitempty"`
}

func (x *CheckResultResponse) Reset() {
	*x = CheckResultResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultResponse) ProtoMessage() {}

func (x *CheckResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultResponse.ProtoReflect.Descriptor instead.
func (*CheckResultResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{3}
}

func (x *CheckResultResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *CheckResultResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *CheckResultResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *CheckResultResponse) GetCheckResults() []*CheckResult {
	if x != nil {
		return x.CheckResults
	}
	return nil
}

type ConvertRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterUuid  string   `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	HostUuid     string   `protobuf:"bytes,2,opt,name=host_uuid,proto3" json:"host_uuid,omitempty"`
	ClientId     string   `protobuf:"bytes,3,opt,name=client_id,proto3" json:"client_id,omitempty"`
	CurrentRole  HostRole `protobuf:"varint,4,opt,name=current_role,proto3,enum=lcm.manager.protobuf.HostRole" json:"current_role,omitempty"`
	TargetRole   HostRole `protobuf:"varint,5,opt,name=target_role,proto3,enum=lcm.manager.protobuf.HostRole" json:"target_role,omitempty"`
	SkipPrecheck bool     `protobuf:"varint,6,opt,name=skip_precheck,proto3" json:"skip_precheck,omitempty"`
}

func (x *ConvertRoleRequest) Reset() {
	*x = ConvertRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertRoleRequest) ProtoMessage() {}

func (x *ConvertRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertRoleRequest.ProtoReflect.Descriptor instead.
func (*ConvertRoleRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{4}
}

func (x *ConvertRoleRequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *ConvertRoleRequest) GetHostUuid() string {
	if x != nil {
		return x.HostUuid
	}
	return ""
}

func (x *ConvertRoleRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ConvertRoleRequest) GetCurrentRole() HostRole {
	if x != nil {
		return x.CurrentRole
	}
	return HostRole_ROLE_UNSPECIFIED
}

func (x *ConvertRoleRequest) GetTargetRole() HostRole {
	if x != nil {
		return x.TargetRole
	}
	return HostRole_ROLE_UNSPECIFIED
}

func (x *ConvertRoleRequest) GetSkipPrecheck() bool {
	if x != nil {
		return x.SkipPrecheck
	}
	return false
}

type ConvertRoleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId    string `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *ConvertRoleResponse) Reset() {
	*x = ConvertRoleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertRoleResponse) ProtoMessage() {}

func (x *ConvertRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertRoleResponse.ProtoReflect.Descriptor instead.
func (*ConvertRoleResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{5}
}

func (x *ConvertRoleResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ConvertRoleResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type RemoveHostCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterUuid string `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	HostUuid    string `protobuf:"bytes,2,opt,name=host_uuid,proto3" json:"host_uuid,omitempty"`
	ClientId    string `protobuf:"bytes,3,opt,name=client_id,proto3" json:"client_id,omitempty"`
}

func (x *RemoveHostCheckRequest) Reset() {
	*x = RemoveHostCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveHostCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveHostCheckRequest) ProtoMessage() {}

func (x *RemoveHostCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveHostCheckRequest.ProtoReflect.Descriptor instead.
func (*RemoveHostCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{6}
}

func (x *RemoveHostCheckRequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *RemoveHostCheckRequest) GetHostUuid() string {
	if x != nil {
		return x.HostUuid
	}
	return ""
}

func (x *RemoveHostCheckRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

type RemoveHostCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId    string `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *RemoveHostCheckResponse) Reset() {
	*x = RemoveHostCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveHostCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveHostCheckResponse) ProtoMessage() {}

func (x *RemoveHostCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveHostCheckResponse.ProtoReflect.Descriptor instead.
func (*RemoveHostCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{7}
}

func (x *RemoveHostCheckResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RemoveHostCheckResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type RemoveHostRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterUuid  string `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	HostUuid     string `protobuf:"bytes,2,opt,name=host_uuid,proto3" json:"host_uuid,omitempty"`
	ClientId     string `protobuf:"bytes,3,opt,name=client_id,proto3" json:"client_id,omitempty"`
	SkipPrecheck bool   `protobuf:"varint,4,opt,name=skip_precheck,proto3" json:"skip_precheck,omitempty"`
}

func (x *RemoveHostRequest) Reset() {
	*x = RemoveHostRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveHostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveHostRequest) ProtoMessage() {}

func (x *RemoveHostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveHostRequest.ProtoReflect.Descriptor instead.
func (*RemoveHostRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{8}
}

func (x *RemoveHostRequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *RemoveHostRequest) GetHostUuid() string {
	if x != nil {
		return x.HostUuid
	}
	return ""
}

func (x *RemoveHostRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RemoveHostRequest) GetSkipPrecheck() bool {
	if x != nil {
		return x.SkipPrecheck
	}
	return false
}

type RemoveHostResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId    string `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *RemoveHostResponse) Reset() {
	*x = RemoveHostResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveHostResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveHostResponse) ProtoMessage() {}

func (x *RemoveHostResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveHostResponse.ProtoReflect.Descriptor instead.
func (*RemoveHostResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{9}
}

func (x *RemoveHostResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RemoveHostResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type TimeSyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterUuid string `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	ClientId    string `protobuf:"bytes,2,opt,name=client_id,proto3" json:"client_id,omitempty"`
	Time        string `protobuf:"bytes,3,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *TimeSyncRequest) Reset() {
	*x = TimeSyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeSyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeSyncRequest) ProtoMessage() {}

func (x *TimeSyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeSyncRequest.ProtoReflect.Descriptor instead.
func (*TimeSyncRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{10}
}

func (x *TimeSyncRequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *TimeSyncRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *TimeSyncRequest) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

type TimeSyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId    string `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *TimeSyncResponse) Reset() {
	*x = TimeSyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeSyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeSyncResponse) ProtoMessage() {}

func (x *TimeSyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeSyncResponse.ProtoReflect.Descriptor instead.
func (*TimeSyncResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{11}
}

func (x *TimeSyncResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *TimeSyncResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type SetRDMARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterUuid string    `protobuf:"bytes,1,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	ClientId    string    `protobuf:"bytes,2,opt,name=client_id,proto3" json:"client_id,omitempty"`
	State       RDMAState `protobuf:"varint,3,opt,name=state,proto3,enum=lcm.manager.protobuf.RDMAState" json:"state,omitempty"`
}

func (x *SetRDMARequest) Reset() {
	*x = SetRDMARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRDMARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRDMARequest) ProtoMessage() {}

func (x *SetRDMARequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRDMARequest.ProtoReflect.Descriptor instead.
func (*SetRDMARequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{12}
}

func (x *SetRDMARequest) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *SetRDMARequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *SetRDMARequest) GetState() RDMAState {
	if x != nil {
		return x.State
	}
	return RDMAState_RDMA_UNKNOWN
}

type SetRDMAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	JobId    string `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
}

func (x *SetRDMAResponse) Reset() {
	*x = SetRDMAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRDMAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRDMAResponse) ProtoMessage() {}

func (x *SetRDMAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRDMAResponse.ProtoReflect.Descriptor instead.
func (*SetRDMAResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{13}
}

func (x *SetRDMAResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *SetRDMAResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type ProgressDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Items map[string]string `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProgressDetail) Reset() {
	*x = ProgressDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressDetail) ProtoMessage() {}

func (x *ProgressDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressDetail.ProtoReflect.Descriptor instead.
func (*ProgressDetail) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{14}
}

func (x *ProgressDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProgressDetail) GetItems() map[string]string {
	if x != nil {
		return x.Items
	}
	return nil
}

type JobProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Progress  string          `protobuf:"bytes,1,opt,name=progress,proto3" json:"progress,omitempty"`
	TotalTime string          `protobuf:"bytes,2,opt,name=total_time,proto3" json:"total_time,omitempty"`
	Details   *ProgressDetail `protobuf:"bytes,3,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *JobProgress) Reset() {
	*x = JobProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobProgress) ProtoMessage() {}

func (x *JobProgress) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobProgress.ProtoReflect.Descriptor instead.
func (*JobProgress) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{15}
}

func (x *JobProgress) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *JobProgress) GetTotalTime() string {
	if x != nil {
		return x.TotalTime
	}
	return ""
}

func (x *JobProgress) GetDetails() *ProgressDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ClusterUuid string                 `protobuf:"bytes,2,opt,name=cluster_uuid,proto3" json:"cluster_uuid,omitempty"`
	HostUuid    string                 `protobuf:"bytes,3,opt,name=host_uuid,proto3" json:"host_uuid,omitempty"`
	Name        string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	State       JobState               `protobuf:"varint,5,opt,name=state,proto3,enum=lcm.manager.protobuf.JobState" json:"state,omitempty"`
	Details     map[string]string      `protobuf:"bytes,6,rep,name=details,proto3" json:"details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Progress    *JobProgress           `protobuf:"bytes,7,opt,name=progress,proto3" json:"progress,omitempty"`
	Messages    []*MessageItem         `protobuf:"bytes,8,rep,name=messages,proto3" json:"messages,omitempty"`
	Ec          ErrorCode              `protobuf:"varint,9,opt,name=ec,proto3,enum=lcm.manager.protobuf.ErrorCode" json:"ec,omitempty"`
	CreateTime  *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,proto3" json:"create_time,omitempty"`
	UpdateTime  *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,proto3" json:"update_time,omitempty"`
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{16}
}

func (x *Job) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Job) GetClusterUuid() string {
	if x != nil {
		return x.ClusterUuid
	}
	return ""
}

func (x *Job) GetHostUuid() string {
	if x != nil {
		return x.HostUuid
	}
	return ""
}

func (x *Job) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Job) GetState() JobState {
	if x != nil {
		return x.State
	}
	return JobState_JOB_STATE_UNSPECIFIED
}

func (x *Job) GetDetails() map[string]string {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *Job) GetProgress() *JobProgress {
	if x != nil {
		return x.Progress
	}
	return nil
}

func (x *Job) GetMessages() []*MessageItem {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *Job) GetEc() ErrorCode {
	if x != nil {
		return x.Ec
	}
	return ErrorCode_EC_UNSPECIFIED
}

func (x *Job) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Job) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type ListJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter    string `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy   string `protobuf:"bytes,4,opt,name=order_by,proto3" json:"order_by,omitempty"`
	PageSize  int32  `protobuf:"varint,1,opt,name=page_size,proto3" json:"page_size,omitempty"`
	PageToken string `protobuf:"bytes,2,opt,name=page_token,proto3" json:"page_token,omitempty"`
}

func (x *ListJobsRequest) Reset() {
	*x = ListJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsRequest) ProtoMessage() {}

func (x *ListJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsRequest.ProtoReflect.Descriptor instead.
func (*ListJobsRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{17}
}

func (x *ListJobsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListJobsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListJobsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListJobsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jobs          []*Job `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,proto3" json:"next_page_token,omitempty"`
	Total         string `protobuf:"bytes,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListJobsResponse) Reset() {
	*x = ListJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsResponse) ProtoMessage() {}

func (x *ListJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsResponse.ProtoReflect.Descriptor instead.
func (*ListJobsResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{18}
}

func (x *ListJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *ListJobsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListJobsResponse) GetTotal() string {
	if x != nil {
		return x.Total
	}
	return ""
}

type GetJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{19}
}

func (x *GetJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetJobLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Offset int32  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Length int32  `protobuf:"varint,3,opt,name=length,proto3" json:"length,omitempty"`
}

func (x *GetJobLogsRequest) Reset() {
	*x = GetJobLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobLogsRequest) ProtoMessage() {}

func (x *GetJobLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobLogsRequest.ProtoReflect.Descriptor instead.
func (*GetJobLogsRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{20}
}

func (x *GetJobLogsRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetJobLogsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetJobLogsRequest) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

type JobLogs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Logs       []string `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs,omitempty"`
	Offset     int32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	NextOffset int32    `protobuf:"varint,3,opt,name=next_offset,proto3" json:"next_offset,omitempty"`
}

func (x *JobLogs) Reset() {
	*x = JobLogs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobLogs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobLogs) ProtoMessage() {}

func (x *JobLogs) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobLogs.ProtoReflect.Descriptor instead.
func (*JobLogs) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{21}
}

func (x *JobLogs) GetLogs() []string {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *JobLogs) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *JobLogs) GetNextOffset() int32 {
	if x != nil {
		return x.NextOffset
	}
	return 0
}

type HealthzResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result string `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *HealthzResponse) Reset() {
	*x = HealthzResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthzResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthzResponse) ProtoMessage() {}

func (x *HealthzResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthzResponse.ProtoReflect.Descriptor instead.
func (*HealthzResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{22}
}

func (x *HealthzResponse) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

type MessageItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang    MessageLang `protobuf:"varint,1,opt,name=lang,proto3,enum=lcm.manager.protobuf.MessageLang" json:"lang,omitempty"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *MessageItem) Reset() {
	*x = MessageItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageItem) ProtoMessage() {}

func (x *MessageItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageItem.ProtoReflect.Descriptor instead.
func (*MessageItem) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{23}
}

func (x *MessageItem) GetLang() MessageLang {
	if x != nil {
		return x.Lang
	}
	return MessageLang_LANG_UNSPECIFIED
}

func (x *MessageItem) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	JobId       string                 `protobuf:"bytes,2,opt,name=job_id,proto3" json:"job_id,omitempty"`
	CheckName   string                 `protobuf:"bytes,3,opt,name=check_name,proto3" json:"check_name,omitempty"`
	Description []*MessageItem         `protobuf:"bytes,4,rep,name=description,proto3" json:"description,omitempty"`
	State       CheckState             `protobuf:"varint,5,opt,name=state,proto3,enum=lcm.manager.protobuf.CheckState" json:"state,omitempty"`
	Messages    []*MessageItem         `protobuf:"bytes,7,rep,name=messages,proto3" json:"messages,omitempty"`
	CreateTime  *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,proto3" json:"create_time,omitempty"`
	UpdateTime  *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,proto3" json:"update_time,omitempty"`
}

func (x *CheckResult) Reset() {
	*x = CheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResult) ProtoMessage() {}

func (x *CheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResult.ProtoReflect.Descriptor instead.
func (*CheckResult) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{24}
}

func (x *CheckResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CheckResult) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *CheckResult) GetCheckName() string {
	if x != nil {
		return x.CheckName
	}
	return ""
}

func (x *CheckResult) GetDescription() []*MessageItem {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *CheckResult) GetState() CheckState {
	if x != nil {
		return x.State
	}
	return CheckState_CHECK_STATE_UNSPECIFIED
}

func (x *CheckResult) GetMessages() []*MessageItem {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *CheckResult) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CheckResult) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type AvailabilityMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version        string           `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	ProductActions []*ProductAction `protobuf:"bytes,2,rep,name=product_actions,proto3" json:"product_actions,omitempty"`
}

func (x *AvailabilityMap) Reset() {
	*x = AvailabilityMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailabilityMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailabilityMap) ProtoMessage() {}

func (x *AvailabilityMap) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailabilityMap.ProtoReflect.Descriptor instead.
func (*AvailabilityMap) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{25}
}

func (x *AvailabilityMap) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AvailabilityMap) GetProductActions() []*ProductAction {
	if x != nil {
		return x.ProductActions
	}
	return nil
}

type ProductAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductName ProductName `protobuf:"varint,1,opt,name=product_name,proto3,enum=lcm.manager.protobuf.ProductName" json:"product_name,omitempty"`
	Actions     []*Action   `protobuf:"bytes,2,rep,name=actions,proto3" json:"actions,omitempty"`
}

func (x *ProductAction) Reset() {
	*x = ProductAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductAction) ProtoMessage() {}

func (x *ProductAction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductAction.ProtoReflect.Descriptor instead.
func (*ProductAction) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{26}
}

func (x *ProductAction) GetProductName() ProductName {
	if x != nil {
		return x.ProductName
	}
	return ProductName_PRODUCT_UNSPECIFIED
}

func (x *ProductAction) GetActions() []*Action {
	if x != nil {
		return x.Actions
	}
	return nil
}

type Action struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type             ActionType         `protobuf:"varint,2,opt,name=type,proto3,enum=lcm.manager.protobuf.ActionType" json:"type,omitempty"`
	VersionedActions []*VersionedAction `protobuf:"bytes,3,rep,name=versioned_actions,proto3" json:"versioned_actions,omitempty"`
}

func (x *Action) Reset() {
	*x = Action{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Action) ProtoMessage() {}

func (x *Action) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Action.ProtoReflect.Descriptor instead.
func (*Action) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{27}
}

func (x *Action) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Action) GetType() ActionType {
	if x != nil {
		return x.Type
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *Action) GetVersionedActions() []*VersionedAction {
	if x != nil {
		return x.VersionedActions
	}
	return nil
}

type VersionedAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Versions []string        `protobuf:"bytes,1,rep,name=versions,proto3" json:"versions,omitempty"`
	Features []ActionFeature `protobuf:"varint,2,rep,packed,name=features,proto3,enum=lcm.manager.protobuf.ActionFeature" json:"features,omitempty"`
}

func (x *VersionedAction) Reset() {
	*x = VersionedAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_server_v1_server_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionedAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionedAction) ProtoMessage() {}

func (x *VersionedAction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_v1_server_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionedAction.ProtoReflect.Descriptor instead.
func (*VersionedAction) Descriptor() ([]byte, []int) {
	return file_proto_server_v1_server_proto_rawDescGZIP(), []int{28}
}

func (x *VersionedAction) GetVersions() []string {
	if x != nil {
		return x.Versions
	}
	return nil
}

func (x *VersionedAction) GetFeatures() []ActionFeature {
	if x != nil {
		return x.Features
	}
	return nil
}

var File_proto_server_v1_server_proto protoreflect.FileDescriptor

var file_proto_server_v1_server_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14,
	0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xff, 0x01, 0x0a, 0x17, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x48, 0x6f, 0x73,
	0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x22, 0x50, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a,
	0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62,
	0x5f, 0x69, 0x64, 0x22, 0xc1, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69,
	0x64, 0x12, 0x2b, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x47,
	0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xa0, 0x02, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x42,
	0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x48, 0x6f, 0x73, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x48,
	0x6f, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x70, 0x72, 0x65,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x6b, 0x69,
	0x70, 0x5f, 0x70, 0x72, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x22, 0x4b, 0x0a, 0x13, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x22, 0x78, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x48, 0x6f, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75,
	0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x22, 0x4f, 0x0a, 0x17, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x5f,
	0x69, 0x64, 0x22, 0x99, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70,
	0x5f, 0x70, 0x72, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x70, 0x72, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x22, 0x4a,
	0x0a, 0x12, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x22, 0x67, 0x0a, 0x0f, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x10, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x22, 0x89, 0x01,
	0x0a, 0x0e, 0x53, 0x65, 0x74, 0x52, 0x44, 0x4d, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x35, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x52, 0x44, 0x4d, 0x41, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x47, 0x0a, 0x0f, 0x53, 0x65, 0x74,
	0x52, 0x44, 0x4d, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x5f,
	0x69, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x1a, 0x38, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x4a,
	0x6f, 0x62, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xca, 0x04, 0x0a, 0x03, 0x4a, 0x6f, 0x62, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6c, 0x63,
	0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4a, 0x6f, 0x62, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3d, 0x0a, 0x08, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x02, 0x65, 0x63,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x02, 0x65, 0x63, 0x12, 0x3c, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x83, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d,
	0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6c,
	0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x1f, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x53,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x22, 0x57, 0x0a, 0x07, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f,
	0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x29, 0x0a, 0x0f,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5e, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x8d, 0x03, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x43, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x08,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x7a, 0x0a, 0x0f, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6c, 0x63,
	0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0c,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x07,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x11, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x6e,
	0x0a, 0x0f, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3f, 0x0a,
	0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x23, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2a, 0x43,
	0x0a, 0x08, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x4f,
	0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47,
	0x45, 0x10, 0x02, 0x2a, 0x38, 0x0a, 0x09, 0x52, 0x44, 0x4d, 0x41, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x10, 0x0a, 0x0c, 0x52, 0x44, 0x4d, 0x41, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x44, 0x4d, 0x41, 0x5f, 0x4f, 0x4e, 0x10, 0x01, 0x12,
	0x0c, 0x0a, 0x08, 0x52, 0x44, 0x4d, 0x41, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x02, 0x2a, 0x80, 0x01,
	0x0a, 0x08, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x4a, 0x4f,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4a, 0x4f,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x2a, 0x3c, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x0e, 0x45, 0x43, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x2a, 0xa5,
	0x01, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x17, 0x0a,
	0x13, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x41, 0x52,
	0x4e, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x2a, 0x3d, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x41, 0x4e, 0x47, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4c,
	0x41, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x41, 0x4e, 0x47,
	0x5f, 0x5a, 0x48, 0x10, 0x02, 0x2a, 0x4c, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x53, 0x4d, 0x54, 0x58, 0x4f, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x4d, 0x54,
	0x58, 0x5a, 0x42, 0x53, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x4d, 0x54, 0x58, 0x45, 0x4c,
	0x46, 0x10, 0x03, 0x2a, 0x5b, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x54, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x10,
	0x02, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x10, 0x03,
	0x2a, 0x50, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4d, 0x55, 0x4c,
	0x54, 0x49, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x53,
	0x10, 0x01, 0x32, 0xb0, 0x0f, 0x0a, 0x07, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0xc2,
	0x01, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x12, 0x2d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x52, 0x6f, 0x6c, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x4f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x49, 0x3a, 0x01, 0x2a, 0x22, 0x44, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f,
	0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x68,
	0x6f, 0x73, 0x74, 0x73, 0x2f, 0x7b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d,
	0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x12, 0xc2, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x28,
	0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4d, 0x12, 0x4b, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x68, 0x6f, 0x73,
	0x74, 0x73, 0x2f, 0x7b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x72,
	0x6f, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xad, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x28, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x49, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x43, 0x3a, 0x01, 0x2a, 0x22, 0x3e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x2f,
	0x7b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x12, 0xb9, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2c, 0x2e, 0x6c,
	0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6c, 0x63, 0x6d,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x43, 0x3a, 0x01, 0x2a, 0x22, 0x3e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x2f, 0x7b, 0x68, 0x6f, 0x73,
	0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x12, 0xbb, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48,
	0x6f, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x28,
	0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x4d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x47, 0x12, 0x45, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x68, 0x6f, 0x73,
	0x74, 0x73, 0x2f, 0x7b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x72,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0xa4, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73,
	0x74, 0x12, 0x27, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6c, 0x63, 0x6d,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3d, 0x3a, 0x01, 0x2a, 0x22,
	0x38, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d,
	0x2f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x2f, 0x7b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x7d, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x08, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x25, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a,
	0x22, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x8b, 0x01, 0x0a, 0x07,
	0x53, 0x65, 0x74, 0x52, 0x44, 0x4d, 0x41, 0x12, 0x24, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x65, 0x74, 0x52, 0x44, 0x4d, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e,
	0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x44, 0x4d, 0x41, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22,
	0x28, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x7d,
	0x2f, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x64, 0x6d, 0x61, 0x12, 0x6f, 0x0a, 0x08, 0x4c, 0x69, 0x73,
	0x74, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x25, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x6c,
	0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x12, 0x0c, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x65, 0x0a, 0x06, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x12, 0x23, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6c, 0x63, 0x6d, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4a, 0x6f, 0x62, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x3d, 0x2a,
	0x7d, 0x12, 0x76, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x73, 0x12,
	0x27, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x73, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12,
	0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x3d, 0x2a, 0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x77, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x25, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x22,
	0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x61, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x12, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x25, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x7a, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x17, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x7a, 0x42, 0xd0, 0x01, 0x0a, 0x18, 0x63, 0x6f, 0x6d, 0x2e, 0x6c, 0x63,
	0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x42, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x78,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x43, 0x4d, 0x2f, 0x6c, 0x63, 0x6d, 0x2d, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x4c, 0x4d, 0x50, 0xaa, 0x02,
	0x14, 0x4c, 0x63, 0x6d, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0xca, 0x02, 0x14, 0x4c, 0x63, 0x6d, 0x5c, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x5c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0xe2, 0x02, 0x20, 0x4c,
	0x63, 0x6d, 0x5c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x5c, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea,
	0x02, 0x16, 0x4c, 0x63, 0x6d, 0x3a, 0x3a, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x3a, 0x3a,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_server_v1_server_proto_rawDescOnce sync.Once
	file_proto_server_v1_server_proto_rawDescData = file_proto_server_v1_server_proto_rawDesc
)

func file_proto_server_v1_server_proto_rawDescGZIP() []byte {
	file_proto_server_v1_server_proto_rawDescOnce.Do(func() {
		file_proto_server_v1_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_server_v1_server_proto_rawDescData)
	})
	return file_proto_server_v1_server_proto_rawDescData
}

var file_proto_server_v1_server_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_proto_server_v1_server_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_proto_server_v1_server_proto_goTypes = []any{
	(HostRole)(0),                    // 0: lcm.manager.protobuf.HostRole
	(RDMAState)(0),                   // 1: lcm.manager.protobuf.RDMAState
	(JobState)(0),                    // 2: lcm.manager.protobuf.JobState
	(ErrorCode)(0),                   // 3: lcm.manager.protobuf.ErrorCode
	(CheckState)(0),                  // 4: lcm.manager.protobuf.CheckState
	(MessageLang)(0),                 // 5: lcm.manager.protobuf.MessageLang
	(ProductName)(0),                 // 6: lcm.manager.protobuf.ProductName
	(ActionType)(0),                  // 7: lcm.manager.protobuf.ActionType
	(ActionFeature)(0),               // 8: lcm.manager.protobuf.ActionFeature
	(*ConvertRoleCheckRequest)(nil),  // 9: lcm.manager.protobuf.ConvertRoleCheckRequest
	(*ConvertRoleCheckResponse)(nil), // 10: lcm.manager.protobuf.ConvertRoleCheckResponse
	(*CheckResultRequest)(nil),       // 11: lcm.manager.protobuf.CheckResultRequest
	(*CheckResultResponse)(nil),      // 12: lcm.manager.protobuf.CheckResultResponse
	(*ConvertRoleRequest)(nil),       // 13: lcm.manager.protobuf.ConvertRoleRequest
	(*ConvertRoleResponse)(nil),      // 14: lcm.manager.protobuf.ConvertRoleResponse
	(*RemoveHostCheckRequest)(nil),   // 15: lcm.manager.protobuf.RemoveHostCheckRequest
	(*RemoveHostCheckResponse)(nil),  // 16: lcm.manager.protobuf.RemoveHostCheckResponse
	(*RemoveHostRequest)(nil),        // 17: lcm.manager.protobuf.RemoveHostRequest
	(*RemoveHostResponse)(nil),       // 18: lcm.manager.protobuf.RemoveHostResponse
	(*TimeSyncRequest)(nil),          // 19: lcm.manager.protobuf.TimeSyncRequest
	(*TimeSyncResponse)(nil),         // 20: lcm.manager.protobuf.TimeSyncResponse
	(*SetRDMARequest)(nil),           // 21: lcm.manager.protobuf.SetRDMARequest
	(*SetRDMAResponse)(nil),          // 22: lcm.manager.protobuf.SetRDMAResponse
	(*ProgressDetail)(nil),           // 23: lcm.manager.protobuf.ProgressDetail
	(*JobProgress)(nil),              // 24: lcm.manager.protobuf.JobProgress
	(*Job)(nil),                      // 25: lcm.manager.protobuf.Job
	(*ListJobsRequest)(nil),          // 26: lcm.manager.protobuf.ListJobsRequest
	(*ListJobsResponse)(nil),         // 27: lcm.manager.protobuf.ListJobsResponse
	(*GetJobRequest)(nil),            // 28: lcm.manager.protobuf.GetJobRequest
	(*GetJobLogsRequest)(nil),        // 29: lcm.manager.protobuf.GetJobLogsRequest
	(*JobLogs)(nil),                  // 30: lcm.manager.protobuf.JobLogs
	(*HealthzResponse)(nil),          // 31: lcm.manager.protobuf.HealthzResponse
	(*MessageItem)(nil),              // 32: lcm.manager.protobuf.MessageItem
	(*CheckResult)(nil),              // 33: lcm.manager.protobuf.CheckResult
	(*AvailabilityMap)(nil),          // 34: lcm.manager.protobuf.AvailabilityMap
	(*ProductAction)(nil),            // 35: lcm.manager.protobuf.ProductAction
	(*Action)(nil),                   // 36: lcm.manager.protobuf.Action
	(*VersionedAction)(nil),          // 37: lcm.manager.protobuf.VersionedAction
	nil,                              // 38: lcm.manager.protobuf.ProgressDetail.ItemsEntry
	nil,                              // 39: lcm.manager.protobuf.Job.DetailsEntry
	(*timestamppb.Timestamp)(nil),    // 40: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),            // 41: google.protobuf.Empty
}
var file_proto_server_v1_server_proto_depIdxs = []int32{
	0,  // 0: lcm.manager.protobuf.ConvertRoleCheckRequest.current_role:type_name -> lcm.manager.protobuf.HostRole
	0,  // 1: lcm.manager.protobuf.ConvertRoleCheckRequest.target_role:type_name -> lcm.manager.protobuf.HostRole
	25, // 2: lcm.manager.protobuf.CheckResultResponse.job:type_name -> lcm.manager.protobuf.Job
	33, // 3: lcm.manager.protobuf.CheckResultResponse.check_results:type_name -> lcm.manager.protobuf.CheckResult
	0,  // 4: lcm.manager.protobuf.ConvertRoleRequest.current_role:type_name -> lcm.manager.protobuf.HostRole
	0,  // 5: lcm.manager.protobuf.ConvertRoleRequest.target_role:type_name -> lcm.manager.protobuf.HostRole
	1,  // 6: lcm.manager.protobuf.SetRDMARequest.state:type_name -> lcm.manager.protobuf.RDMAState
	38, // 7: lcm.manager.protobuf.ProgressDetail.items:type_name -> lcm.manager.protobuf.ProgressDetail.ItemsEntry
	23, // 8: lcm.manager.protobuf.JobProgress.details:type_name -> lcm.manager.protobuf.ProgressDetail
	2,  // 9: lcm.manager.protobuf.Job.state:type_name -> lcm.manager.protobuf.JobState
	39, // 10: lcm.manager.protobuf.Job.details:type_name -> lcm.manager.protobuf.Job.DetailsEntry
	24, // 11: lcm.manager.protobuf.Job.progress:type_name -> lcm.manager.protobuf.JobProgress
	32, // 12: lcm.manager.protobuf.Job.messages:type_name -> lcm.manager.protobuf.MessageItem
	3,  // 13: lcm.manager.protobuf.Job.ec:type_name -> lcm.manager.protobuf.ErrorCode
	40, // 14: lcm.manager.protobuf.Job.create_time:type_name -> google.protobuf.Timestamp
	40, // 15: lcm.manager.protobuf.Job.update_time:type_name -> google.protobuf.Timestamp
	25, // 16: lcm.manager.protobuf.ListJobsResponse.jobs:type_name -> lcm.manager.protobuf.Job
	5,  // 17: lcm.manager.protobuf.MessageItem.lang:type_name -> lcm.manager.protobuf.MessageLang
	32, // 18: lcm.manager.protobuf.CheckResult.description:type_name -> lcm.manager.protobuf.MessageItem
	4,  // 19: lcm.manager.protobuf.CheckResult.state:type_name -> lcm.manager.protobuf.CheckState
	32, // 20: lcm.manager.protobuf.CheckResult.messages:type_name -> lcm.manager.protobuf.MessageItem
	40, // 21: lcm.manager.protobuf.CheckResult.create_time:type_name -> google.protobuf.Timestamp
	40, // 22: lcm.manager.protobuf.CheckResult.update_time:type_name -> google.protobuf.Timestamp
	35, // 23: lcm.manager.protobuf.AvailabilityMap.product_actions:type_name -> lcm.manager.protobuf.ProductAction
	6,  // 24: lcm.manager.protobuf.ProductAction.product_name:type_name -> lcm.manager.protobuf.ProductName
	36, // 25: lcm.manager.protobuf.ProductAction.actions:type_name -> lcm.manager.protobuf.Action
	7,  // 26: lcm.manager.protobuf.Action.type:type_name -> lcm.manager.protobuf.ActionType
	37, // 27: lcm.manager.protobuf.Action.versioned_actions:type_name -> lcm.manager.protobuf.VersionedAction
	8,  // 28: lcm.manager.protobuf.VersionedAction.features:type_name -> lcm.manager.protobuf.ActionFeature
	9,  // 29: lcm.manager.protobuf.Manager.ConvertRoleCheck:input_type -> lcm.manager.protobuf.ConvertRoleCheckRequest
	11, // 30: lcm.manager.protobuf.Manager.ConvertRoleCheckResult:input_type -> lcm.manager.protobuf.CheckResultRequest
	13, // 31: lcm.manager.protobuf.Manager.ConvertRole:input_type -> lcm.manager.protobuf.ConvertRoleRequest
	15, // 32: lcm.manager.protobuf.Manager.RemoveHostCheck:input_type -> lcm.manager.protobuf.RemoveHostCheckRequest
	11, // 33: lcm.manager.protobuf.Manager.RemoveHostCheckResult:input_type -> lcm.manager.protobuf.CheckResultRequest
	17, // 34: lcm.manager.protobuf.Manager.RemoveHost:input_type -> lcm.manager.protobuf.RemoveHostRequest
	19, // 35: lcm.manager.protobuf.Manager.TimeSync:input_type -> lcm.manager.protobuf.TimeSyncRequest
	21, // 36: lcm.manager.protobuf.Manager.SetRDMA:input_type -> lcm.manager.protobuf.SetRDMARequest
	26, // 37: lcm.manager.protobuf.Manager.ListJobs:input_type -> lcm.manager.protobuf.ListJobsRequest
	28, // 38: lcm.manager.protobuf.Manager.GetJob:input_type -> lcm.manager.protobuf.GetJobRequest
	29, // 39: lcm.manager.protobuf.Manager.GetJobLogs:input_type -> lcm.manager.protobuf.GetJobLogsRequest
	41, // 40: lcm.manager.protobuf.Manager.GetSupportedActions:input_type -> google.protobuf.Empty
	41, // 41: lcm.manager.protobuf.Manager.Healthz:input_type -> google.protobuf.Empty
	10, // 42: lcm.manager.protobuf.Manager.ConvertRoleCheck:output_type -> lcm.manager.protobuf.ConvertRoleCheckResponse
	12, // 43: lcm.manager.protobuf.Manager.ConvertRoleCheckResult:output_type -> lcm.manager.protobuf.CheckResultResponse
	14, // 44: lcm.manager.protobuf.Manager.ConvertRole:output_type -> lcm.manager.protobuf.ConvertRoleResponse
	16, // 45: lcm.manager.protobuf.Manager.RemoveHostCheck:output_type -> lcm.manager.protobuf.RemoveHostCheckResponse
	12, // 46: lcm.manager.protobuf.Manager.RemoveHostCheckResult:output_type -> lcm.manager.protobuf.CheckResultResponse
	18, // 47: lcm.manager.protobuf.Manager.RemoveHost:output_type -> lcm.manager.protobuf.RemoveHostResponse
	20, // 48: lcm.manager.protobuf.Manager.TimeSync:output_type -> lcm.manager.protobuf.TimeSyncResponse
	22, // 49: lcm.manager.protobuf.Manager.SetRDMA:output_type -> lcm.manager.protobuf.SetRDMAResponse
	27, // 50: lcm.manager.protobuf.Manager.ListJobs:output_type -> lcm.manager.protobuf.ListJobsResponse
	25, // 51: lcm.manager.protobuf.Manager.GetJob:output_type -> lcm.manager.protobuf.Job
	30, // 52: lcm.manager.protobuf.Manager.GetJobLogs:output_type -> lcm.manager.protobuf.JobLogs
	34, // 53: lcm.manager.protobuf.Manager.GetSupportedActions:output_type -> lcm.manager.protobuf.AvailabilityMap
	31, // 54: lcm.manager.protobuf.Manager.Healthz:output_type -> lcm.manager.protobuf.HealthzResponse
	42, // [42:55] is the sub-list for method output_type
	29, // [29:42] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_proto_server_v1_server_proto_init() }
func file_proto_server_v1_server_proto_init() {
	if File_proto_server_v1_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_server_v1_server_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ConvertRoleCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ConvertRoleCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CheckResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CheckResultResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ConvertRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ConvertRoleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveHostCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveHostCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveHostRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveHostResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*TimeSyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*TimeSyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*SetRDMARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*SetRDMAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ProgressDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*JobProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*ListJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GetJobLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*JobLogs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*HealthzResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*MessageItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*CheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*AvailabilityMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*ProductAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*Action); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_server_v1_server_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*VersionedAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_server_v1_server_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_server_v1_server_proto_goTypes,
		DependencyIndexes: file_proto_server_v1_server_proto_depIdxs,
		EnumInfos:         file_proto_server_v1_server_proto_enumTypes,
		MessageInfos:      file_proto_server_v1_server_proto_msgTypes,
	}.Build()
	File_proto_server_v1_server_proto = out.File
	file_proto_server_v1_server_proto_rawDesc = nil
	file_proto_server_v1_server_proto_goTypes = nil
	file_proto_server_v1_server_proto_depIdxs = nil
}
