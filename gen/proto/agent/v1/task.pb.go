// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: proto/agent/v1/task.proto

package v1

import (
	v1 "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorCode int32

const (
	ErrorCode_EC_UNSPECIFIED        ErrorCode = 0
	ErrorCode_EC_EOK                ErrorCode = 1
	ErrorCode_EC_INVALID_PARAMETER  ErrorCode = 2
	ErrorCode_EC_CREATE_TASK_FAILED ErrorCode = 3
	ErrorCode_EC_UNAUTHORIZED       ErrorCode = 4
	ErrorCode_EC_NOT_FOUND          ErrorCode = 5
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0: "EC_UNSPECIFIED",
		1: "EC_EOK",
		2: "EC_INVALID_PARAMETER",
		3: "EC_CREATE_TASK_FAILED",
		4: "EC_UNAUTHORIZED",
		5: "EC_NOT_FOUND",
	}
	ErrorCode_value = map[string]int32{
		"EC_UNSPECIFIED":        0,
		"EC_EOK":                1,
		"EC_INVALID_PARAMETER":  2,
		"EC_CREATE_TASK_FAILED": 3,
		"EC_UNAUTHORIZED":       4,
		"EC_NOT_FOUND":          5,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_agent_v1_task_proto_enumTypes[0].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_proto_agent_v1_task_proto_enumTypes[0]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{0}
}

type TaskStatusEnum int32

const (
	TaskStatusEnum_UNSPECIFIED TaskStatusEnum = 0
	TaskStatusEnum_FAILED      TaskStatusEnum = 1
	TaskStatusEnum_PENDING     TaskStatusEnum = 2
	TaskStatusEnum_COMPLETED   TaskStatusEnum = 3
	TaskStatusEnum_RUNNING     TaskStatusEnum = 4
)

// Enum value maps for TaskStatusEnum.
var (
	TaskStatusEnum_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "FAILED",
		2: "PENDING",
		3: "COMPLETED",
		4: "RUNNING",
	}
	TaskStatusEnum_value = map[string]int32{
		"UNSPECIFIED": 0,
		"FAILED":      1,
		"PENDING":     2,
		"COMPLETED":   3,
		"RUNNING":     4,
	}
)

func (x TaskStatusEnum) Enum() *TaskStatusEnum {
	p := new(TaskStatusEnum)
	*p = x
	return p
}

func (x TaskStatusEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatusEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_agent_v1_task_proto_enumTypes[1].Descriptor()
}

func (TaskStatusEnum) Type() protoreflect.EnumType {
	return &file_proto_agent_v1_task_proto_enumTypes[1]
}

func (x TaskStatusEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatusEnum.Descriptor instead.
func (TaskStatusEnum) EnumDescriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{1}
}

type QA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query      string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Answer     string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
	PassedMark string `protobuf:"bytes,3,opt,name=passed_mark,proto3" json:"passed_mark,omitempty"`
}

func (x *QA) Reset() {
	*x = QA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QA) ProtoMessage() {}

func (x *QA) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QA.ProtoReflect.Descriptor instead.
func (*QA) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{0}
}

func (x *QA) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *QA) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *QA) GetPassedMark() string {
	if x != nil {
		return x.PassedMark
	}
	return ""
}

type TaskInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   string `protobuf:"bytes,1,opt,name=task_id,proto3" json:"task_id,omitempty"`
	Command  string `protobuf:"bytes,2,opt,name=command,proto3" json:"command,omitempty"`
	TargetIp string `protobuf:"bytes,3,opt,name=target_ip,proto3" json:"target_ip,omitempty"`
	Timeout  int32  `protobuf:"varint,4,opt,name=timeout,proto3" json:"timeout,omitempty"`
	CmdQa    []*QA  `protobuf:"bytes,5,rep,name=cmd_qa,proto3" json:"cmd_qa,omitempty"`
}

func (x *TaskInput) Reset() {
	*x = TaskInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInput) ProtoMessage() {}

func (x *TaskInput) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInput.ProtoReflect.Descriptor instead.
func (*TaskInput) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{1}
}

func (x *TaskInput) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskInput) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *TaskInput) GetTargetIp() string {
	if x != nil {
		return x.TargetIp
	}
	return ""
}

func (x *TaskInput) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *TaskInput) GetCmdQa() []*QA {
	if x != nil {
		return x.CmdQa
	}
	return nil
}

type TaskOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stdout string `protobuf:"bytes,1,opt,name=stdout,proto3" json:"stdout,omitempty"`
	Stderr string `protobuf:"bytes,2,opt,name=stderr,proto3" json:"stderr,omitempty"`
}

func (x *TaskOutput) Reset() {
	*x = TaskOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskOutput) ProtoMessage() {}

func (x *TaskOutput) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskOutput.ProtoReflect.Descriptor instead.
func (*TaskOutput) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{2}
}

func (x *TaskOutput) GetStdout() string {
	if x != nil {
		return x.Stdout
	}
	return ""
}

func (x *TaskOutput) GetStderr() string {
	if x != nil {
		return x.Stderr
	}
	return ""
}

type TaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  TaskStatusEnum         `protobuf:"varint,1,opt,name=status,proto3,enum=lcm.agent.protobuf.TaskStatusEnum" json:"status,omitempty"`
	Error   string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	StartAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_at,proto3" json:"start_at,omitempty"`
	EndAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_at,proto3" json:"end_at,omitempty"`
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{3}
}

func (x *TaskStatus) GetStatus() TaskStatusEnum {
	if x != nil {
		return x.Status
	}
	return TaskStatusEnum_UNSPECIFIED
}

func (x *TaskStatus) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *TaskStatus) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *TaskStatus) GetEndAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndAt
	}
	return nil
}

type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string      `protobuf:"bytes,1,opt,name=task_id,proto3" json:"task_id,omitempty"`
	Input  *TaskInput  `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
	Output *TaskOutput `protobuf:"bytes,3,opt,name=output,proto3" json:"output,omitempty"`
	Status *TaskStatus `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{4}
}

func (x *Task) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *Task) GetInput() *TaskInput {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *Task) GetOutput() *TaskOutput {
	if x != nil {
		return x.Output
	}
	return nil
}

func (x *Task) GetStatus() *TaskStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,proto3" json:"task_id,omitempty"`
}

func (x *GetTaskRequest) Reset() {
	*x = GetTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRequest) ProtoMessage() {}

func (x *GetTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRequest.ProtoReflect.Descriptor instead.
func (*GetTaskRequest) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{5}
}

func (x *GetTaskRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type GetTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ec    ErrorCode `protobuf:"varint,1,opt,name=ec,proto3,enum=lcm.agent.protobuf.ErrorCode" json:"ec,omitempty"`
	Data  *Task     `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Error string    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *GetTaskReply) Reset() {
	*x = GetTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskReply) ProtoMessage() {}

func (x *GetTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskReply.ProtoReflect.Descriptor instead.
func (*GetTaskReply) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{6}
}

func (x *GetTaskReply) GetEc() ErrorCode {
	if x != nil {
		return x.Ec
	}
	return ErrorCode_EC_UNSPECIFIED
}

func (x *GetTaskReply) GetData() *Task {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetTaskReply) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type ListTasksReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ec    ErrorCode `protobuf:"varint,1,opt,name=ec,proto3,enum=lcm.agent.protobuf.ErrorCode" json:"ec,omitempty"`
	Data  []*Task   `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	Error string    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ListTasksReply) Reset() {
	*x = ListTasksReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTasksReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTasksReply) ProtoMessage() {}

func (x *ListTasksReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTasksReply.ProtoReflect.Descriptor instead.
func (*ListTasksReply) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{7}
}

func (x *ListTasksReply) GetEc() ErrorCode {
	if x != nil {
		return x.Ec
	}
	return ErrorCode_EC_UNSPECIFIED
}

func (x *ListTasksReply) GetData() []*Task {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListTasksReply) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type CreateTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ec    ErrorCode            `protobuf:"varint,1,opt,name=ec,proto3,enum=lcm.agent.protobuf.ErrorCode" json:"ec,omitempty"`
	Data  *CreateTaskReplyData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Error string               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *CreateTaskReply) Reset() {
	*x = CreateTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskReply) ProtoMessage() {}

func (x *CreateTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskReply.ProtoReflect.Descriptor instead.
func (*CreateTaskReply) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{8}
}

func (x *CreateTaskReply) GetEc() ErrorCode {
	if x != nil {
		return x.Ec
	}
	return ErrorCode_EC_UNSPECIFIED
}

func (x *CreateTaskReply) GetData() *CreateTaskReplyData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CreateTaskReply) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type CreateTaskReplyData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,proto3" json:"task_id,omitempty"`
}

func (x *CreateTaskReplyData) Reset() {
	*x = CreateTaskReplyData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_agent_v1_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTaskReplyData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskReplyData) ProtoMessage() {}

func (x *CreateTaskReplyData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_agent_v1_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskReplyData.ProtoReflect.Descriptor instead.
func (*CreateTaskReplyData) Descriptor() ([]byte, []int) {
	return file_proto_agent_v1_task_proto_rawDescGZIP(), []int{9}
}

func (x *CreateTaskReplyData) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

var File_proto_agent_v1_task_proto protoreflect.FileDescriptor

var file_proto_agent_v1_task_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6c, 0x63, 0x6d,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x54, 0x0a, 0x02, 0x51, 0x41, 0x12,
	0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x22,
	0xa7, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x63, 0x6d, 0x64,
	0x5f, 0x71, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6c, 0x63, 0x6d, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x51,
	0x41, 0x52, 0x06, 0x63, 0x6d, 0x64, 0x5f, 0x71, 0x61, 0x22, 0x3c, 0x0a, 0x0a, 0x54, 0x61, 0x73,
	0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x64, 0x6f, 0x75,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x64, 0x6f, 0x75, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x64, 0x65, 0x72, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x64, 0x65, 0x72, 0x72, 0x22, 0xca, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74,
	0x12, 0x32, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x65, 0x6e,
	0x64, 0x5f, 0x61, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x36, 0x0a, 0x06,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6c,
	0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x06, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2a, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x02, 0x65, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x02, 0x65, 0x63, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x83, 0x01, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x2d, 0x0a, 0x02, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6c, 0x63,
	0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x02, 0x65, 0x63, 0x12, 0x2c,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c,
	0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x22, 0x93, 0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x02, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x02, 0x65, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x2f, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x2a, 0x87, 0x01, 0x0a, 0x09, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x43, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45,
	0x43, 0x5f, 0x45, 0x4f, 0x4b, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x43, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x10,
	0x02, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x43, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54,
	0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f,
	0x45, 0x43, 0x5f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x2a, 0x56, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x32, 0xb4, 0x03, 0x0a, 0x0b,
	0x54, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x6a, 0x0a, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x23, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x70, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x22, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x12, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x2f,
	0x7b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x5e, 0x0a, 0x09, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x22,
	0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x67, 0x0a, 0x07, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x7a, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x25, 0x2e, 0x6c,
	0x63, 0x6d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x7a, 0x42, 0xc3, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x6c, 0x63, 0x6d, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x42, 0x09, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x78, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x43,
	0x4d, 0x2f, 0x6c, 0x63, 0x6d, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0xa2, 0x02, 0x03, 0x4c, 0x41, 0x50, 0xaa, 0x02, 0x12, 0x4c, 0x63, 0x6d, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0xca, 0x02, 0x12, 0x4c, 0x63,
	0x6d, 0x5c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x5c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0xe2, 0x02, 0x1e, 0x4c, 0x63, 0x6d, 0x5c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x5c, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0xea, 0x02, 0x14, 0x4c, 0x63, 0x6d, 0x3a, 0x3a, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x3a, 0x3a,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_agent_v1_task_proto_rawDescOnce sync.Once
	file_proto_agent_v1_task_proto_rawDescData = file_proto_agent_v1_task_proto_rawDesc
)

func file_proto_agent_v1_task_proto_rawDescGZIP() []byte {
	file_proto_agent_v1_task_proto_rawDescOnce.Do(func() {
		file_proto_agent_v1_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_agent_v1_task_proto_rawDescData)
	})
	return file_proto_agent_v1_task_proto_rawDescData
}

var file_proto_agent_v1_task_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_agent_v1_task_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proto_agent_v1_task_proto_goTypes = []any{
	(ErrorCode)(0),                // 0: lcm.agent.protobuf.ErrorCode
	(TaskStatusEnum)(0),           // 1: lcm.agent.protobuf.TaskStatusEnum
	(*QA)(nil),                    // 2: lcm.agent.protobuf.QA
	(*TaskInput)(nil),             // 3: lcm.agent.protobuf.TaskInput
	(*TaskOutput)(nil),            // 4: lcm.agent.protobuf.TaskOutput
	(*TaskStatus)(nil),            // 5: lcm.agent.protobuf.TaskStatus
	(*Task)(nil),                  // 6: lcm.agent.protobuf.Task
	(*GetTaskRequest)(nil),        // 7: lcm.agent.protobuf.GetTaskRequest
	(*GetTaskReply)(nil),          // 8: lcm.agent.protobuf.GetTaskReply
	(*ListTasksReply)(nil),        // 9: lcm.agent.protobuf.ListTasksReply
	(*CreateTaskReply)(nil),       // 10: lcm.agent.protobuf.CreateTaskReply
	(*CreateTaskReplyData)(nil),   // 11: lcm.agent.protobuf.CreateTaskReplyData
	(*timestamppb.Timestamp)(nil), // 12: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 13: google.protobuf.Empty
	(*v1.HealthzResponse)(nil),    // 14: lcm.manager.protobuf.HealthzResponse
}
var file_proto_agent_v1_task_proto_depIdxs = []int32{
	2,  // 0: lcm.agent.protobuf.TaskInput.cmd_qa:type_name -> lcm.agent.protobuf.QA
	1,  // 1: lcm.agent.protobuf.TaskStatus.status:type_name -> lcm.agent.protobuf.TaskStatusEnum
	12, // 2: lcm.agent.protobuf.TaskStatus.start_at:type_name -> google.protobuf.Timestamp
	12, // 3: lcm.agent.protobuf.TaskStatus.end_at:type_name -> google.protobuf.Timestamp
	3,  // 4: lcm.agent.protobuf.Task.input:type_name -> lcm.agent.protobuf.TaskInput
	4,  // 5: lcm.agent.protobuf.Task.output:type_name -> lcm.agent.protobuf.TaskOutput
	5,  // 6: lcm.agent.protobuf.Task.status:type_name -> lcm.agent.protobuf.TaskStatus
	0,  // 7: lcm.agent.protobuf.GetTaskReply.ec:type_name -> lcm.agent.protobuf.ErrorCode
	6,  // 8: lcm.agent.protobuf.GetTaskReply.data:type_name -> lcm.agent.protobuf.Task
	0,  // 9: lcm.agent.protobuf.ListTasksReply.ec:type_name -> lcm.agent.protobuf.ErrorCode
	6,  // 10: lcm.agent.protobuf.ListTasksReply.data:type_name -> lcm.agent.protobuf.Task
	0,  // 11: lcm.agent.protobuf.CreateTaskReply.ec:type_name -> lcm.agent.protobuf.ErrorCode
	11, // 12: lcm.agent.protobuf.CreateTaskReply.data:type_name -> lcm.agent.protobuf.CreateTaskReplyData
	3,  // 13: lcm.agent.protobuf.TaskManager.CreateTask:input_type -> lcm.agent.protobuf.TaskInput
	7,  // 14: lcm.agent.protobuf.TaskManager.GetTask:input_type -> lcm.agent.protobuf.GetTaskRequest
	13, // 15: lcm.agent.protobuf.TaskManager.ListTasks:input_type -> google.protobuf.Empty
	13, // 16: lcm.agent.protobuf.TaskManager.Healthz:input_type -> google.protobuf.Empty
	10, // 17: lcm.agent.protobuf.TaskManager.CreateTask:output_type -> lcm.agent.protobuf.CreateTaskReply
	8,  // 18: lcm.agent.protobuf.TaskManager.GetTask:output_type -> lcm.agent.protobuf.GetTaskReply
	9,  // 19: lcm.agent.protobuf.TaskManager.ListTasks:output_type -> lcm.agent.protobuf.ListTasksReply
	14, // 20: lcm.agent.protobuf.TaskManager.Healthz:output_type -> lcm.manager.protobuf.HealthzResponse
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_proto_agent_v1_task_proto_init() }
func file_proto_agent_v1_task_proto_init() {
	if File_proto_agent_v1_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_agent_v1_task_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*QA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*TaskInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*TaskOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*TaskStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*GetTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListTasksReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_agent_v1_task_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTaskReplyData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_agent_v1_task_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_agent_v1_task_proto_goTypes,
		DependencyIndexes: file_proto_agent_v1_task_proto_depIdxs,
		EnumInfos:         file_proto_agent_v1_task_proto_enumTypes,
		MessageInfos:      file_proto_agent_v1_task_proto_msgTypes,
	}.Build()
	File_proto_agent_v1_task_proto = out.File
	file_proto_agent_v1_task_proto_rawDesc = nil
	file_proto_agent_v1_task_proto_goTypes = nil
	file_proto_agent_v1_task_proto_depIdxs = nil
}
