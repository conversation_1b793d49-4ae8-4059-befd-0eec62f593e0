package apiutil_test

import (
	"testing"

	"github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"

	. "github.com/onsi/gomega"
)

func Test_DuplicateFieldError(t *testing.T) {
	e := apiutil.NewDuplicateFieldError("a.b")
	g := NewGomegaWithT(t)

	g.Expect(e.Error(), "a.b is duplicate")
}

func Test_MissingFieldError(t *testing.T) {
	e := apiutil.NewMissingFieldError("a.b")
	g := NewGomegaWithT(t)

	g.Expect(e.Error(), "a.b is missing")
}
