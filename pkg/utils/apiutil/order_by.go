package apiutil

import (
	"fmt"
	"strings"
)

type FieldSortDirection string

const (
	FieldSortDirectionAsc  FieldSortDirection = "ASC"
	FieldSortDirectionDesc FieldSortDirection = "DESC"
)

type FieldSortDescriptor struct {
	FieldName string
}

type FieldSort struct {
	FieldName string
	Direction FieldSortDirection
}

func DecodeOrderBy(orderBy string, descriptors []*FieldSortDescriptor) ([]*FieldSort, error) {
	var sorts []*FieldSort

	strs := strings.Split(orderBy, ",")

OuterLoop:
	for _, str := range strs {
		var fieldName string
		var direction FieldSortDirection

		if strings.HasPrefix(str, "-") {
			fieldName = strings.TrimPrefix(str, "-")
			direction = FieldSortDirectionDesc
		} else {
			fieldName = str
			direction = FieldSortDirectionAsc
		}

		for _, descriptor := range descriptors {
			if descriptor.FieldName == "" {
				continue
			}

			if descriptor.FieldName == fieldName {
				sorts = append(sorts, &FieldSort{
					FieldName: fieldName,
					Direction: direction,
				})
				continue OuterLoop
			}
		}

		return nil, fmt.Errorf("unsupported field `%s`", fieldName)
	}

	return sorts, nil
}

func EncodeOrderBy(sorts []*FieldSort) (string, error) {
	strs := []string{}
	for _, sort := range sorts {
		if sort.Direction == FieldSortDirectionAsc {
			strs = append(strs, sort.FieldName)
		} else {
			strs = append(strs, "-"+sort.FieldName)
		}
	}
	return strings.Join(strs, ","), nil
}
