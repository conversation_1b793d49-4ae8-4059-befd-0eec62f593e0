package apiutil

import (
	"strconv"
)

func DecodePageToken(pageToken string) (int32, error) {
	if pageToken == "" {
		return 0, nil
	}

	i, err := strconv.ParseInt(pageToken, 10, 32)
	if err != nil {
		return 0, err
	}

	return int32(i), nil
}

func DecodePageSize(pageSize int32, defaultPageSize int32, maxPageSize int32) (int32, error) {
	if pageSize <= 0 {
		return defaultPageSize, nil
	}

	if pageSize > maxPageSize {
		return maxPageSize, nil
	}

	return pageSize, nil
}
