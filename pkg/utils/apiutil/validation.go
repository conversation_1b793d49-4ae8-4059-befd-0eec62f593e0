package apiutil

import (
	"fmt"

	"github.com/asaskevich/govalidator"
	"golang.org/x/text/language"
)

type MissingFieldError struct {
	FieldPath string
}

func NewMissingFieldError(fieldPath string) *MissingFieldError {
	return &MissingFieldError{FieldPath: fieldPath}
}

func (e *MissingFieldError) Error() string {
	return fmt.Sprintf("`%s` is missing", e.FieldPath)
}

type InvalidFieldError struct {
	FieldPath string
}

func NewInvalidFieldError(fieldPath string) *InvalidFieldError {
	return &InvalidFieldError{FieldPath: fieldPath}
}

func (e *InvalidFieldError) Error() string {
	return fmt.Sprintf("`%s` is invalid", e.FieldPath)
}

type ReadonlyFieldError struct {
	FieldPath string
}

func NewReadonlyFieldError(fieldPath string) *ReadonlyFieldError {
	return &ReadonlyFieldError{FieldPath: fieldPath}
}

func (e *ReadonlyFieldError) Error() string {
	return fmt.Sprintf("`%s` is readonly", e.FieldP<PERSON>)
}

type DuplicateFieldError struct {
	FieldPath string
}

func NewDuplicateFieldError(fieldPath string) *DuplicateFieldError {
	return &DuplicateFieldError{FieldPath: fieldPath}
}

func (e *DuplicateFieldError) Error() string {
	return fmt.Sprintf("`%s` is duplicate", e.FieldPath)
}

type LocalizedString struct {
	Locale string `protobuf:"bytes,1,opt,name=locale,proto3" json:"locale,omitempty"`
	Str    string `protobuf:"bytes,2,opt,name=str,proto3" json:"str,omitempty"`
}

func ValidateLocalizedString(ls *LocalizedString, fieldPath string) error {
	if ls.Locale == "" {
		return NewMissingFieldError(fieldPath + ".locale")
	}
	if _, err := language.Parse(ls.Locale); err != nil {
		return NewInvalidFieldError(fieldPath + ".locale")
	}

	return nil
}

type Address struct {
	Host string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Port int32  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
}

func ValidateAddress(address *Address, fieldPath string) error {
	if address.Host == "" {
		return NewMissingFieldError(fieldPath + ".host")
	}
	if !govalidator.IsHost(address.Host) {
		return NewInvalidFieldError(fieldPath + ".host")
	}

	if address.Port == 0 {
		return NewMissingFieldError(fieldPath + ".port")
	}
	if address.Port < 0 || address.Port > 65535 {
		return NewInvalidFieldError(fieldPath + ".port")
	}

	return nil
}
