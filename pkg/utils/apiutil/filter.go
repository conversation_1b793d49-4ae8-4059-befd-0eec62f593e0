package apiutil

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"time"

	pkgerr "github.com/pkg/errors"
)

type FieldFilterOperator string

const (
	FieldFilterOperatorIn           FieldFilterOperator = "IN"
	FieldFilterOperatorEq           FieldFilterOperator = "EQ"
	FieldFilterOperatorNe           FieldFilterOperator = "NE"
	FieldFilterOperatorLe           FieldFilterOperator = "LE"
	FieldFilterOperatorLt           FieldFilterOperator = "LT"
	FieldFilterOperatorGe           FieldFilterOperator = "GE"
	FieldFilterOperatorGt           FieldFilterOperator = "GT"
	FieldFilterOperatorLike         FieldFilterOperator = "LIKE"
	FieldFilterOperatorExists       FieldFilterOperator = "EXISTS"
	FieldFilterOperatorJSONContains FieldFilterOperator = "JSON_CONTAINS"
	FieldFilterOperatorJSONKeyIn    FieldFilterOperator = "JSON_KEY_IN"
)

type FieldFilterDescriptor struct {
	FieldPath        string
	FieldPathPattern string
	Operators        []FieldFilterOperator
	Value            interface{}
}

type FieldFilter struct {
	FieldPath string
	Operator  FieldFilterOperator
	Value     interface{}
}

func DecodeFilter(filter string, descriptors []*FieldFilterDescriptor) ([]*FieldFilter, error) {
	if filter == "" {
		return []*FieldFilter{}, nil
	}

	var items []interface{}
	if err := json.Unmarshal([]byte(filter), &items); err != nil {
		return nil, err
	}

	if len(items)%3 != 0 {
		return nil, errors.New("array length must be dividable by 3")
	}

	var filters []*FieldFilter

	for i := 0; i < len(items)/3; i++ {
		fieldPath, ok := items[3*i].(string)
		if !ok {
			return nil, errors.New("field path is not a string")
		}

		operator, ok := items[3*i+1].(string)
		if !ok {
			return nil, errors.New("operator is not a string")
		}

		f, err := decodeFieldFilter(fieldPath, FieldFilterOperator(operator), items[3*i+2], descriptors)
		if err != nil {
			return nil, err
		}

		filters = append(filters, f)
	}

	return filters, nil
}

type ValueConverter func(input interface{}) (output interface{}, err error)

func decodeFieldFilter(fieldPath string, operator FieldFilterOperator, value interface{}, descriptors []*FieldFilterDescriptor) (*FieldFilter, error) {
	for _, descriptor := range descriptors {
		if descriptor.FieldPath == "" && descriptor.FieldPathPattern == "" {
			continue
		}

		if descriptor.FieldPath != "" && descriptor.FieldPath != fieldPath {
			continue
		}

		if descriptor.FieldPathPattern != "" {
			matched, err := regexp.MatchString(descriptor.FieldPathPattern, fieldPath)
			if err != nil {
				return nil, err
			}

			if !matched {
				continue
			}
		}

		filter := &FieldFilter{
			FieldPath: fieldPath,
		}

		for _, op := range descriptor.Operators {
			if op == operator {
				filter.Operator = operator
				break
			}
		}

		if filter.Operator == "" {
			return nil, errors.New("unsupported operator")
		}

		switch descriptor.Value.(type) { //nolint: gocritic
		case string:
			s, ok := value.(string)
			if !ok {
				return nil, errors.New("value is not a string")
			}
			filter.Value = s
		case bool:
			b, ok := value.(bool)
			if !ok {
				return nil, errors.New("value is not a bool")
			}
			filter.Value = b
		case time.Time:
			s, ok := value.(string)
			if !ok {
				return nil, errors.New("value is not a string")
			}
			t, err := time.Parse(time.RFC3339, s)
			if err != nil {
				return nil, err
			}
			filter.Value = t
		case ValueConverter:
			converter := descriptor.Value.(ValueConverter) //nolint:errcheck
			val, err := converter(value)
			if err != nil {
				return nil, pkgerr.Wrap(err, "convert value")
			}
			filter.Value = val
		default:
			return nil, errors.New("unsupported value type")
		}

		return filter, nil
	}

	return nil, fmt.Errorf("unsupported field `%s`", fieldPath)
}

func EncodeFilter(filters []*FieldFilter) (string, error) {
	items := []interface{}{}
	for _, filter := range filters {
		items = append(items, filter.FieldPath, filter.Operator, filter.Value)
	}

	b, err := json.Marshal(items)
	if err != nil {
		return "", err
	}

	return string(b), nil
}
