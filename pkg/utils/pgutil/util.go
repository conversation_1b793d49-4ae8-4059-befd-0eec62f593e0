package pgutil

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"gorm.io/gorm"

	"github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
)

type RepoParams struct {
	DB *gorm.DB
}

var postgresOperator = map[apiutil.FieldFilterOperator]string{
	apiutil.FieldFilterOperatorLike:         " LIKE ?",
	apiutil.FieldFilterOperatorIn:           " IN ?",
	apiutil.FieldFilterOperatorEq:           " = ?",
	apiutil.FieldFilterOperatorNe:           " != ?",
	apiutil.FieldFilterOperatorLe:           " <= ?",
	apiutil.FieldFilterOperatorLt:           " < ?",
	apiutil.FieldFilterOperatorGe:           " >= ?",
	apiutil.FieldFilterOperatorGt:           " > ?",
	apiutil.FieldFilterOperatorExists:       " IS ",
	apiutil.FieldFilterOperatorJSONContains: " @> ?",
	apiutil.FieldFilterOperatorJSONKeyIn:    " ?| array[%s]::text[]",
}

func BuildSelector(db *gorm.DB, filters []*apiutil.FieldFilter) (*gorm.DB, error) {
	selector := db

	for _, filter := range filters {
		operator, ok := postgresOperator[filter.Operator]
		if !ok {
			panic(filter.Operator)
		}

		if filter.Operator == apiutil.FieldFilterOperatorExists {
			exists, ok := filter.Value.(bool)
			if !ok {
				return nil, errors.New("exists filter value must be bool")
			}
			if exists {
				operator = " IS NOT NULL"
			} else {
				operator = " IS NULL"
			}
			selector = selector.Where(filter.FieldPath + operator)
			continue
		}

		if filter.Operator == apiutil.FieldFilterOperatorJSONContains {
			if flg := json.Valid([]byte(filter.Value.(string))); !flg {
				return nil, errors.New("json_contains filter value must be json string")
			}
		}

		if filter.Operator == apiutil.FieldFilterOperatorIn {
			valueType := reflect.TypeOf(filter.Value)

			if valueType.Kind() == reflect.Slice {
				selector = selector.Where(filter.FieldPath+operator, filter.Value)
				continue
			}

			if valueType.Kind() == reflect.String {
				var value []interface{}
				if err := json.Unmarshal([]byte(filter.Value.(string)), &value); err != nil {
					return nil, errors.New("in filter value must be json string")
				}
				selector = selector.Where(filter.FieldPath+operator, value)
				continue
			}

			return nil, errors.New("in filter value must be slice or json string")
		}

		if filter.Operator == apiutil.FieldFilterOperatorJSONKeyIn {
			valueType := reflect.TypeOf(filter.Value)

			if valueType.Kind() != reflect.String {
				return nil, errors.New("json_key_in filter value must be string")
			}
			var value []string
			if err := json.Unmarshal([]byte(filter.Value.(string)), &value); err != nil {
				return nil, errors.New("json_key_in filter value must be string array")
			}
			jsonKeys := strings.Split(filter.FieldPath, ".")
			if len(jsonKeys) < 2 {
				return nil, errors.New("json_key_in filter field path format error, e.g. foo.bar, foo.bar.foo")
			}
			fieldPath := jsonKeys[0]
			for i := 1; i < len(jsonKeys); i++ {
				fieldPath += fmt.Sprintf("->'%s'", jsonKeys[i])
			}

			selector = selector.Where(fmt.Sprintf(fieldPath+operator, fmt.Sprintf("'%s'", strings.Join(value, "','"))))
			continue
		}

		selector = selector.Where(filter.FieldPath+operator, filter.Value)
	}

	return selector, nil
}
