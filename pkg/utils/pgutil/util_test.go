package pgutil

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
)

func Test_BuildSelector_JsonKeyIn(t *testing.T) {
	mockDb, _, _ := sqlmock.New()
	dialector := postgres.New(postgres.Config{
		Conn:       mockDb,
		DriverName: "postgres",
	})
	db, _ := gorm.Open(dialector, &gorm.Config{})

	filters := []*apiutil.FieldFilter{
		{
			"alertname",
			apiutil.FieldFilterOperatorJSONKeyIn,
			1,
		},
	}
	_, err := BuildSelector(db, filters)
	assert.EqualError(t, err, "json_key_in filter value must be string")

	filters = []*apiutil.FieldFilter{
		{
			"alertname",
			apiutil.FieldFilterOperatorJSONKeyIn,
			"1",
		},
	}
	_, err = BuildSelector(db, filters)
	assert.EqualError(t, err, "json_key_in filter value must be string array")

	filters = []*apiutil.FieldFilter{
		{
			"alertname",
			apiutil.FieldFilterOperatorJSONKeyIn,
			"[\"test_alertname\"]",
		},
	}
	_, err = BuildSelector(db, filters)
	assert.EqualError(t, err, "json_key_in filter field path format error, e.g. foo.bar, foo.bar.foo")

	filters = []*apiutil.FieldFilter{
		{
			"labels.alertname",
			apiutil.FieldFilterOperatorJSONKeyIn,
			"[\"test_alertname\"]",
		},
	}
	selector, err := BuildSelector(db, filters)
	assert.Nil(t, err)
	assert.Equal(t, selector.Statement.Clauses["WHERE"].Expression.(clause.Where).Exprs[0].(clause.Expr).SQL, "labels->'alertname' ?| array['test_alertname']::text[]")

	filters = []*apiutil.FieldFilter{
		{
			"labels.alertname.test",
			apiutil.FieldFilterOperatorJSONKeyIn,
			"[\"test_alertname1\",\"test_alertname2\"]",
		},
	}
	selector, err = BuildSelector(db, filters)
	assert.Nil(t, err)
	assert.Equal(t, selector.Statement.Clauses["WHERE"].Expression.(clause.Where).Exprs[0].(clause.Expr).SQL, "labels->'alertname'->'test' ?| array['test_alertname1','test_alertname2']::text[]")
}
