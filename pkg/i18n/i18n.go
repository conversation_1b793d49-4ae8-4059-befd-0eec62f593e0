package i18n

import (
	"embed"
	"strings"

	"github.com/eduardolat/goeasyi18n"
)

//go:embed translations
var translationsFS embed.FS

func InitializeI18n() *goeasyi18n.I18n {
	i18n := goeasyi18n.NewI18n()

	enTranslations, err := goeasyi18n.LoadFromYamlFS(
		translationsFS,
		"translations/en.yaml",
	)
	if err != nil {
		panic(err)
	}

	zhTranslations, err := goeasyi18n.LoadFromYamlFS(
		translationsFS,
		"translations/zh.yaml",
	)
	if err != nil {
		panic(err)
	}

	enVmwareTranslations, err := goeasyi18n.LoadFromYamlFS(
		translationsFS,
		"translations/en-vmware.yaml",
	)
	if err != nil {
		panic(err)
	}

	zhVmwareTranslations, err := goeasyi18n.LoadFromYamlFS(
		translationsFS,
		"translations/zh-vmware.yaml",
	)
	if err != nil {
		panic(err)
	}

	i18n.AddLanguage("en", enTranslations)
	i18n.AddLanguage("zh", zhTranslations)
	i18n.AddLanguage("en-vmware", enVmwareTranslations)
	i18n.AddLanguage("zh-vmware", zhVmwareTranslations)

	return i18n
}

func FormatList(items []string, lang string) string {
	var separator string
	if lang == "zh" {
		separator = "、"
	} else {
		separator = ", "
	}

	return strings.Join(items, separator)
}
