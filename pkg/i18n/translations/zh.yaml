- Key: RoleConvertCheckTowerTask
  Default: "主机 {{.HostName}} 角色转换前检查"

- Key: RoleConvertTowerTask
  Default: "将主机 {{.HostName}} 的角色转换为{{.RoleName}}"

- Key: RoleConvertTowerAuditEvent
  Default: "将主机 {{.HostName}} 的角色转换为{{.RoleName}}"

- Key: RoleNameMasterNode
  Default: "主节点"

- Key: RoleNameStorageNode
  Default: "存储节点"

- Key: RoleNameComputeNode
  Default: "计算节点"

- Key: ZoneNamePrimaryZone
  Default: "优先可用域"

- Key: ZoneNameSecondaryZone
  Default: "次级可用域"

- Key: RoleConvertToStorageCheckHostsStatusDesc
  Default: "集群中全部主机处于健康或无响应状态"

- Key: RoleConvertToStorageCheckHostsStatusFailed
  Default: "主机 {{.HostNames}} 不处于健康或无响应状态。"

- Key: CheckTargetHostStatusToMasterDesc
  Default: "当前主机处于健康状态、且不处于维护模式状态"

- Key: CheckTargetHostStatusToMasterFailed
  Default: "请将主机退出维护模式后，再转换。"

- Key: RoleConvertToMasterCheckOtherHostStatusDesc
  Default: "集群中其他主机处于健康状态"

- Key: RoleConvertToMasterCheckOtherHostStatusFailed
  Default: "主机 {{.HostNames}} 不处于健康状态。"

- Key: RoleConvertCheckClusterMasterCountDesc
  Default: "当前主机转换角色后，集群中主节点数量满足要求"

- Key: RoleConvertCheckClusterMasterCountWarning
  Default: "{{.Count}}"

- Key: RoleConvertCheckClusterMasterCountFailed
  Default: "{{.Count}}"

- Key: RoleConvertCheckExistRemovingHostDesc
  Default: "集群中没有正在移除或移除失败的主机"

- Key: RoleConvertCheckExistRemovingHostFailed
  Default: "请先将主机 {{.HostName}} 成功移除。"

- Key: RoleConvertCheckExistConvertingRoleHostDesc
  Default: "集群中没有正在转换角色的主机或转换失败的其他主机"

- Key: RoleConvertCheckExistConvertingRoleHostFailed
  Default: "请先将主机 {{.HostName}} 成功转换角色。"

- Key: CostTimeEvaluateDesc
  Default: "预计花费时间"

- Key: CostTimeEvaluateSuccess
  Default: "{{.CostTime}} 分钟"

- Key: RoleConvert2ndCheckFailed
  Default: "角色转换二次前置检查失败，请重试。"

# Remove Host
- Key: RemoveHostCheckTowerTask
  Default: "移除主机 {{.HostName}} 前检查"

- Key: RemoveHostTowerTask
  Default: "移除主机 {{.HostName}}"

- Key: RemoveHostTowerAuditEvent
  Default: "移除主机 {{.HostName}}"

- Key: RemoveHostCheckTargetHostStatusDesc
  Default: "当前主机处于无响应状态或维护模式状态"

- Key: RemoveHostCheckTargetHostStatusWarning
  Default: "当前主机处于无响应状态。"

- Key: RemoveHostCheckTargetHostStatusFailed
  Default: "请将主机置于维护模式后，再移除。"

- Key: RemoveHostCheckHostExistVMsDesc
  Default: "当前主机上没有回收站之外的虚拟机"

- Key: ZBS.RemoveHostCheckHostExistVMsDesc
  Default: "当前主机上没有系统服务虚拟机"

- Key: RemoveHostCheckHostExistVMsWarning
  Default: "不支持获取 VMware ESXi 平台的虚拟机，请确保当前主机上无虚拟机。"

- Key: RemoveHostCheckMountedUsbDevicesDesc
  Default: "当前主机上没有被虚拟机挂载的 USB 设备"

- Key: RemoveHostCheckMountedUsbDevicesFailed
  Default: "{{.UsbDevices}}"

- Key: RemoveHostCheckTargetHostRoleDesc
  Default: "当前主机不是主节点"

- Key: RemoveHostCheckTargetHostRoleFailed
  Default: "当前主机为主节点，不支持移除。请转换节点角色后，再移除。"

- Key: RemoveHostCheckClusterMasterCountDesc
  Default: "集群中主节点数量满足要求"

- Key: RemoveHostCheckClusterMasterCountWarning
  Default: "集群中仅存在 {{.Count}} 个健康的主节点。"

- Key: RemoveHostCheckClusterMasterCountFailed
  Default: "集群中仅存在 {{.Count}} 个健康的主节点。"

- Key: RemoveHostCheckZBSChunkUseStateDesc
  Default: "集群中其他主机的所有存储服务不处于 removing 或 idle 状态"

- Key: RemoveHostCheckZBSChunkUseStateFailed
  Default: "{{.HostNames}}"

- Key: RemoveHostCheckClusterStorageECDesc
  Default: "移除当前主机后，集群中可提供存储服务的主机数满足纠删码卷的要求"

- Key: RemoveHostCheckClusterStorageECFailed
  Default: "移除当前主机后，包含健康且非移除中的存储服务的主机将少于 {{.Count}} 个，无法保证删码卷数据安全。"

- Key: RemoveHostCheckClusterCapacitySpaceDesc
  Default: "集群中其他主机的空闲存储空间充足"

- Key: RemoveHostCheckClusterCapacitySpaceFailed
  Default: "预计额外需要 {{.Capacity}} 的存储空间。"

- Key: RemoveHostCheckPlacementGroupsDesc
  Default: "集群中没有涉及当前主机的虚拟机放置组"

- Key: RemoveHostCheckPlacementGroupsFailed
  Default: "{{.PlacementGroups}}"

- Key: RemoveHostCheckExistRemovingHostDesc
  Default: "集群中没有正在移除或移除失败的其他主机"

- Key: RemoveHostCheckExistRemovingHostFailed
  Default: "请先将主机 {{.HostName}} 成功移除。"

- Key: RemoveHostCheckExistConvertingRoleHostDesc
  Default: "集群中没有正在转换角色或转换失败的主机"

- Key: RemoveHostCheckExistConvertingRoleHostFailed
  Default: "请先将主机 {{.HostName}} 成功转换角色。"

- Key: RemoveHostCheckClusterExistPextentDeadDesc
  Default: "集群中没有 dead pextent"

- Key: RemoveHostCheckMetroClusterHealthyHostCountDesc
  Default: "移除当前主机后，优先可用域中至少有 2 个健康的主机，次级可用域中至少有 1 个健康的主机"

- Key: RemoveHostCheckMetroClusterHealthyHostCountFailed
  Default: "{{.Zone}}中健康主机数量不满足要求。"

- Key: RemoveHost2ndCheckFailed
  Default: "移除主机二次前置检查失败，请重试。"

# Common precheck items
- Key: CheckZkMongoServiceStatusDesc
  Default: "集群 ZooKeeper 及 MongoDB 服务正常运行"

- Key: CheckClusterExtendingDesc
  Default: "集群中没有正在添加的主机"

- Key: CheckClusterExtendingFailed
  Default: "正在添加主机 {{.HostNames}}，请等待主机添加完成。"

- Key: CheckEnteringMaintenanceHostDesc
  Default: "集群中没有正在进入维护模式的主机"

- Key: CheckEnteringMaintenanceHostFailed
  Default: "主机 {{.HostName}} 正在进入维护模式。"

- Key: CheckExistMaintenanceHostDesc
  Default: "集群中没有处于维护模式的其他主机"

- Key: CheckExistMaintenanceHostFailed
  Default: "主机 {{.HostName}} 处于维护模式状态，请先完成其维护任务、并退出维护模式。"

- Key: CheckClusterExistPextentRecoverDesc
  Default: "集群中没有数据恢复"

- Key: CheckClusterUpgradingDesc
  Default: "集群不处于升级中状态"

- Key: CheckWitnessNodeStatusDesc
  Default: "仲裁节点处于健康状态"

# --- Time Sync ---
- Key: TimeSyncWithNtpServerTowerTask
  Default: "集群与外部 NTP 服务器同步时间"

- Key: TimeSyncWithNtpServerTowerAuditEvent
  Default: "集群与外部 NTP 服务器同步时间"

- Key: TimeSyncInternalTowerTask
  Default: "同步集群时间到 {{.Time}}"

- Key: TimeSyncInternalTowerAuditEvent
  Default: "同步集群时间到 {{.Time}}"

#  --- RDMA Toggle ---
- Key: SetRdmaOffTowerTask
  Default: "关闭 RDMA"

- Key: SetRdmaOffTowerAuditEvent
  Default: "关闭 RDMA"

- Key: SetRdmaOnTowerTask
  Default: "开启 RDMA"

- Key: SetRdmaOnTowerAuditEvent
  Default: "开启 RDMA"
