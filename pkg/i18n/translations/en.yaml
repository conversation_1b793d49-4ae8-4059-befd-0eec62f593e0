- Key: RoleConvertCheckTowerTask
  Default: "Host {{.HostName}} role convert precheck"

- Key: RoleConvertTowerTask
  Default: "Convert the host {{.HostName}} to {{.RoleName}}"

- Key: RoleConvertTowerAuditEvent
  Default: "Convert the host {{.HostName}} to {{.RoleName}}"

- Key: RoleNameMasterNode
  Default: "master"

- Key: RoleNameStorageNode
  Default: "storage"

- Key: RoleNameComputeNode
  Default: "compute"

- Key: ZoneNamePrimaryZone
  Default: "primary availability zone"

- Key: ZoneNameSecondaryZone
  Default: "secondary availability zone"

- Key: RoleConvertToStorageCheckHostsStatusDesc
  Default: "All hosts in the cluster are in the healthy or not responding state"
  One: "All hosts in the cluster are in the healthy or not responding state"
  Many: "All hosts in the cluster are in the healthy or not responding state"

- Key: RoleConvertToStorageCheckHostsStatusFailed
  One: "The host {{.HostNames}} is not in the healthy or not responding state."
  Many: "The hosts {{.HostNames}} are not in the healthy or not responding state."

- Key: CheckTargetHostStatusToMasterDesc
  Default: "The current host is healthy and not in maintenance mode"

- Key: CheckTargetHostStatusToMasterFailed
  Default: "Please try again after the host exits maintenance mode."

- Key: RoleConvertToMasterCheckOtherHostStatusDesc
  Default: "Other hosts in the cluster are in the healthy state"
  One: "Other hosts in the cluster are in the healthy state"
  Many: "Other hosts in the cluster are in the healthy state"

- Key: RoleConvertToMasterCheckOtherHostStatusFailed
  One: "The host {{.HostNames}} is not in the healthy state."
  Many: The hosts {{.HostNames}} are not in the healthy state.

- Key: RoleConvertCheckClusterMasterCountDesc
  Default: "The number of master nodes in this cluster will meet the requirement after conversion"

- Key: RoleConvertCheckClusterMasterCountWarning
  Default: "{{.Count}}"

- Key: RoleConvertCheckClusterMasterCountFailed
  Default: "{{.Count}}"

- Key: RoleConvertCheckExistRemovingHostDesc
  Default: "No host in the cluster is being removed or has failed in doing so"

- Key: RoleConvertCheckExistRemovingHostFailed
  Default: "Please remove the host {{.HostName}} first."

- Key: RoleConvertCheckExistConvertingRoleHostDesc
  Default: "No other host in the cluster is converting its role or has failed in doing so"

- Key: RoleConvertCheckExistConvertingRoleHostFailed
  Default: "Please convert the role of the host {{.HostName}} first."

- Key: CostTimeEvaluateDesc
  Default: "Cost time evaluate"

- Key: CostTimeEvaluateSuccess
  Default: "{{.CostTime}} minutes"

- Key: RoleConvert2ndCheckFailed
  Default: "Role convert second precheck failed, please retry."

# Remove Host
- Key: RemoveHostCheckTowerTask
  Default: "Remove host {{.HostName}} precheck"

- Key: RemoveHostTowerTask
  Default: "Remove the host {{.HostName}}"

- Key: RemoveHostTowerAuditEvent
  Default: "Remove the host {{.HostName}}"

- Key: RemoveHostCheckTargetHostStatusDesc
  Default: "The current host is not responding or in maintenance mode"

- Key: RemoveHostCheckTargetHostStatusWarning
  Default: "The current host is not responding."

- Key: RemoveHostCheckTargetHostStatusFailed
  Default: "Please remove the host after it enters maintenance mode."

- Key: RemoveHostCheckHostExistVMsDesc
  Default: "No virtual machines on the current host except those in the recycle bin"

- Key: ZBS.RemoveHostCheckHostExistVMsDesc
  Default: "No virtual machines running system services on the current host"

- Key: RemoveHostCheckHostExistVMsWarning
  Default: "Please ensure that there are no virtual machines on the current host as the system cannot retrieve information about virtual machines on the VMware ESXi platform."

- Key: RemoveHostCheckMountedUsbDevicesDesc
  Default: "No USB devices mounted on virtual machines on the host"

- Key: RemoveHostCheckMountedUsbDevicesFailed
  Default: "{{.UsbDevices}}"

- Key: RemoveHostCheckTargetHostRoleDesc
  Default: "The current host is not a master node"

- Key: RemoveHostCheckTargetHostRoleFailed
  Default: "Unable to remove the current host for it is a master node. Please try again after converting its role."

- Key: RemoveHostCheckClusterMasterCountDesc
  Default: "The number of master nodes in this cluster meets the requirement"

- Key: RemoveHostCheckClusterMasterCountWarning
  Default: "The number of healthy master nodes in the cluster is {{.Count}}."

- Key: RemoveHostCheckClusterMasterCountFailed
  Default: "The number of healthy master nodes in the cluster is {{.Count}}."

- Key: RemoveHostCheckZBSChunkUseStateDesc
  Default: "The storage services on other hosts in the cluster is not in the removing or idle state"

- Key: RemoveHostCheckZBSChunkUseStateFailed
  Default: "{{.HostNames}}"

- Key: RemoveHostCheckClusterStorageECDesc
  Default: "After removing the current host, the number of hosts in the cluster that can provide storage services meets the requirements of the erasure-coded volume"

- Key: RemoveHostCheckClusterStorageECFailed
  Default: "The data security of erasure coded volumes cannot be guaranteed for the hosts that provide the healthy storage services and are not being removed in the cluster will be less than {{.Count}} after removing the current host."

- Key: RemoveHostCheckClusterCapacitySpaceDesc
  Default: "The available storage capacity of other hosts in the cluster is sufficient"

- Key: RemoveHostCheckClusterCapacitySpaceFailed
  Default: "Estimated additional storage space required: {{.Capacity}}."

- Key: RemoveHostCheckPlacementGroupsDesc
  Default: "No VM placement group policies for current host exist"

- Key: RemoveHostCheckPlacementGroupsFailed
  Default: "{{.PlacementGroups}}"

- Key: RemoveHostCheckExistRemovingHostDesc
  Default: "No other host in the cluster is being removed or has failed in doing so"

- Key: RemoveHostCheckExistRemovingHostFailed
  Default: "Please remove the host {{.HostName}} first."

- Key: RemoveHostCheckExistConvertingRoleHostDesc
  Default: "No host in the cluster is converting its role or has failed in doing so"

- Key: RemoveHostCheckExistConvertingRoleHostFailed
  Default: "Please convert the role of the host {{.HostName}} first."

- Key: RemoveHostCheckClusterExistPextentDeadDesc
  Default: "No dead pextent in the cluster"

- Key: RemoveHostCheckMetroClusterHealthyHostCountDesc
  Default: "After removing the current host, the number of healthy hosts in the primary availability zone must be no less than 2, the number of healthy hosts in the secondary availability zone must be no less than 1"

- Key: RemoveHostCheckMetroClusterHealthyHostCountFailed
  Default: "The number of healthy hosts in {{.Zone}} can't meets the requirement."

- Key: RemoveHost2ndCheckFailed
  Default: "Removing host second precheck failed, please retry."

# Common precheck items
- Key: CheckZkMongoServiceStatusDesc
  Default: "The ZooKeeper and MongoDB services in the cluster are running properly"

- Key: CheckClusterExtendingDesc
  Default: "Not adding new hosts to the cluster"

- Key: CheckClusterExtendingFailed
  Default: "Adding the host {{.HostNames}} to the cluster now. Please try again after it is completed."

- Key: CheckEnteringMaintenanceHostDesc
  Default: "No host in the cluster is entering maintenance mode"

- Key: CheckEnteringMaintenanceHostFailed
  Default: "The host {{.HostName}} is entering maintenance mode."

- Key: CheckExistMaintenanceHostDesc
  Default: "No other host in the cluster is in maintenance mode"

- Key: CheckExistMaintenanceHostFailed
  Default: "The host {{.HostName}} is in maintenance mode. Please complete its maintenance task and exit its maintenance mode."

- Key: CheckClusterExistPextentRecoverDesc
  Default: "No data recovery in progress in the cluster"

- Key: CheckClusterUpgradingDesc
  Default: "The cluster is not upgrading"

- Key: CheckWitnessNodeStatusDesc
  Default: "The witness node in the active-active cluster is healthy"

# --- Time Sync ---
- Key: TimeSyncWithNtpServerTowerTask
  Default: "Sync cluster time with external NTP server"

- Key: TimeSyncWithNtpServerTowerAuditEvent
  Default: "Sync cluster time with external NTP server"

- Key: TimeSyncInternalTowerTask
  Default: "Sync cluster time to {{.Time}}"

- Key: TimeSyncInternalTowerAuditEvent
  Default: "Sync cluster time to {{.Time}}"

  #  --- RDMA Toggle ---
- Key: SetRdmaOffTowerTask
  Default: "Turn off RDMA"

- Key: SetRdmaOffTowerAuditEvent
  Default: "Turn Off RDMA"

- Key: SetRdmaOnTowerTask
  Default: "Turn On RDMA"

- Key: SetRdmaOnTowerAuditEvent
  Default: "Turn On RDMA"
