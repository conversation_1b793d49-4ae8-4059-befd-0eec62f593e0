package towerclient

import (
	"context"
	"fmt"

	"gopkg.in/yaml.v2"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	kubeclient "github.smartx.com/LCM/lcm-manager/pkg/client/kube"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	tower "github.smartx.com/LCM/lcm-manager/third_party/tower"
)

type towerServerYAML struct {
	Server        string `json:"server" yaml:"server"`
	AuthMode      string `json:"authMode" yaml:"authMode"`
	Username      string `json:"username" yaml:"username"`
	Password      string `json:"password" yaml:"password"`
	SkipTLSVerify bool   `json:"skipTLSVerify" yaml:"skipTLSVerify"`
}

const (
	cloudtowerSecretName = "cloudtower-server"
	cloudtowerSecretKey  = "cloudtower.yaml" //nolint:gosec
	cloudtowerNamespace  = "cloudtower-system"
)

func getTowerServerYamlFromSecret(ctx context.Context) (*towerServerYAML, error) {
	kubeClient, err := kubeclient.NewClientset()
	if err != nil {
		return nil, err
	}

	secret, err := kubeClient.CoreV1().Secrets(cloudtowerNamespace).Get(ctx, cloudtowerSecretName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	data := secret.Data[cloudtowerSecretKey]
	if len(data) == 0 {
		return nil, fmt.Errorf("secret %s/%s is empty", cloudtowerNamespace, cloudtowerSecretName)
	}

	var opt towerServerYAML

	if err := yaml.Unmarshal(data, &opt); err != nil {
		return nil, err
	}

	return &opt, nil
}

func NewTowerClient() (tower.Client, error) {
	towerEndpoint := config.TowerEndpoint
	towerPrismaEndpoint := config.TowerPrismaEndpoint
	towerAdminEndpoint := config.TowerAdminEndpoint

	towerServerConfig, err := getTowerServerYamlFromSecret(context.Background())
	if err != nil {
		return nil, err
	}

	towerUsername := towerServerConfig.Username
	towerPassword := towerServerConfig.Password
	towerUsersource := towerServerConfig.AuthMode

	return tower.NewClient(
		towerEndpoint,
		towerServerConfig.Server,
		towerUsername,
		towerPassword,
		tower.UserSource(towerUsersource),
		towerPrismaEndpoint,
		towerAdminEndpoint,
	)
}
