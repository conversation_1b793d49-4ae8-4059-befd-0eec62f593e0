package shell

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"
)

const (
	stdTypeStdout = "stdout"
	stdTypeStderr = "stderr"
)

type Client struct {
	TrimNewline bool
	Envs        map[string]string
}

func (c *Client) RunCommand(command string, timeout int) (string, string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	var (
		stderrBuf bytes.Buffer
		stdoutBuf bytes.Buffer
	)

	slog.Info("run bash command", "command", command)
	cmd := exec.CommandContext(ctx, "bash", "-c", command)
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf

	for k, v := range c.Envs {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
	}

	if err := cmd.Run(); err != nil {
		return "", "", err
	}

	if c.TrimNewline {
		return strings.TrimRight(stdoutBuf.String(), "\n"), strings.TrimRight(stderrBuf.String(), "\n"), nil
	}

	return stdoutBuf.String(), stderrBuf.String(), nil
}

func (c *Client) RunCommandContext(ctx context.Context, command string) error {
	cmd := exec.CommandContext(ctx, "bash", "-c", command)
	for k, v := range c.Envs {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
	}

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return err
	}
	defer stdout.Close()

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return err
	}
	defer stderr.Close()

	var wg sync.WaitGroup

	wg.Add(2) //nolint:gomnd

	go readCmdOutput(ctx, &wg, stdout, stdTypeStdout)
	go readCmdOutput(ctx, &wg, stderr, stdTypeStderr)

	err = cmd.Start()

	wg.Wait()

	if err != nil {
		return err
	}

	return cmd.Wait()
}

func readCmdOutput(ctx context.Context, wg *sync.WaitGroup, std io.ReadCloser, stdType string) {
	reader := bufio.NewReader(std)

	defer wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			readString, err := reader.ReadString('\n')

			if err != nil || err == io.EOF {
				return
			}

			switch stdType {
			case stdTypeStdout:
				fmt.Fprint(os.Stdout, readString)

			case stdTypeStderr:
				fmt.Fprint(os.Stderr, readString)
			}
		}
	}
}
