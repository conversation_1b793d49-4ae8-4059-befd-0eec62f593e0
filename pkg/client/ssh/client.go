package sshclient

import (
	"context"
	"time"

	"github.com/melbahja/goph"
)

type Client struct {
	*goph.Client
}

func NewClient(address, username, privateKey string) (*Client, error) {
	auth, err := goph.Key(privateKey, "")
	if err != nil {
		return nil, err
	}

	client, err := goph.NewUnknown(username, address, auth)
	if err != nil {
		return nil, err
	}

	return &Client{Client: client}, nil
}

func (c *Client) NewCommand(ctx context.Context, command string) (*goph.Cmd, error) {
	return c.CommandContext(ctx, command)
}

func (c *Client) RunCommandWithTimeout(command string, timeout int) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	return c.RunContext(ctx, command)
}
