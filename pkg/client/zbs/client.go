package zbsclient

import (
	"context"
	"encoding/binary"
	"errors"
	"fmt"
	"log/slog"
	"net"

	zbspb "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/zbs"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
	"github.com/iomesh/zbs-client-go/zbs/zrpc"
)

const ZbsStoragePoolDefaultName string = "system"

// 10206: chunk service (meta proxy)
// 10100: meta service
const (
	MetaPort  = 10100
	ChunkPort = 10206
)

type ECMaxKM struct {
	MaxK  uint32
	MaxM  uint32
	MaxKM uint32
}

var ErrChunkNotFound = errors.New("chunk not found")

type Error struct {
	Op  string
	Err error

	Seg *ZbsClient
}

func (e *Error) Error() string {
	return fmt.Sprintf("%s: %v: %s", e.Op, e.Seg, e.Err)
}

func (e *Error) Unwrap() error {
	return e.Err
}

type ZbsClient struct {
	client *zbs.Client
}

func NewZbsClient(ip string) (*ZbsClient, error) {
	config := zrpc.DefaultClientConfig()
	config.RpcTimeoutMS = 5_000 // 5s
	config.Addr = fmt.Sprintf("%s:%d", ip, ChunkPort)

	client, err := zbs.NewClientWithConfig(config)
	if err != nil {
		return nil, err
	}

	return &ZbsClient{
		client: client,
	}, nil
}

func (c *ZbsClient) IsExistPextendDead() (bool, error) {
	ctx := context.Background()
	res, err := c.client.Meta.FindPExtent(ctx, meta.PExtentStatus_PEXTENT_DEAD)
	if err != nil {
		return false, err
	}
	if res.Pextents != nil {
		return true, nil
	}
	return false, nil
}

func (c *ZbsClient) IsExistPextendNeedRecover() (bool, error) {
	ctx := context.Background()
	res, err := c.client.Meta.FindPExtent(ctx, meta.PExtentStatus_PEXTENT_NEED_RECOVER)
	if err != nil {
		return false, err
	}
	if res.Pextents != nil {
		if len(res.Pextents) > 0 {
			return true, nil
		}
	}
	return false, nil
}

func (c *ZbsClient) IsTieringEnabled(ctx context.Context) (bool, error) {
	res, err := c.client.Status.GetClusterSummary(ctx)
	if err != nil {
		return false, err
	}

	if res.ClusterInfo != nil && res.ClusterInfo.NegotiatedConfig != nil && res.ClusterInfo.NegotiatedConfig.EnableTiering != nil && *res.ClusterInfo.NegotiatedConfig.EnableTiering {
		return true, nil
	}

	return false, nil
}

func (c *ZbsClient) GetSystemStoragePool(ctx context.Context) (*zbspb.StoragePool, error) {
	res, err := c.client.Status.GetClusterSummary(ctx)
	if err != nil {
		return nil, err
	}

	for _, storagePool := range res.StoragePools {
		if storagePool == nil || storagePool.Name == nil {
			continue
		}

		if string(storagePool.Name) == ZbsStoragePoolDefaultName {
			return storagePool, nil
		}
	}

	slog.Info("No system storage pool.")

	return nil, nil
}

func (c *ZbsClient) GetChunkByIP(ctx context.Context, targetChunkIP string) (*zbspb.Chunk, error) {
	slog.Info("getting chunk by ip", "ip", targetChunkIP)

	res, err := c.client.Meta.Chunk.ListChunk(ctx)
	if err != nil {
		return nil, err
	}

	for _, chunk := range res.Chunks {
		if chunk == nil || chunk.RpcIp == nil {
			continue
		}

		ipStr := IntToIP(*chunk.RpcIp)

		if ipStr == targetChunkIP {
			return chunk, nil
		}
	}

	slog.Info("chunk with target ip not exist.", "ip", targetChunkIP)

	return nil, &Error{"chunk with target ip not exist.", ErrChunkNotFound, c}
}

func (c *ZbsClient) GetAllPools(ctx context.Context) ([]*meta.Pool, error) {
	slog.Info("storage meta get all pools")

	pos, num := uint64(0), zbspb.Default_Pagination_Num
	allPools := make([]*meta.Pool, 0)

	for {
		listPoolRequest := &meta.ListPoolRequest{Pagination: &zbspb.Pagination{Pos: &pos, Num: &num}}

		res, err := c.client.Meta.ListPool(ctx, listPoolRequest)
		if err != nil {
			return nil, err
		}

		allPools = append(allPools, res.Pools...)

		if len(res.Pools) < int(num) {
			break
		}

		pos += num
	}

	return allPools, nil
}

func (c *ZbsClient) CheckExistECVolumePool(ctx context.Context) (bool, error) {
	slog.Info("Check exist EC Volume pool.")

	allPools, err := c.GetAllPools(ctx)
	if err != nil {
		return false, err
	}

	for _, pool := range allPools {
		if pool == nil || pool.ResiliencyType == nil || pool.EcParam == nil || *pool.ResiliencyType != zbspb.ResiliencyType_RT_EC {
			continue
		}

		return true, nil
	}

	slog.Info("No volume pool with resiliency type RT_EC found.")

	return false, nil
}

func (c *ZbsClient) GetECMaxKM(ctx context.Context) (*ECMaxKM, error) {
	slog.Info("Fetching EC max k & m values.")

	allPools, err := c.GetAllPools(ctx)
	if err != nil {
		return nil, err
	}

	var maxKM, maxK, maxM uint32

	for _, pool := range allPools {
		if pool == nil || pool.ResiliencyType == nil || pool.EcParam == nil || *pool.ResiliencyType != zbspb.ResiliencyType_RT_EC {
			continue
		}

		ec := pool.EcParam
		if ec.K != nil && *ec.K > maxK {
			maxK = *ec.K
		}

		if ec.M != nil && *ec.M > maxM {
			maxM = *ec.M
		}

		if (ec.K != nil && ec.M != nil) && (*ec.K+*ec.M) > maxKM {
			slog.Info("get larger EC K+M", "pool_name", pool.Name, "K", *ec.K, "M", *ec.M)
			maxKM = *ec.K + *ec.M
		}
	}

	if maxK > 0 || maxM > 0 {
		ecMaxKM := &ECMaxKM{MaxK: maxK, MaxM: maxM, MaxKM: maxKM}
		slog.Info("EC max k & m calculated", "ecMaxKM", ecMaxKM)

		return ecMaxKM, nil
	}

	slog.Info("No pools with resiliency type RT_EC found.")

	return nil, nil
}

func (c *ZbsClient) CheckClusterECWithOfflineChunks(ctx context.Context, targetChunkIP string, ecMaxKM *ECMaxKM) (bool, error) {
	slog.Info("check cluster ec with offline chunks", "target_chunk_ip", targetChunkIP)

	/*
		This var name is a bit misleading in the context of this function.
		In multi chunk version(like ZBS 570), it represents the count of alive nodes that have at least one healthy chunk.
		In the single chunk version(like OS 620), it represents the count of alive chunks.
		To be compatible with different versions, we keep the name as `aliveChunkCount`.
	*/
	aliveChunkCount := 0

	res, err := c.client.Meta.Chunk.ListNode(ctx)
	if err == nil {
		for _, node := range res.Nodes {
			if node == nil || node.NodeDataIp == nil {
				continue
			}

			ipStr := IntToIP(*node.NodeDataIp)
			if ipStr == targetChunkIP {
				continue
			}

			for _, chunk := range node.Chunks {
				if *chunk.UseState == zbspb.ChunkState_CHUNK_STATE_IN_USE && *chunk.Status == zbspb.ChunkStatus_CHUNK_STATUS_CONNECTED_HEALTHY {
					aliveChunkCount++
					break
				}
			}
		}
	} else if zbserror.IsUnknownMethodId(err) {
		res, err := c.client.Meta.Chunk.ListChunk(ctx)
		if err != nil {
			return false, err
		}

		for _, chunk := range res.Chunks {
			if chunk == nil || chunk.RpcIp == nil || chunk.UseState == nil {
				continue
			}

			ipStr := IntToIP(*chunk.RpcIp)
			if ipStr == targetChunkIP {
				continue
			}

			if *chunk.UseState == zbspb.ChunkState_CHUNK_STATE_IN_USE && *chunk.Status == zbspb.ChunkStatus_CHUNK_STATUS_CONNECTED_HEALTHY {
				aliveChunkCount++
			}
		}
	} else {
		return false, err
	}

	if aliveChunkCount >= int(ecMaxKM.MaxKM) {
		slog.Info("aliveChunkCount >= ecMaxKM", "aliveChunkCount", aliveChunkCount, "ecMaxKM", ecMaxKM)
		return true, nil
	}

	slog.Error("check cluster ec with offline chunks failed", "aliveChunkCount", aliveChunkCount, "ecMaxKM", ecMaxKM)

	return false, nil
}

func IntToIP(ip uint32) string {
	bytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(bytes, ip)
	ipStr := net.IP(bytes).String()
	slog.Info("transfer unsigned int to ip", "from", ip, "to", ipStr)

	return ipStr
}
