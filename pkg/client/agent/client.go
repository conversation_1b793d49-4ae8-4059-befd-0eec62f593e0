package agentclient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"google.golang.org/protobuf/encoding/protojson"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
)

type TaskManagerClient interface {
	CreateTask(ctx context.Context, input *agentpb.TaskInput) (*agentpb.CreateTaskReply, error)
	GetTask(ctx context.Context, input *agentpb.GetTaskRequest) (*agentpb.GetTaskReply, error)
	ListTasks(ctx context.Context) (*agentpb.ListTasksReply, error)
}

type httpClient struct {
	address string
	client  *http.Client
}

func NewTaskManagerClient(address string) TaskManagerClient {
	return &httpClient{
		address: address,
		client:  &http.Client{Timeout: 60 * time.Second},
	}
}

func (c *httpClient) CreateTask(ctx context.Context, input *agentpb.TaskInput) (*agentpb.CreateTaskReply, error) {
	url := "http://" + c.address + "/api/v1/tasks"

	payload, err := json.Marshal(input)
	if err != nil {
		slog.Error("json marshal failed.", "error", err)
		return nil, err
	}

	slog.Info("Request Body:", "payload", string(payload))

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(payload))
	if err != nil {
		slog.Error("failed to prepare request", "error", err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.client.Do(req)
	if err != nil {
		slog.Error("failed to send request", "error", err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("request response code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("failed to read response", "error", err)
		return nil, err
	}

	var reply agentpb.CreateTaskReply

	err = protojson.Unmarshal(body, &reply)
	if err != nil {
		slog.Error("fail to unmarshal reply", "error", err)
		return nil, err
	}

	slog.Info("Task request success.", "response", string(body))

	return &reply, nil
}

func (c *httpClient) GetTask(ctx context.Context, input *agentpb.GetTaskRequest) (*agentpb.GetTaskReply, error) {
	url := fmt.Sprintf("http://%s/api/v1/tasks/%s", c.address, input.TaskId)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		slog.Error("fail to prepare getting agent task request", "error", err)
		return nil, err
	}

	resp, err := c.client.Do(req)
	if err != nil {
		slog.Error("fail to send getting agent task request", "error", err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("getting agent task info response code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("fail to read agent task info", "error", err)
		return nil, err
	}

	var reply agentpb.GetTaskReply

	err = protojson.Unmarshal(body, &reply)
	if err != nil {
		return nil, err
	}

	if reply.Ec != agentpb.ErrorCode_EC_EOK {
		slog.Error("fail to get agent task info", "EC", reply.Ec)
		return nil, fmt.Errorf("fail to get agent task info, EC: %s", reply.Ec)
	}

	return &reply, nil
}

func (c *httpClient) ListTasks(ctx context.Context) (*agentpb.ListTasksReply, error) {
	url := "http://" + c.address + "/api/v1/tasks"

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("getting agent task info response code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("fail to read agent task info", "error", err)
		return nil, err
	}

	var reply agentpb.ListTasksReply

	err = protojson.Unmarshal(body, &reply)
	if err != nil {
		return nil, err
	}

	if reply.Ec != agentpb.ErrorCode_EC_EOK {
		slog.Error("fail to get agent task info", "EC", reply.Ec)
		return nil, fmt.Errorf("fail to get agent task info, EC: %s", reply.Ec)
	}

	return &reply, nil
}
