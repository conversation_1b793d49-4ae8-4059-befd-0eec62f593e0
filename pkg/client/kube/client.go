package kubeclient

import (
	"flag"
	"log/slog"
	"os"
	"path/filepath"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

var kubeconfig *string

func newKubeConfig() (*rest.Config, error) {
	// use in-cluster config if the server running in kubernetes cluster
	if _, err := os.Stat("/var/run/secrets/kubernetes.io/serviceaccount/token"); err == nil {
		return rest.InClusterConfig()
	}

	// use out-of-cluster config if the server running outside kubernetes cluster
	if kubeconfig == nil {
		if home := homedir.HomeDir(); home != "" {
			kubeconfig = flag.String("kubeconfig", filepath.Join(home, ".kube", "config"), "(optional) absolute path to the kubeconfig file")
		} else {
			kubeconfig = flag.String("kubeconfig", "", "absolute path to the kubeconfig file")
		}
	}

	// use the current context in kubeconfig
	return clientcmd.BuildConfigFromFlags("", *kubeconfig)
}

func NewClientset() (*kubernetes.Clientset, error) {
	config, err := newKubeConfig()
	if err != nil {
		slog.Info("error creating kube config:", "error", err)
		return nil, err
	}

	return kubernetes.NewForConfig(config)
}
