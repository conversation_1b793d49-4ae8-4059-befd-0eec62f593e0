package postgres

import (
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
	error_util "github.smartx.com/LCM/lcm-manager/pkg/utils/errors"
	"github.smartx.com/LCM/lcm-manager/pkg/utils/pgutil"
)

var JobTableName = "job"

type RepoParams struct {
	DB *gorm.DB
}

type JobRepository struct {
	db *gorm.DB
}

func NewJobRepository(ctx context.Context, params *RepoParams) (*JobRepository, error) {
	r := &JobRepository{
		db: params.DB,
	}

	if err := r.migrate(ctx); err != nil {
		return nil, err
	}

	return r, nil
}

func (r *JobRepository) migrate(ctx context.Context) error {
	conn := r.db.WithContext(ctx)
	err := conn.AutoMigrate(&JobModel{})
	if err != nil {
		return err
	}
	return nil
}

func (r *JobRepository) Get(ctx context.Context, id string) (*server_pb.Job, error) {
	conn := r.db.WithContext(ctx)
	var row JobModel
	if err := conn.First(&row, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, error_util.ErrNotFound
		}
		return nil, err
	}

	return row.toProto(), nil
}

func (r *JobRepository) Save(ctx context.Context, job *server_pb.Job) error {
	conn := r.db.WithContext(ctx)
	return conn.Save(newJobRow(job)).Error
}

func (r *JobRepository) BatchSave(ctx context.Context, jobs []*server_pb.Job) error {
	conn := r.db.WithContext(ctx)
	if conn.Error != nil {
		return conn.Error
	}

	rows := make([]*JobModel, 0, len(jobs))
	for _, job := range jobs {
		rows = append(rows, newJobRow(job))
	}
	if err := conn.Save(&rows).Error; err != nil {
		return err
	}
	return nil
}

func (r *JobRepository) List(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.Job, error) {
	conn := r.db.WithContext(ctx)
	selector, err := pgutil.BuildSelector(conn, filters)
	if err != nil {
		return nil, err
	}

	for _, o := range sorts {
		if o.Direction == apiutil.FieldSortDirectionDesc {
			selector = selector.Order(o.FieldName + " DESC")
		} else {
			selector = selector.Order(o.FieldName)
		}
	}

	if limit > 0 {
		selector = selector.Offset(int(offset)).Limit(int(limit))
	} else {
		selector = selector.Offset(int(offset))
	}

	var rows []*JobModel
	if err := selector.Find(&rows).Error; err != nil {
		return nil, err
	}

	protos := make([]*server_pb.Job, 0)
	for _, row := range rows {
		protos = append(protos, row.toProto())
	}
	return protos, nil
}

func (r *JobRepository) ListWithTotal(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.Job, int64, error) {
	var total int64
	protos, err := r.List(ctx, filters, sorts, offset, limit)
	if err != nil {
		return protos, total, err
	}

	total, err = r.Count(ctx, filters)
	if err != nil {
		return nil, total, err
	}

	return protos, total, nil
}

func (r *JobRepository) Count(ctx context.Context, filters []*apiutil.FieldFilter) (int64, error) {
	var total int64
	conn := r.db.WithContext(ctx)
	selector, err := pgutil.BuildSelector(conn, filters)
	if err != nil {
		return total, err
	}
	j := &JobModel{}
	if err := selector.Model(j).Count(&total).Error; err != nil {
		return total, err
	}

	return total, nil
}

func (r *JobRepository) Delete(ctx context.Context, id string) error {
	conn := r.db.WithContext(ctx)
	if err := conn.First(&JobModel{}, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return error_util.ErrNotFound
		}
		return err
	}

	if err := conn.Delete(&JobModel{}, "id = ?", id).Error; err != nil {
		return err
	}

	return nil
}

func (r *JobRepository) UpdateJobState(ctx context.Context, id string, state server_pb.JobState, messages []*server_pb.MessageItem) error {
	slog.Info("UpdateJobState", "job_id", id, "state", state.String(), "msg", messages)

	job, err := r.Get(ctx, id)
	if err != nil {
		slog.Error("fail to get job with id: ", "job_id", id)
		return err
	}

	job.State = state
	if messages != nil {
		job.Messages = messages
	}

	err = r.Save(ctx, job)
	if err != nil {
		slog.Error("fail to update job state with id: ", "job_id", id)
		return err
	}

	return nil
}

func (r *JobRepository) UpdateJobStateWithEC(ctx context.Context, id string, state server_pb.JobState, messages []*server_pb.MessageItem, ec server_pb.ErrorCode) error {
	slog.Info("UpdateJobStateWithEC", "job_id", id, "state", state.String(), "msg", messages, "ec", ec.String())

	job, err := r.Get(ctx, id)
	if err != nil {
		slog.Error("fail to get job", "job_id", id)
		return err
	}

	job.State = state
	job.Ec = ec

	if messages != nil {
		job.Messages = messages
	}

	err = r.Save(ctx, job)
	if err != nil {
		slog.Error("fail to update job state.", "job_id", id)
		return err
	}

	return nil
}

func (r *JobRepository) UpdateJobDetail(ctx context.Context, id string, jobDetails map[string]string) error {
	slog.Info("UpdateJobDetail started.")

	job, err := r.Get(ctx, id)
	if err != nil {
		slog.Error("fail to get job", "job_id", id)
		return err
	}

	slog.Info("update job details in db", "job_id", id, "old_details", job.Details, "new_details", jobDetails)

	if job.Details == nil {
		job.Details = jobDetails
	} else {
		for k, v := range jobDetails {
			job.Details[k] = v
		}
	}

	err = r.Save(ctx, job)
	if err != nil {
		slog.Error("fail to update job details.", "job_id", id)
		return err
	}

	return nil
}

func (r *JobRepository) UpdateJobProgress(ctx context.Context, id string, newProgress *server_pb.JobProgress) error {
	slog.Info("update job progress", "job_id", id, "job_progress", newProgress)

	job, err := r.Get(ctx, id)
	if err != nil {
		slog.Error("fail to get job", "job_id", id)
		return err
	}

	if job.Progress == nil {
		job.Progress = newProgress
		return r.Save(ctx, job)
	}

	if newProgress.Progress != "" {
		job.Progress.Progress = newProgress.Progress
	}

	if newProgress.TotalTime != "" {
		job.Progress.TotalTime = newProgress.TotalTime
	}

	if newProgress.Details == nil {
		return r.Save(ctx, job)
	}

	// update progress details
	if job.Progress.Details == nil {
		job.Progress.Details = newProgress.Details
		return r.Save(ctx, job)
	}

	if newProgress.Details.Name != "" {
		job.Progress.Details.Name = newProgress.Details.Name
	}

	if job.Progress.Details.Items == nil {
		job.Progress.Details.Items = newProgress.Details.Items
		return r.Save(ctx, job)
	}

	for k, v := range newProgress.Details.Items {
		job.Progress.Details.Items[k] = v
	}

	return r.Save(ctx, job)
}

type JobModel struct {
	ID          string `gorm:"primary_key"`
	ClusterUUID string
	HostUUID    string
	Name        string
	State       string
	EC          string
	Messages    datatypes.JSON
	Progress    datatypes.JSON
	Details     datatypes.JSON
	CreateTime  time.Time
	UpdateTime  time.Time `gorm:"autoUpdateTime:true"`
}

func (m *JobModel) TableName() string {
	return JobTableName
}

func newJobRow(proto *server_pb.Job) *JobModel {
	row := &JobModel{
		ID:          proto.Id,
		ClusterUUID: proto.ClusterUuid,
		HostUUID:    proto.HostUuid,
		Name:        proto.Name,
		State:       proto.State.String(),
		EC:          proto.Ec.String(),
	}

	if messages, err := json.Marshal(proto.Messages); err == nil {
		row.Messages = messages
	}

	if progress, err := json.Marshal(proto.Progress); err == nil {
		row.Progress = progress
	}

	if details, err := json.Marshal(proto.Details); err == nil {
		row.Details = details
	}

	if proto.CreateTime != nil {
		row.CreateTime = proto.CreateTime.AsTime()
	}

	if proto.UpdateTime != nil {
		row.UpdateTime = proto.UpdateTime.AsTime()
	}

	return row
}

func (m *JobModel) toProto() *server_pb.Job {
	proto := &server_pb.Job{
		Id:          m.ID,
		ClusterUuid: m.ClusterUUID,
		HostUuid:    m.HostUUID,
		Name:        m.Name,
		State:       server_pb.JobState(server_pb.JobState_value[m.State]),
		Ec:          server_pb.ErrorCode(server_pb.ErrorCode_value[m.EC]),
	}

	_ = json.Unmarshal(m.Messages, &proto.Messages)
	_ = json.Unmarshal(m.Progress, &proto.Progress)
	_ = json.Unmarshal(m.Details, &proto.Details)
	proto.CreateTime = timestamppb.New(m.CreateTime)
	proto.UpdateTime = timestamppb.New(m.UpdateTime)

	return proto
}
