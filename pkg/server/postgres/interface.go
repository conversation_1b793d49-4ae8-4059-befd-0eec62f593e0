package postgres

import (
	"context"

	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	apiutil "github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
)

type IJobRepository interface {
	Get(ctx context.Context, id string) (*server_pb.Job, error)
	List(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.Job, error)
	ListWithTotal(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.Job, int64, error)
	Save(ctx context.Context, job *server_pb.Job) error
	BatchSave(ctx context.Context, jobs []*server_pb.Job) error
	Delete(ctx context.Context, id string) error
	UpdateJobState(ctx context.Context, id string, state server_pb.JobState, messages []*server_pb.MessageItem) error
	UpdateJobStateWithEC(ctx context.Context, id string, state server_pb.JobState, messages []*server_pb.MessageItem, ec server_pb.ErrorCode) error
	UpdateJobDetail(ctx context.Context, id string, jobDetails map[string]string) error
	UpdateJobProgress(ctx context.Context, id string, jobProgress *server_pb.JobProgress) error
}

type ICheckResultRepository interface {
	Get(ctx context.Context, id string) (*server_pb.CheckResult, error)
	GetByJobID(ctx context.Context, jobID string) ([]*server_pb.CheckResult, error)
	List(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.CheckResult, error)
	ListWithTotal(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.CheckResult, int64, error)
	Save(ctx context.Context, checkResult *server_pb.CheckResult) error
	BatchSave(ctx context.Context, checkResults []*server_pb.CheckResult) error
	Delete(ctx context.Context, id string) error
}
