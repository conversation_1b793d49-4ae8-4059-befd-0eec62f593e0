package postgres

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	apiutil "github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
	error_util "github.smartx.com/LCM/lcm-manager/pkg/utils/errors"
	"github.smartx.com/LCM/lcm-manager/pkg/utils/pgutil"
)

var CheckResultTableName = "check_result"

type CheckResultRepository struct {
	db *gorm.DB
}

func NewCheckResultRepository(ctx context.Context, params *RepoParams) (*CheckResultRepository, error) {
	r := &CheckResultRepository{
		db: params.DB,
	}

	if err := r.migrate(ctx); err != nil {
		return nil, err
	}

	return r, nil
}

func (r *CheckResultRepository) migrate(ctx context.Context) error {
	conn := r.db.WithContext(ctx)

	err := conn.AutoMigrate(&CheckResultModel{})
	if err != nil {
		return err
	}

	return nil
}

func (r *CheckResultRepository) Get(ctx context.Context, id string) (*server_pb.CheckResult, error) {
	conn := r.db.WithContext(ctx)

	var row CheckResultModel
	if err := conn.First(&row, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, error_util.ErrNotFound
		}

		return nil, err
	}

	return row.toProto(), nil
}

func (r *CheckResultRepository) GetByJobID(ctx context.Context, jobID string) ([]*server_pb.CheckResult, error) {
	conn := r.db.WithContext(ctx)

	var rows []*CheckResultModel
	if err := conn.Where("job_id = ?", jobID).Find(&rows).Error; err != nil {
		return nil, err
	}

	protos := make([]*server_pb.CheckResult, 0)
	for _, row := range rows {
		protos = append(protos, row.toProto())
	}

	return protos, nil
}

func (r *CheckResultRepository) Save(ctx context.Context, checkResult *server_pb.CheckResult) error {
	conn := r.db.WithContext(ctx)
	return conn.Save(newCheckResultRow(checkResult)).Error
}

func (r *CheckResultRepository) BatchSave(ctx context.Context, checkResults []*server_pb.CheckResult) error {
	conn := r.db.WithContext(ctx)
	if conn.Error != nil {
		return conn.Error
	}

	rows := make([]*CheckResultModel, 0, len(checkResults))
	for _, checkResult := range checkResults {
		rows = append(rows, newCheckResultRow(checkResult))
	}

	if err := conn.Save(&rows).Error; err != nil {
		return err
	}

	return nil
}

func (r *CheckResultRepository) List(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.CheckResult, error) {
	conn := r.db.WithContext(ctx)

	selector, err := pgutil.BuildSelector(conn, filters)
	if err != nil {
		return nil, err
	}

	for _, o := range sorts {
		if o.Direction == apiutil.FieldSortDirectionDesc {
			selector = selector.Order(o.FieldName + " DESC")
		} else {
			selector = selector.Order(o.FieldName)
		}
	}

	if limit > 0 {
		selector = selector.Offset(int(offset)).Limit(int(limit))
	} else {
		selector = selector.Offset(int(offset))
	}

	var rows []*CheckResultModel
	if err := selector.Find(&rows).Error; err != nil {
		return nil, err
	}

	protos := make([]*server_pb.CheckResult, 0)
	for _, row := range rows {
		protos = append(protos, row.toProto())
	}

	return protos, nil
}

func (r *CheckResultRepository) ListWithTotal(ctx context.Context, filters []*apiutil.FieldFilter, sorts []*apiutil.FieldSort, offset, limit int32) ([]*server_pb.CheckResult, int64, error) {
	var total int64

	protos, err := r.List(ctx, filters, sorts, offset, limit)
	if err != nil {
		return protos, total, err
	}

	total, err = r.Count(ctx, filters)
	if err != nil {
		return nil, total, err
	}

	return protos, total, nil
}

func (r *CheckResultRepository) Count(ctx context.Context, filters []*apiutil.FieldFilter) (int64, error) {
	var total int64

	conn := r.db.WithContext(ctx)

	selector, err := pgutil.BuildSelector(conn, filters)
	if err != nil {
		return total, err
	}

	j := &CheckResultModel{}
	if err := selector.Model(j).Count(&total).Error; err != nil {
		return total, err
	}

	return total, nil
}

func (r *CheckResultRepository) Delete(ctx context.Context, id string) error {
	conn := r.db.WithContext(ctx)
	if err := conn.First(&CheckResultModel{}, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return error_util.ErrNotFound
		}

		return err
	}

	if err := conn.Delete(&CheckResultModel{}, "id = ?", id).Error; err != nil {
		return err
	}

	return nil
}

type CheckResultModel struct {
	ID          string `gorm:"primary_key"`
	JobID       string
	CheckName   string
	Description datatypes.JSON
	State       string
	Messages    datatypes.JSON
	CreateTime  time.Time
	UpdateTime  time.Time `gorm:"autoUpdateTime:true"`
}

func (m *CheckResultModel) TableName() string {
	return CheckResultTableName
}

func newCheckResultRow(proto *server_pb.CheckResult) *CheckResultModel {
	row := &CheckResultModel{
		ID:        proto.Id,
		JobID:     proto.JobId,
		CheckName: proto.CheckName,
		State:     proto.State.String(),
	}

	if descriptions, err := json.Marshal(proto.Description); err == nil {
		row.Description = descriptions
	}

	if messages, err := json.Marshal(proto.Messages); err == nil {
		row.Messages = messages
	}

	if proto.CreateTime != nil {
		row.CreateTime = proto.CreateTime.AsTime()
	}

	if proto.UpdateTime != nil {
		row.UpdateTime = proto.UpdateTime.AsTime()
	}

	return row
}

func (m *CheckResultModel) toProto() *server_pb.CheckResult {
	proto := &server_pb.CheckResult{
		Id:        m.ID,
		JobId:     m.JobID,
		CheckName: m.CheckName,
		State:     server_pb.CheckState(server_pb.CheckState_value[m.State]),
	}

	_ = json.Unmarshal(m.Description, &proto.Description)
	_ = json.Unmarshal(m.Messages, &proto.Messages)
	proto.CreateTime = timestamppb.New(m.CreateTime)
	proto.UpdateTime = timestamppb.New(m.UpdateTime)

	return proto
}
