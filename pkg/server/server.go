package server

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"sort"
	"strconv"
	"strings"
	"sync"

	"github.com/grpc-ecosystem/go-grpc-middleware/util/metautils"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"
	"gopkg.in/yaml.v2"

	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	agentclient "github.smartx.com/LCM/lcm-manager/pkg/client/agent"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	"github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
	"github.smartx.com/LCM/lcm-manager/pkg/workflow"
	rdma_toggle "github.smartx.com/LCM/lcm-manager/pkg/workflow/rdma_toggle"
	removehost "github.smartx.com/LCM/lcm-manager/pkg/workflow/remove_host"
	roleconvert "github.smartx.com/LCM/lcm-manager/pkg/workflow/role_convert"
	timesync "github.smartx.com/LCM/lcm-manager/pkg/workflow/time_sync"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
)

type Server struct {
	serverpb.UnimplementedManagerServer
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	wfClient              *workflow.Client
	towerClient           tower.Client
	mu                    sync.Mutex
}

func NewServer(jr postgres.IJobRepository, crr postgres.ICheckResultRepository, wfClient *workflow.Client, towerClient tower.Client) *Server {
	return &Server{
		jobRepository:         jr,
		checkResultRepository: crr,
		wfClient:              wfClient,
		towerClient:           towerClient,
	}
}

func (s *Server) ValidateTargetClusterAndHost(ctx context.Context, clusterUUID string, hostUUID string) (*tower.QueryClusterInfoByLocalIdCluster, *tower.QueryHostInfoByLocalIdHost, error) {
	cluster, err := s.towerClient.GetClusterInfoByLocalID(ctx, clusterUUID)
	if err != nil {
		slog.Error("fail to get cluster info", "error", err)
		return nil, nil, status.Error(codes.Internal, err.Error())
	}

	if cluster == nil || cluster.Id == "" {
		err := fmt.Errorf("cluster %s not found", clusterUUID)
		slog.Error(err.Error())
		return nil, nil, status.Error(codes.NotFound, err.Error())
	}

	targetHost, err := s.towerClient.GetHostInfoByLocalID(ctx, hostUUID)
	if err != nil {
		slog.Error("fail to get target host info", "error", err)
		return cluster, nil, status.Error(codes.Internal, err.Error())
	}

	if targetHost == nil || targetHost.Id == "" {
		err := fmt.Errorf("host %s not found", hostUUID)
		slog.Error(err.Error())
		return cluster, nil, status.Error(codes.NotFound, err.Error())
	}

	return cluster, targetHost, nil
}

func (s *Server) ValidateRoleConvertParams(clusterIP string, targetRole string, targetHost *tower.QueryHostInfoByLocalIdHost) error {
	hostRole, err := workflowutils.GetHostRole(clusterIP, targetHost.Local_id, targetHost.Role)
	if err != nil {
		slog.Error("fail to get host role from host label", "host", targetHost.Name, "error", err)
		return status.Error(codes.Internal, "fail to get host role: "+err.Error())
	}

	if targetRole == serverpb.HostRole_ROLE_STORAGE.String() && hostRole == string(config.HostRoleStorage) {
		slog.Error("invalid argument, target host already in storage/compute role")
		return status.Error(codes.InvalidArgument, "invalid argument, target host already in storage/compute role")
	} else if targetRole == serverpb.HostRole_ROLE_MASTER.String() && hostRole == string(config.HostRoleMaster) {
		slog.Error("invalid argument, target host already in master role")
		return status.Error(codes.InvalidArgument, "invalid argument, target host already in master role")
	}

	return nil
}

func (s *Server) CheckAndCreateActionJob(ctx context.Context, clusterUUID string, hostUUID string, jobName string) (string, bool, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	runningJobs, err := s.getHostRunningLcmManagerJobs(ctx, clusterUUID, hostUUID, jobName)
	if err != nil {
		return "", false, status.Error(codes.Internal, err.Error())
	}

	slog.Info("get host running jobs", "running_jobs", runningJobs)
	if len(runningJobs) == 1 {
		slog.Info("job already started, return the running job", "job_name", jobName, "job_id", runningJobs[0].Id)
		return runningJobs[0].Id, false, nil
	} else if len(runningJobs) > 1 {
		// This is not a normal situation, need fix the bug and reset the lcm manager job manually.
		err := fmt.Errorf("already %d running jobs %s exist", len(runningJobs), jobName)
		slog.Error(err.Error())
		return "", false, status.Error(codes.AlreadyExists, err.Error())
	}

	jobID, err := workflowutils.CreateJob(ctx, s.jobRepository, clusterUUID, hostUUID, jobName)
	if err != nil {
		slog.Error("fail to create job", "job_name", jobName, "cluster_uuid", clusterUUID, "host_uuid", hostUUID, "error", err)
		return "", false, status.Error(codes.Internal, "fail to create job: "+err.Error())
	}

	return jobID, true, nil
}

func (s *Server) GetCheckResult(ctx context.Context, req *serverpb.CheckResultRequest) (*serverpb.CheckResultResponse, error) {
	jobID := req.JobId

	job, err := s.jobRepository.Get(ctx, jobID)
	if err != nil || job == nil {
		return nil, status.Error(codes.NotFound, errors.New("job not found").Error())
	}

	checkResults, err := s.checkResultRepository.GetByJobID(ctx, jobID)
	if err != nil {
		return nil, status.Error(codes.Internal, errors.New("fail to get check result by job id").Error())
	}

	sort.Slice(checkResults, func(i, j int) bool {
		return checkResults[i].Id < checkResults[j].Id
	})

	response := &serverpb.CheckResultResponse{
		ClientId:     req.ClientId,
		JobId:        jobID,
		Job:          job,
		CheckResults: checkResults,
	}

	return response, nil
}

func (s *Server) ConvertRoleCheck(ctx context.Context, req *serverpb.ConvertRoleCheckRequest) (*serverpb.ConvertRoleCheckResponse, error) {
	slog.Info("ConvertRoleCheck Request", "req", req)

	cluster, targetHost, err := s.ValidateTargetClusterAndHost(ctx, req.ClusterUuid, req.HostUuid)
	if err != nil {
		return nil, err
	}

	err = s.ValidateRoleConvertParams(cluster.Ip, req.TargetRole.String(), targetHost)
	if err != nil {
		return nil, err
	}

	var jobName string
	if req.TargetRole.String() == serverpb.HostRole_ROLE_STORAGE.String() {
		jobName = config.ActionConvertToStorageCheck
	}

	if req.TargetRole.String() == serverpb.HostRole_ROLE_MASTER.String() {
		jobName = config.ActionConvertToMasterCheck
	}

	jobID, err := workflowutils.CreateJob(ctx, s.jobRepository, req.ClusterUuid, req.HostUuid, jobName)
	if err != nil {
		slog.Error("fail to create convert role check job", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	var isVmware bool
	if cluster.Hypervisor == tower.HypervisorVmware {
		isVmware = true
	}

	input := roleconvert.RoleConvertCheckWorkflowInput{
		JobID:          jobID,
		TowerClusterID: cluster.Id,
		ClusterUUID:    req.ClusterUuid,
		ClusterIP:      cluster.Ip,
		HostName:       workflowutils.PickHostName(targetHost.Name, targetHost.Scvm_name, isVmware),
		HostUUID:       req.HostUuid,
		TargetRole:     req.TargetRole.String(),
		IsVmware:       isVmware,
	}

	input.Header.UserID = metautils.ExtractIncoming(ctx).Get(config.UserIDHeader)
	input.Header.UserIP = metautils.ExtractIncoming(ctx).Get(config.UserIPHeader)

	slog.Info("execute role convert check workflow with input.", "input", input)

	if err := s.wfClient.ExecuteRoleConvertCheckWorkflow(ctx, input); err != nil {
		slog.Error("fail to trigger ExecuteRoleConvertCheckWorkflow", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.ConvertRoleCheckResponse{
		ClientId: req.ClientId,
		JobId:    jobID,
	}, nil
}

func (s *Server) ConvertRoleCheckResult(ctx context.Context, req *serverpb.CheckResultRequest) (*serverpb.CheckResultResponse, error) {
	slog.Info("ConvertRoleCheckResult Request", "req", req)

	return s.GetCheckResult(ctx, req)
}

func (s *Server) ConvertRole(ctx context.Context, req *serverpb.ConvertRoleRequest) (*serverpb.ConvertRoleResponse, error) {
	slog.Info("ConvertRole Request", "req", req)

	cluster, targetHost, err := s.ValidateTargetClusterAndHost(ctx, req.ClusterUuid, req.HostUuid)
	if err != nil {
		return nil, err
	}

	err = s.ValidateRoleConvertParams(cluster.Ip, req.TargetRole.String(), targetHost)
	if err != nil {
		return nil, err
	}

	var jobName string
	if req.TargetRole.String() == serverpb.HostRole_ROLE_STORAGE.String() {
		jobName = config.ActionConvertToStorage
	}

	if req.TargetRole.String() == serverpb.HostRole_ROLE_MASTER.String() {
		jobName = config.ActionConvertToMaster
	}

	jobID, newCreated, err := s.CheckAndCreateActionJob(ctx, req.ClusterUuid, req.HostUuid, jobName)
	if err != nil {
		return nil, err
	}

	if !newCreated {
		return &serverpb.ConvertRoleResponse{ClientId: req.ClientId, JobId: jobID}, nil
	}

	var isVmware bool
	if cluster.Hypervisor == tower.HypervisorVmware {
		isVmware = true
	}

	input := roleconvert.RoleConvertWorkflowInput{
		JobID:          jobID,
		TowerClusterID: cluster.Id,
		ClusterUUID:    req.ClusterUuid,
		ClusterIP:      cluster.Ip,
		HostName:       workflowutils.PickHostName(targetHost.Name, targetHost.Scvm_name, isVmware),
		HostUUID:       req.HostUuid,
		TargetRole:     req.TargetRole.String(),
		SkipPrecheck:   req.SkipPrecheck,
		IsVmware:       isVmware,
	}

	input.Header.UserID = metautils.ExtractIncoming(ctx).Get(config.UserIDHeader)
	input.Header.UserIP = metautils.ExtractIncoming(ctx).Get(config.UserIPHeader)

	slog.Info("execute role convert workflow with input.", "input", input)

	if err := s.wfClient.ExecuteRoleConvertWorkflow(ctx, input); err != nil {
		slog.Error("fail to trigger ExecuteRoleConvertWorkflow", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.ConvertRoleResponse{
		ClientId: req.ClientId,
		JobId:    jobID,
	}, nil
}

func (s *Server) RemoveHostCheck(ctx context.Context, req *serverpb.RemoveHostCheckRequest) (*serverpb.RemoveHostCheckResponse, error) {
	slog.Info("RemoveHostCheck Request", "req", req)

	cluster, targetHost, err := s.ValidateTargetClusterAndHost(ctx, req.ClusterUuid, req.HostUuid)
	if err != nil {
		return nil, err
	}

	jobID, err := workflowutils.CreateJob(ctx, s.jobRepository, req.ClusterUuid, req.HostUuid, config.ActionRemoveHostCheck)
	if err != nil {
		slog.Error("fail to create remove host check job", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	var isVmware bool
	if cluster.Hypervisor == tower.HypervisorVmware {
		isVmware = true
	}

	input := removehost.RemoveHostCheckWorkflowInput{
		JobID:          jobID,
		TowerClusterID: cluster.Id,
		ClusterUUID:    req.ClusterUuid,
		ClusterIP:      cluster.Ip,
		HostName:       workflowutils.PickHostName(targetHost.Name, targetHost.Scvm_name, isVmware),
		HostUUID:       req.HostUuid,
		IsVmware:       isVmware,
	}

	input.Header.UserID = metautils.ExtractIncoming(ctx).Get(config.UserIDHeader)
	input.Header.UserIP = metautils.ExtractIncoming(ctx).Get(config.UserIPHeader)

	slog.Info("execute remove host check workflow with input.", "input", input)

	err = s.wfClient.ExecuteRemoveHostCheckWorkflow(ctx, input)
	if err != nil {
		slog.Error("fail to trigger ExecuteRemoveHostCheckWorkflow", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.RemoveHostCheckResponse{
		ClientId: req.ClientId,
		JobId:    jobID,
	}, nil
}

func (s *Server) RemoveHostCheckResult(ctx context.Context, req *serverpb.CheckResultRequest) (*serverpb.CheckResultResponse, error) {
	slog.Info("RemoveHostCheckResult Request", "req", req)

	return s.GetCheckResult(ctx, req)
}

func (s *Server) RemoveHost(ctx context.Context, req *serverpb.RemoveHostRequest) (*serverpb.RemoveHostResponse, error) {
	slog.Info("RemoveHost Request", "req", req)

	cluster, targetHost, err := s.ValidateTargetClusterAndHost(ctx, req.ClusterUuid, req.HostUuid)
	if err != nil {
		return nil, err
	}

	jobID, newCreated, err := s.CheckAndCreateActionJob(ctx, req.ClusterUuid, req.HostUuid, config.ActionRemoveHost)
	if err != nil {
		return nil, err
	}

	if !newCreated {
		return &serverpb.RemoveHostResponse{ClientId: req.ClientId, JobId: jobID}, nil
	}

	var isVmware bool
	if cluster.Hypervisor == tower.HypervisorVmware {
		isVmware = true
	}

	input := removehost.RemoveHostWorkflowInput{
		JobID:          jobID,
		TowerClusterID: cluster.Id,
		ClusterUUID:    req.ClusterUuid,
		ClusterIP:      cluster.Ip,
		HostName:       workflowutils.PickHostName(targetHost.Name, targetHost.Scvm_name, isVmware),
		HostUUID:       req.HostUuid,
		SkipPrecheck:   req.SkipPrecheck,
		IsVmware:       isVmware,
	}

	input.Header.UserID = metautils.ExtractIncoming(ctx).Get(config.UserIDHeader)
	input.Header.UserIP = metautils.ExtractIncoming(ctx).Get(config.UserIPHeader)

	slog.Info("execute remove host workflow with input.", "input", input)

	err = s.wfClient.ExecuteRemoveHostWorkflow(ctx, input)
	if err != nil {
		slog.Error("fail to trigger ExecuteRemoveHostWorkflow", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.RemoveHostResponse{
		ClientId: req.ClientId,
		JobId:    jobID,
	}, nil
}

func (s *Server) TimeSync(ctx context.Context, req *serverpb.TimeSyncRequest) (*serverpb.TimeSyncResponse, error) {
	slog.Info("TimeSync Request", "req", req)

	// pick a host to execute time sync
	cluster, err := s.towerClient.GetClusterInfoByLocalID(ctx, req.ClusterUuid)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return nil, err
	}

	sort.Slice(cluster.Hosts, func(i, j int) bool {
		return cluster.Hosts[i].Name < cluster.Hosts[j].Name
	})

	var hostUUID string
	for _, host := range cluster.Hosts {
		if host.Status == tower.HostStatusConnectedHealthy {
			slog.Info("Pick host to deploy lcm manager agent host plugin and execute time sync task.", "host_name", host.Name, "mgt_ip", host.Management_ip, "host_uuid", host.Id)
			hostUUID = host.Local_id
			break
		}
	}

	if hostUUID == "" {
		slog.Error("failed find to a host in state CONNECTED_HEALTHY")
		return nil, errors.New("failed find to a host in state CONNECTED_HEALTHY")
	}

	cluster, targetHost, err := s.ValidateTargetClusterAndHost(ctx, req.ClusterUuid, hostUUID)
	if err != nil {
		return nil, err
	}

	if err := validateTimeParam(req.Time, cluster.Ntp_servers); err != nil {
		return nil, err
	}

	jobID, newCreated, err := s.CheckAndCreateActionJob(ctx, req.ClusterUuid, targetHost.Local_id, config.ActionTimeSync)
	if err != nil {
		return nil, err
	}

	if !newCreated {
		return &serverpb.TimeSyncResponse{ClientId: req.ClientId, JobId: jobID}, nil
	}

	input := timesync.TimeSyncWorkflowInput{
		JobID:            jobID,
		TowerClusterID:   cluster.Id,
		ClusterUUID:      req.ClusterUuid,
		ClusterName:      cluster.Name,
		ClusterIP:        cluster.Ip,
		HostUUID:         targetHost.Local_id,
		HostName:         targetHost.Name,
		Time:             req.Time,
		TowerTaskCfgName: "TimeSyncWithNtpServer",
	}
	if req.Time != "" {
		input.TowerTaskCfgName = "TimeSyncInternal"
	}

	input.Header.UserID = metautils.ExtractIncoming(ctx).Get(config.UserIDHeader)
	input.Header.UserIP = metautils.ExtractIncoming(ctx).Get(config.UserIPHeader)

	slog.Info("execute time sync workflow with input.", "input", input)

	err = s.wfClient.ExecuteTimeSyncWorkflow(ctx, input)
	if err != nil {
		slog.Error("fail to trigger ExecuteTimeSyncWorkflow", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.TimeSyncResponse{
		ClientId: req.ClientId,
		JobId:    jobID,
	}, nil
}

func validateTimeParam(time string, ntpServers []string) error {
	if time == "" {
		if len(ntpServers) == 0 {
			return status.Error(codes.FailedPrecondition, "time param must be specified if no NTP server configured")
		}
	} else {
		if len(ntpServers) > 0 {
			return status.Error(codes.FailedPrecondition, "time param must not be specified if any NTP server configured")
		}

		cmd := exec.Command("date", "-d", time)
		_, err := cmd.Output()
		if err != nil {
			slog.Warn(fmt.Sprintf("time param '%s' is invalid: %v", time, err))
			return status.Error(codes.InvalidArgument, "time param is invalid")
		}
	}
	return nil
}
func (s *Server) SetRDMA(ctx context.Context, req *serverpb.SetRDMARequest) (*serverpb.SetRDMAResponse, error) {
	slog.Info("SetRDMA Request", "req", req)

	// pick a host to execute rdma toggle
	cluster, err := s.towerClient.GetClusterInfoByLocalID(ctx, req.ClusterUuid)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return nil, err
	}

	sort.Slice(cluster.Hosts, func(i, j int) bool {
		return cluster.Hosts[i].Name < cluster.Hosts[j].Name
	})

	var hostUUID string
	for _, host := range cluster.Hosts {
		if host.Status == tower.HostStatusConnectedHealthy {
			slog.Info("Pick host to deploy lcm manager agent host plugin and execute rdma toggle task.", "host_name", host.Name, "mgt_ip", host.Management_ip, "host_uuid", host.Id)
			hostUUID = host.Local_id
			break
		}
	}

	if hostUUID == "" {
		slog.Error("failed find to a host in state CONNECTED_HEALTHY")
		return nil, errors.New("failed find to a host in state CONNECTED_HEALTHY")
	}

	cluster, targetHost, err := s.ValidateTargetClusterAndHost(ctx, req.ClusterUuid, hostUUID)
	if err != nil {
		return nil, err
	}

	jobID, newCreated, err := s.CheckAndCreateActionJob(ctx, req.ClusterUuid, targetHost.Local_id, config.ActionRDMAToggle)
	if err != nil {
		return nil, err
	}

	if !newCreated {
		return &serverpb.SetRDMAResponse{ClientId: req.ClientId, JobId: jobID}, nil
	}

	// TODO: check status, if on skip
	var towerTaskCfgName string
	switch req.State {
	case serverpb.RDMAState_RDMA_ON:
		towerTaskCfgName = "SetRdmaOn"
	case serverpb.RDMAState_RDMA_OFF:
		towerTaskCfgName = "SetRdmaOff"
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid rdma state")
	}

	input := rdma_toggle.RdmaToggleWorkflowInput{
		JobID:            jobID,
		TowerClusterID:   cluster.Id,
		ClusterUUID:      req.ClusterUuid,
		ClusterName:      cluster.Name,
		ClusterIP:        cluster.Ip,
		HostUUID:         targetHost.Local_id,
		HostName:         targetHost.Name,
		TargetState:      req.State,
		TowerTaskCfgName: towerTaskCfgName,
	}

	input.Header.UserID = metautils.ExtractIncoming(ctx).Get(config.UserIDHeader)
	input.Header.UserIP = metautils.ExtractIncoming(ctx).Get(config.UserIPHeader)

	slog.Info("execute rdma toggle workflow with input.", "input", input)

	err = s.wfClient.ExecuteRdmaToggleWorkflow(ctx, input)
	if err != nil {
		slog.Error("fail to trigger ExecuteRDMAWorkflow", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.SetRDMAResponse{
		ClientId: req.ClientId,
		JobId:    jobID,
	}, nil
}

func (s *Server) getHostRunningLcmManagerJobs(ctx context.Context, clusterUUID string, hostUUID string, jobName string) ([]*serverpb.Job, error) {
	var err error

	filters := []*apiutil.FieldFilter{
		{
			FieldPath: "cluster_uuid",
			Operator:  apiutil.FieldFilterOperatorEq,
			Value:     clusterUUID,
		},
		{
			FieldPath: "host_uuid",
			Operator:  apiutil.FieldFilterOperatorEq,
			Value:     hostUUID,
		},
		{
			FieldPath: "state",
			Operator:  apiutil.FieldFilterOperatorIn,
			Value:     []string{serverpb.JobState_JOB_STATE_PENDING.String(), serverpb.JobState_JOB_STATE_RUNNING.String()},
		},
		{
			FieldPath: "name",
			Operator:  apiutil.FieldFilterOperatorEq,
			Value:     jobName,
		},
	}

	sorts := []*apiutil.FieldSort{
		{
			FieldName: "create_time",
			Direction: apiutil.FieldSortDirectionDesc,
		},
	}

	jobs, err := s.jobRepository.List(ctx, filters, sorts, 0, 100)
	if err != nil {
		return nil, err
	}

	return jobs, nil
}

func (s *Server) ListJobs(ctx context.Context, req *serverpb.ListJobsRequest) (*serverpb.ListJobsResponse, error) {
	var err error
	var filters []*apiutil.FieldFilter
	if req.Filter != "" {
		if filters, err = apiutil.DecodeFilter(req.Filter, jobFieldFilterDescriptors); err != nil {
			return nil, status.Error(codes.InvalidArgument, apiutil.NewInvalidFieldError("filter").Error())
		}
	}
	var sorts []*apiutil.FieldSort
	if req.OrderBy != "" {
		if sorts, err = apiutil.DecodeOrderBy(req.OrderBy, jobFieldSortDescriptors); err != nil {
			return nil, status.Error(codes.InvalidArgument, apiutil.NewInvalidFieldError("order_by").Error())
		}
	}

	offset, err := apiutil.DecodePageToken(req.PageToken)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, apiutil.NewInvalidFieldError("page_token").Error())
	}

	limit, err := apiutil.DecodePageSize(req.PageSize, 25, 500)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, apiutil.NewInvalidFieldError("page_size").Error())
	}

	jobs, total, err := s.jobRepository.ListWithTotal(ctx, filters, sorts, offset, limit)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &serverpb.ListJobsResponse{
		Jobs:          jobs,
		NextPageToken: strconv.Itoa(int(offset) + len(jobs)),
		Total:         strconv.FormatInt(total, 10),
	}, nil
}

func (s *Server) GetJob(ctx context.Context, req *serverpb.GetJobRequest) (*serverpb.Job, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, apiutil.NewInvalidFieldError("id").Error())
	}
	job, err := s.jobRepository.Get(ctx, req.Id)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return job, nil
}

func getLogsFromFile(jobID string, offset int, pageSize int) (*serverpb.JobLogs, error) {
	logFilename := jobID + ".log"
	logFilePath := config.LogDir + "/" + logFilename

	_, err := os.Stat(logFilePath)
	if err != nil {
		slog.Error("Log file not found", "log_file", logFilePath)
		return nil, status.Error(codes.NotFound, fmt.Sprintf("log file %s not found, err: %s", logFilename, err.Error()))
	}

	file, err := os.Open(logFilePath)
	if err != nil {
		slog.Error("fail to open log file.", "log_file", logFilePath, "error", err)
		return nil, status.Error(codes.Internal, fmt.Sprintf("fail to open log file %s, err: %s", logFilename, err.Error()))
	}
	defer file.Close()

	var logs []string
	scanner := bufio.NewScanner(file)
	totalLine := 0

	for i := 0; scanner.Scan(); i++ {
		totalLine++

		if i < offset {
			continue
		}

		logs = append(logs, scanner.Text())
		if len(logs) >= pageSize {
			break
		}
	}

	if err := scanner.Err(); err != nil {
		slog.Error("error reading log file", "log_file", logFilePath, "error", err)
		return nil, status.Error(codes.Internal, fmt.Sprintf("error reading log file %s, err: %s", logFilename, err.Error()))
	}

	if offset > totalLine {
		offset = totalLine
	}

	jobLogs := serverpb.JobLogs{
		Logs:       logs,
		Offset:     int32(offset),
		NextOffset: int32(offset + len(logs)),
	}

	return &jobLogs, nil
}

func getLogsFromAgent(ctx context.Context, agentAddr string, offset int, pageSize int) (*serverpb.JobLogs, error) {
	client := agentclient.NewTaskManagerClient(agentAddr)

	jobTasks, err := client.ListTasks(ctx)
	if err != nil {
		slog.Error("Fail to get lcm manager agent task log", "error", err)
		return nil, status.Error(codes.Internal, fmt.Sprintf("fail to get lcm manager agent %s task log, err: %s", agentAddr, err.Error()))
	}

	sort.Slice(jobTasks.Data, func(i, j int) bool {
		return jobTasks.Data[i].TaskId < jobTasks.Data[j].TaskId
	})

	var logs []string

	for _, task := range jobTasks.Data {
		cleanedLogs := strings.ReplaceAll(task.Output.Stdout, "\r\n", "\n")
		cleanedLogs = strings.ReplaceAll(cleanedLogs, "\r", "\n")

		taskLogs := strings.Split(cleanedLogs, "\n")
		logs = append(logs, taskLogs...)
	}

	if offset > len(logs) {
		offset = len(logs)
	}

	var pageLogs []string
	if offset+pageSize > len(logs) {
		pageLogs = logs[offset:]
	} else {
		pageLogs = logs[offset:(offset + pageSize)]
	}

	jobLogs := serverpb.JobLogs{
		Logs:       pageLogs,
		Offset:     int32(offset),
		NextOffset: int32(offset + len(pageLogs)),
	}

	return &jobLogs, nil
}

func (s *Server) GetJobLogs(ctx context.Context, req *serverpb.GetJobLogsRequest) (*serverpb.JobLogs, error) {
	slog.Info(fmt.Sprintf("Getting job %s logs", req.Id))

	offset := int(req.Offset)
	if offset < 0 {
		return nil, status.Error(codes.InvalidArgument, "offset can't less than 0")
	}

	pageSize := int(req.Length)
	if pageSize <= 0 {
		pageSize = 1000
	}

	job, err := s.jobRepository.Get(ctx, req.Id)
	if err != nil {
		slog.Error("fail to get job from db", "job_id", req.Id, "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	slog.Info("get job info", "state", job.State, "name", job.Name)

	if job.State == serverpb.JobState_JOB_STATE_SUCCESS || job.State == serverpb.JobState_JOB_STATE_FAILED {
		return getLogsFromFile(job.Id, offset, pageSize)
	}

	agentAddr, exist := job.Details["agent_addr"]
	if !exist {
		return nil, status.Error(codes.NotFound, "no agent ip fond in job, logs not ready.")
	}

	return getLogsFromAgent(ctx, agentAddr, offset, pageSize)
}

func (s *Server) GetSupportedActions(_ context.Context, _ *emptypb.Empty) (*serverpb.AvailabilityMap, error) {
	slog.Info("get supported actions")

	fileData, err := os.ReadFile(config.AvailabilityMapFile)
	if err != nil {
		slog.Error("fail to read yaml file.", "file", config.AvailabilityMapFile, "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	var midAvailabilityMap config.AvailabilityMap

	err = yaml.Unmarshal(fileData, &midAvailabilityMap)
	if err != nil {
		slog.Error("fail to unmarshal yaml", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	jsonData, err := json.Marshal(midAvailabilityMap)
	if err != nil {
		slog.Error("fail to marshal to json", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	var availabilityMap serverpb.AvailabilityMap

	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true,
		AllowPartial:   true,
	}

	err = unmarshaler.Unmarshal(jsonData, &availabilityMap)
	if err != nil {
		slog.Error("fail to unmarshal json to proto", "error", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &availabilityMap, nil
}

func (s *Server) Healthz(_ context.Context, _ *emptypb.Empty) (*serverpb.HealthzResponse, error) {
	return &serverpb.HealthzResponse{
		Result: "ok",
	}, nil
}

var (
	jobFieldFilterDescriptors = []*apiutil.FieldFilterDescriptor{
		{
			FieldPath: "cluster_uuid",
			Operators: []apiutil.FieldFilterOperator{
				apiutil.FieldFilterOperatorEq,
				apiutil.FieldFilterOperatorNe,
				apiutil.FieldFilterOperatorJSONContains,
			},
			Value: "",
		},
		{
			FieldPath: "host_uuid",
			Operators: []apiutil.FieldFilterOperator{
				apiutil.FieldFilterOperatorEq,
				apiutil.FieldFilterOperatorNe,
				apiutil.FieldFilterOperatorJSONContains,
			},
			Value: "",
		},
		{
			FieldPath: "state",
			Operators: []apiutil.FieldFilterOperator{
				apiutil.FieldFilterOperatorEq,
				apiutil.FieldFilterOperatorNe,
				apiutil.FieldFilterOperatorJSONContains,
			},
			Value: "",
		},
	}

	jobFieldSortDescriptors = []*apiutil.FieldSortDescriptor{
		{FieldName: "cluster_uuid"},
		{FieldName: "host_uuid"},
		{FieldName: "job_id"},
		{FieldName: "create_time"},
		{FieldName: "update_time"},
	}
)
