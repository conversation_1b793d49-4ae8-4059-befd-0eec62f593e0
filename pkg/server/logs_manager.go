package server

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	kubeclient "github.smartx.com/LCM/lcm-manager/pkg/client/kube"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
)

const (
	cloudtowerNamespace     = "cloudtower-system"
	lcmManagerLogsPvcName   = "lcm-manager-logs"
	lcmManagerLogsThreshold = 0.8
	logCleanupInterval      = 24 * time.Hour
)

type FileInfo struct {
	Path    string
	ModTime time.Time
	Size    int64
}

func ListLogFilesSortedByModTime(rootDir string) ([]FileInfo, error) {
	var files []FileInfo

	err := filepath.WalkDir(rootDir, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && strings.HasSuffix(d.Name(), ".log") {
			info, err := d.Info()
			if err != nil {
				return err
			}

			files = append(files, FileInfo{
				Path:    path,
				ModTime: info.ModTime(),
				Size:    info.Size(),
			})
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	sort.Slice(files, func(i, j int) bool {
		return files[i].ModTime.Before(files[j].ModTime)
	})

	return files, nil
}

func GetAllocatedStorageForLcmLogs() (int64, error) {
	kubeClient, err := kubeclient.NewClientset()
	if err != nil {
		slog.Error("fail to init kube client", "error", err)
		return 0, err
	}

	pvc, err := kubeClient.CoreV1().PersistentVolumeClaims(cloudtowerNamespace).Get(context.Background(), lcmManagerLogsPvcName, metav1.GetOptions{})
	if err != nil {
		slog.Error("fail to get PersistentVolumeClaims", "pvc", lcmManagerLogsPvcName, "error", err)
		return 0, err
	}

	allocatedStorage := pvc.Status.Capacity.Storage().Value()
	slog.Info("get allocated pvc lcm-manager-logs", "size", pvc.Status.Capacity.Storage())

	return allocatedStorage, nil
}

func GetDirectorySize(dir string) (int64, error) {
	var totalSize int64

	err := filepath.Walk(dir, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		totalSize += info.Size()
		return nil
	})

	slog.Info("get directory used space size", "dir", dir, "size", totalSize)
	return totalSize, err
}

func CleanUpLogs(dir string, capacityLimit int64) error {
	dirSize, err := GetDirectorySize(dir)
	if err != nil {
		return fmt.Errorf("failed to get directory size: %w", err)
	}

	if dirSize < capacityLimit {
		slog.Info("directory size is within threshold", "dirSize", dirSize, "thresholdSize", capacityLimit)
		return nil
	}

	logFiles, err := ListLogFilesSortedByModTime(dir)
	if err != nil {
		return fmt.Errorf("failed to list log files: %w", err)
	}

	slog.Info("starting log cleanup", "currentDirSize", dirSize, "thresholdSize", capacityLimit)

	for _, file := range logFiles {
		slog.Info("deleting file", "path", file.Path, "size", file.Size, "modTime", file.ModTime)

		if err := os.Remove(file.Path); err != nil {
			return fmt.Errorf("failed to delete file %s: %w", file.Path, err)
		}

		dirSize -= file.Size
		if dirSize < capacityLimit {
			slog.Info("cleanup complete", "newDirSize", dirSize)
			break
		}
	}

	return nil
}

func StartLogCleanupScheduler() error {
	allocatedSize, err := GetAllocatedStorageForLcmLogs()
	if err != nil {
		return fmt.Errorf("failed to get allocated storage: %w", err)
	}
	capacityLimit := int64(float64(allocatedSize) * lcmManagerLogsThreshold)

	if err := CleanUpLogs(config.LogDir, capacityLimit); err != nil {
		slog.Error("Log cleanup failed", "error", err)
	}

	ticker := time.NewTicker(logCleanupInterval)
	for range ticker.C {
		slog.Info("running scheduled log files cleanup")

		if err := CleanUpLogs(config.LogDir, capacityLimit); err != nil {
			slog.Error("Log cleanup failed", "error", err)
		}
	}

	return nil
}
