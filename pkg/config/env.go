package config

import (
	"fmt"
	"net"
	"os"
	"strings"
)

var (
	TemporalHost        string
	TemporalPort        string
	TemporalHostPort    string
	TowerHost           string
	TowerPort           string
	TowerEndpoint       string
	TowerAdminEndpoint  string
	TowerPrismaEndpoint string
	HostPluginUploadURL string
	HpoPkgsDIR          string
	AgentListenAddr     string
	AgentListenPort     string
	DBbackendDSN        string
	KubernetesNamespace string
	PodHostIP           string
	ServerListenPort    string
	WorkerListenPort    string
	ServerListenAddr    string
	WorkerListenAddr    string
	LocalServerEndpoint string
)

func InitENV() {
	TemporalHost = os.Getenv("TEMPORAL_HOST")
	TemporalPort = os.Getenv("TEMPORAL_PORT")
	TowerHost = os.Getenv("CLOUDTOWER_HOST")
	TowerPort = os.Getenv("CLOUDTOWER_PORT")
	TowerPrismaEndpoint = os.Getenv("CLOUDTOWER_PRISMA_ENDPOINT")
	HostPluginUploadURL = os.Getenv("HOST_PLUGIN_UPLOAD_URL")
	HpoPkgsDIR = os.Getenv("HPO_PKGS_DIR")

	TemporalHostPort = fmt.Sprintf("%s:%s", TemporalHost, TemporalPort)
	TowerEndpoint = fmt.Sprintf("http://%s/api", net.JoinHostPort(TowerHost, TowerPort))
	TowerAdminEndpoint = fmt.Sprintf("http://%s/api/admin", net.JoinHostPort(TowerHost, TowerPort))

	DBbackendDSN = os.Getenv("DB_BACKEND_DSN")

	KubernetesNamespace = os.Getenv("NAMESPACE")
	PodHostIP = os.Getenv("HOST_IP")
	ServerListenPort = os.Getenv("SERVER_LISTEN_PORT")
	WorkerListenPort = os.Getenv("WORKER_LISTEN_PORT")

	ServerListenAddr = os.Getenv("SERVER_LISTEN_ADDR")
	if ServerListenAddr == "" {
		ServerListenAddr = "0.0.0.0"
	}

	WorkerListenAddr = os.Getenv("WORKER_LISTEN_ADDR")
	if WorkerListenAddr == "" {
		WorkerListenAddr = ":8080"
	}

	// kubernetes service env, example: UPGRADE_CENTER_PORT=tcp://*************:80
	LocalServerEndpoint = strings.Replace(os.Getenv("LCM_MANAGER_SERVER_PORT"), "tcp", "http", 1)
}

func InitAgentEnv() {
	AgentListenAddr = os.Getenv("AGENT_LISTEN_ADDR")
	AgentListenPort = os.Getenv("AGENT_LISTEN_PORT")
}
