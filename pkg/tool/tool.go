package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"time"

	"github.com/modood/table"

	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	"github.smartx.com/LCM/lcm-manager/pkg/utils/apiutil"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
	"github.smartx.com/LCM/lcm-manager/third_party/tuna"
)

type Client struct {
	jobRepository postgres.IJobRepository
	towerClient   tower.Client
}

func NewClient(
	jobRepository postgres.IJobRepository,
	towerClient tower.Client,
) *Client {
	return &Client{
		jobRepository: jobRepository,
		towerClient:   towerClient,
	}
}

type Host struct {
	HostName    string
	ClusterName string
	HostUUID    string
	MgtIP       string
	DataIP      string
}

type Jobs struct {
	Index       int
	Name        string
	ID          string
	State       string
	TowerTaskID string
	HostUUID    string
	CreateTime  string
	UpdateTime  string
}

func (c *Client) ListHostJobs(ctx context.Context, hostUUID string, offset int32) error {
	filters := []*apiutil.FieldFilter{
		{
			FieldPath: "host_uuid",
			Operator:  apiutil.FieldFilterOperatorEq,
			Value:     hostUUID,
		},
		{
			FieldPath: "name",
			Operator:  apiutil.FieldFilterOperatorIn,
			Value:     []string{config.ActionConvertToMaster, config.ActionConvertToStorage, config.ActionRemoveHost},
		},
	}

	sorts := []*apiutil.FieldSort{
		{
			FieldName: "create_time",
			Direction: "DESC",
		},
	}

	var limit int32 = 10

	outputJobs := make([]Jobs, 0)
	jobs, err := c.jobRepository.List(ctx, filters, sorts, offset, limit)
	if err != nil {
		slog.Error("Failed to list jobs by job repository", "error", err)
		return err
	}

	for i, job := range jobs {
		towerTaskID := job.Details["tower_task_id"]
		outputJobs = append(outputJobs, Jobs{
			Index:       int(offset) + i + 1,
			Name:        job.Name,
			ID:          job.Id,
			State:       job.State.String(),
			TowerTaskID: towerTaskID,
			HostUUID:    job.HostUuid,
			CreateTime:  job.CreateTime.AsTime().Format(time.RFC3339),
			UpdateTime:  job.UpdateTime.AsTime().Format(time.RFC3339),
		})
	}

	table.Output(outputJobs)
	return err
}

func (c *Client) ListClusterHosts(ctx context.Context) error {
	clusters, err := c.towerClient.GetClustersInfo(ctx)
	if err != nil {
		slog.Error("fail to get clusters by tower api")
		return err
	}

	allHosts := make([]Host, 0)

	for _, cluster := range clusters {
		for _, host := range cluster.Hosts {
			allHosts = append(allHosts, Host{
				HostName:    host.Name,
				ClusterName: cluster.Name,
				HostUUID:    host.Local_id,
				MgtIP:       host.Management_ip,
				DataIP:      host.Data_ip,
			})
		}
	}

	table.Output(allHosts)
	return nil
}

func (c *Client) ShowTowerTask(ctx context.Context, taskID string) error {
	towerTaskInfo, err := c.towerClient.GetTask(ctx, taskID)
	if err != nil {
		slog.Error("fail to get tower task info", "error", err)
		return err
	}

	val, err := json.MarshalIndent(towerTaskInfo, "", "  ")
	if err != nil {
		slog.Error("fail to marshal tower task info", "error", err)
		return err
	}
	fmt.Println("Tower Task Info:")
	fmt.Println(string(val))
	return nil
}

func (c *Client) UpdateTowerTask(ctx context.Context, taskID string, status string) error {
	taskHandler := tower.NewTaskHandler(c.towerClient, nil)
	taskHandler.SetTaskID(taskID)

	towerTaskInfo, err := taskHandler.GetTask(ctx, taskID)
	if err != nil {
		slog.Error("fail to get tower task info", "error", err)
		return err
	}

	val, err := json.MarshalIndent(towerTaskInfo, "", "  ")
	if err != nil {
		slog.Error("fail to marshal tower task info", "error", err)
		return err
	}
	fmt.Println("Origin Tower Task Info:")
	fmt.Println(string(val))

	taskInput := &tower.UpdateTaskInput{
		Progress: towerTaskInfo.Progress,
		Status:   tower.TaskStatus(status),
		Args:     towerTaskInfo.Args,
		Done:     true,
	}

	if err := taskHandler.UpdateTask(ctx, *taskInput); err != nil {
		slog.Error("UpdateTask failed.", "error", err)
		return err
	}

	newTowerTaskInfo, err := taskHandler.GetTask(ctx, taskID)
	if err != nil {
		slog.Error("fail to get tower task info", "error", err)
		return err
	}

	newVal, err := json.MarshalIndent(newTowerTaskInfo, "", "  ")
	if err != nil {
		slog.Error("fail to marshal tower task info", "error", err)
		return err
	}
	fmt.Println("New Tower Task Info:")
	fmt.Println(string(newVal))

	return nil
}

func (c *Client) ShowHostlabels(ctx context.Context, hostUUID string) error {
	clusters, err := c.towerClient.GetClustersInfo(ctx)
	if err != nil {
		slog.Error("fail to get clusters by tower api")
		return err
	}

	var hostCluster tower.QueryClustersInfoClustersCluster
	found := false
	for _, cluster := range clusters {
		for _, host := range cluster.Hosts {
			if host.Local_id == hostUUID {
				found = true

				hostCluster = cluster
				break
			}
		}
	}

	if !found {
		slog.Error("can't find host cluster info")
	}

	tunaClient := tuna.NewClient(hostCluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetHostLabels(hostUUID)
	if err != nil {
		slog.Error("fail to get host labels", "error", err)
		return err
	}

	val, err := json.MarshalIndent(res, "", "  ")
	if err != nil {
		slog.Error("fail to marshal host labels", "error", err)
		return err
	}

	fmt.Println("Host Labels:")
	fmt.Println(string(val))

	return nil
}

func (c *Client) UpdateHostLabelRole(ctx context.Context, hostUUID string, role string) error {
	clusters, err := c.towerClient.GetClustersInfo(ctx)
	if err != nil {
		slog.Error("fail to get clusters by tower api")
		return err
	}

	var hostCluster tower.QueryClustersInfoClustersCluster
	found := false
	for _, cluster := range clusters {
		for _, host := range cluster.Hosts {
			if host.Local_id == hostUUID {
				found = true

				hostCluster = cluster
				break
			}
		}
	}

	if !found {
		slog.Error("can't find host cluster info")
	}

	handler := workflowutils.NewActivityBaseHandler(c.jobRepository, nil, c.towerClient, nil, nil)

	err = handler.UpdateHostLabel(ctx, hostCluster.Local_id, hostUUID, config.LcmManagerHostRole, config.HostLabel(role))
	if err != nil {
		slog.Error("failed to update host label role", "error", err)
		return err
	}

	return nil
}

func (c *Client) UpdateActionStatus(ctx context.Context, hostUUID string, status string) error {
	clusters, err := c.towerClient.GetClustersInfo(ctx)
	if err != nil {
		slog.Error("fail to get clusters by tower api")
		return err
	}

	var hostCluster tower.QueryClustersInfoClustersCluster
	found := false
	for _, cluster := range clusters {
		for _, host := range cluster.Hosts {
			if host.Local_id == hostUUID {
				found = true

				hostCluster = cluster
				break
			}
		}
	}

	if !found {
		slog.Error("can't find host cluster info")
	}

	handler := workflowutils.NewActivityBaseHandler(c.jobRepository, nil, c.towerClient, nil, nil)

	err = handler.UpdateHostLabel(ctx, hostCluster.Local_id, hostUUID, config.LcmManagerAction, config.HostLabel(status))
	if err != nil {
		slog.Error("failed to update host label LCM_MANAGER_ACTION", "error", err)
		return err
	}

	return nil
}
