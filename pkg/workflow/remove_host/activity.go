package removehost

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"net/rpc"
	"strconv"
	"strings"
	"time"

	"github.com/eduardolat/goeasyi18n"
	version "github.com/hashicorp/go-version"
	zbspb "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	agentclient "github.smartx.com/LCM/lcm-manager/pkg/client/agent"
	towerclient "github.smartx.com/LCM/lcm-manager/pkg/client/tower"
	zbsclient "github.smartx.com/LCM/lcm-manager/pkg/client/zbs"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/i18n"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	errorutil "github.smartx.com/LCM/lcm-manager/pkg/utils/errors"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
	"github.smartx.com/LCM/lcm-manager/third_party/tuna"
)

type RemoveHostStage string

const (
	ChunkStateUnknown          string          = "CHUNK_STATE_UNKNOWN"
	ChunkStateIDLE             string          = "CHUNK_STATE_IDLE"
	ChunkStateInUse            string          = "CHUNK_STATE_IN_USE"
	ChunkStateRemoving         string          = "CHUNK_STATE_REMOVING"
	ChunkStateNotFound         string          = "CHUNK_STATE_NOT_FOUND"
	EstimatedInitTimeCostSec   int             = 32       // seconds
	EstimatedChunkRemoveSpeed  float64         = 40000000 // Bytes per second
	RemoveHostStageInit        RemoveHostStage = "InitStage"
	RemoveHostStageChunkRemove RemoveHostStage = "ChunkRemoveStage"
	RemoveHostStageMetaRemove  RemoveHostStage = "MetaRemoveStage"
	RemoveHostStageFinish      RemoveHostStage = "FinishStage"
)

type RemoveHostCheck struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	i18n                  *goeasyi18n.I18n
}

func NewRemoveHostCheck(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	i18n *goeasyi18n.I18n,
) *RemoveHostCheck {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, nil, i18n)

	return &RemoveHostCheck{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		i18n:                  i18n,
	}
}

func (h *RemoveHostCheck) GetRemoveHostActivities(input RemoveHostCheckWorkflowInput) ([]any, error) {
	// Can't use towerClient in RemoveHostCheck here, because GetActivities is called by temporal workflow,
	// and GetActivities is not called as an activity. towerClient init in activity instead of workflow.
	// So is necessary to init towerClient here to get cluster type.
	towerClient, err := towerclient.NewTowerClient()
	if err != nil {
		slog.Error("failed to init tower client", "error", err)
		return nil, err
	}

	cluster, err := towerClient.GetClusterInfoByLocalID(context.Background(), input.ClusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return nil, err
	}

	activities := []any{h.RemoveHostCostTimeEvaluate}

	activities = append(activities, h.RemoveHostCheckTargetHostStatus)

	if cluster.Hypervisor == tower.HypervisorElf {
		activities = append(activities, h.RemoveHostCheckHostExistVMs)

		if cluster.Type != tower.ClusterTypeSmtxZbs {
			activities = append(activities, h.RemoveHostCheckMountedUsbDevices)
		}
	}

	activities = append(activities, h.RemoveHostCheckTargetHostRole)
	activities = append(activities, h.RemoveHostCheckClusterMasterCount)
	activities = append(activities, h.RemoveHostCheckZkMongoServiceStatus)

	if cluster.Type != tower.ClusterTypeSmtxElf {
		activities = append(activities, h.RemoveHostCheckZBSChunkUseState)
	}

	vSmtxZbs, _ := version.NewVersion(config.SmtxZbsSupportECVersion)
	vSmtxOs, _ := version.NewVersion(config.SmtxOsSupportECVersion)
	clusterVersion, _ := version.NewVersion(cluster.Version)

	if (cluster.Type == tower.ClusterTypeSmtxZbs && clusterVersion.GreaterThanOrEqual(vSmtxZbs)) ||
		(cluster.Type == tower.ClusterTypeSmtxOs && clusterVersion.GreaterThanOrEqual(vSmtxOs)) {
		activities = append(activities, h.RemoveHostCheckClusterStorageEC)
	}

	if cluster.Type != tower.ClusterTypeSmtxElf {
		activities = append(activities, h.RemoveHostCheckClusterCapacitySpace)
	}

	if cluster.Hypervisor == tower.HypervisorElf && cluster.Type != tower.ClusterTypeSmtxZbs {
		activities = append(activities, h.RemoveHostCheckPlacementGroups)
	}

	activities = append(activities, h.RemoveHostCheckClusterExtending)
	activities = append(activities, h.RemoveHostCheckEnteringMaintenanceHost)
	activities = append(activities, h.RemoveHostCheckExistMaintenanceHost)
	activities = append(activities, h.RemoveHostCheckExistRemovingHost)
	activities = append(activities, h.RemoveHostCheckExistConvertingRoleHost)

	if cluster.Type != tower.ClusterTypeSmtxElf {
		activities = append(activities, h.RemoveHostCheckClusterExistPextentRecover)
		activities = append(activities, h.RemoveHostCheckClusterExistPextentDead)
	}

	activities = append(activities, h.RemoveHostCheckClusterUpgrading)

	if cluster.Stretch {
		activities = append(activities, h.RemoveHostCheckWitnessStatus)
		activities = append(activities, h.RemoveHostCheckMetroClusterHealthyHostCount)
	}

	return activities, nil
}

func (h *RemoveHostCheck) RemoveHostCheckTargetHostStatus(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("CheckTargetHostStatus started.", "JobID", input.JobID)

	var err error

	checkName := "RemoveHostCheckTargetHostStatus"

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	// check host merged status
	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return err
	}

	logger.Info("get target host merged_status,", "merged_status", targetHost.Merged_status)

	if targetHost.Merged_status == tower.HostMergedStatusDisconnected {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_WARNING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	// check host maintenance status
	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetHostState(input.HostUUID)
	if err != nil {
		logger.Error("failed to get host state", "error", err)
		return err
	}

	hostState := res.HostState.State
	logger.Info("get target host state,", "host_state", hostState)

	if hostState == tuna.HostStateMaintenanceMode {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_SUCCESS, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_FAILED, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckTargetHostRole(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckTargetHostRole started.")

	var err error

	checkName := "RemoveHostCheckTargetHostRole"

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return err
	}

	targetHostRole, err := workflowutils.GetHostRole(input.ClusterIP, input.HostUUID, targetHost.Role)
	if err != nil {
		logger.Error("fail to get target host role", "host", targetHost.Name, "error", err)
		return err
	}

	logger.Info("get target host role.", "role", targetHostRole)

	if targetHostRole != string(config.HostRoleStorage) {
		logger.Error("check target host role failed, host role is not storage", "role", targetHostRole)

		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_FAILED, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_SUCCESS, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterMasterCount(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterMasterCount started", input)

	checkName := "RemoveHostCheckClusterMasterCount"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	targetHostConnected := false
	healthyMasterCount := 0

	ClusterInfo, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	for _, host := range ClusterInfo.Hosts {
		if host.Local_id == input.HostUUID && host.Status == tower.HostStatusConnectedHealthy {
			targetHostConnected = true
		}

		if host.Role == string(config.HostRoleMaster) && host.Status == tower.HostStatusConnectedHealthy {
			healthyMasterCount++
		}
	}

	if ClusterInfo.Stretch {
		healthyMasterCount++
	}

	var checkState serverpb.CheckState

	if !targetHostConnected {
		switch {
		case healthyMasterCount == 2:
			checkState = serverpb.CheckState_CHECK_STATE_WARNING
		case healthyMasterCount <= 1 || healthyMasterCount > 5:
			checkState = serverpb.CheckState_CHECK_STATE_FAILED
		case healthyMasterCount >= 3 && healthyMasterCount <= 5:
			checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
		}
	} else {
		if healthyMasterCount < 3 || healthyMasterCount > 5 {
			checkState = serverpb.CheckState_CHECK_STATE_FAILED
		} else if healthyMasterCount >= 3 && healthyMasterCount <= 5 {
			checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
		}
	}

	logger.Info("check result", "connect status", targetHostConnected, "count", healthyMasterCount, "result", checkState)

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"Count": healthyMasterCount,
		},
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckHostExistVMs(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckHostExistVM started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	checkName := "RemoveHostCheckHostExistVMs"
	if cluster.Type == tower.ClusterTypeSmtxZbs && cluster.Hypervisor != tower.HypervisorVmware {
		checkName = "ZBS.RemoveHostCheckHostExistVMs"
	}

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	if cluster.Hypervisor == tower.HypervisorVmware {
		logger.Info("VMware platform, skip check host exist VMs.")

		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_WARNING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	hostVMs, err := h.towerClient.GetHostVMsByHostLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host vms info", "error", err)
		return err
	}

	inRecycleBinVMs := make([]string, 0)

	for _, vm := range hostVMs.Vms {
		logger.Info("get host vm", "host", input.HostUUID, "vm_name", vm.Name)
		if vm.In_recycle_bin {
			inRecycleBinVMs = append(inRecycleBinVMs, vm.Local_id)
		}
	}

	logger.Info("get host vm num.", "vm_num", len(hostVMs.Vms), "in_recycle_bin_vm_num", len(inRecycleBinVMs))

	var checkState serverpb.CheckState
	if len(hostVMs.Vms) == len(inRecycleBinVMs) {
		checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
	} else {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckMountedUsbDevices(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckMountedUsbDevices started.")

	checkName := "RemoveHostCheckMountedUsbDevices"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	usbDevices, err := h.towerClient.GetUsbDevicesByHostLocalId(ctx, input.HostUUID)
	if err != nil {
		logger.Error("fail to get host usb devices", "error", err)
		return err
	}

	ubsMountedVMs := make([]string, 0)
	usbDevicesNames := make([]string, 0)

	for _, usbDevice := range usbDevices {
		if len(usbDevice.Vms) != 0 {
			usbDevicesNames = append(usbDevicesNames, usbDevice.Name)

			for _, vm := range usbDevice.Vms {
				ubsMountedVMs = append(ubsMountedVMs, vm.Name)
			}
		}
	}

	logger.Info("get host usb mounted vms.", "ubsMountedVMs", ubsMountedVMs, "usbDevicesNames", usbDevicesNames)

	var zhOptions, enOptions *goeasyi18n.Options
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if len(ubsMountedVMs) != 0 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED

		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"UsbDevices": i18n.FormatList(usbDevicesNames, "zh"),
			},
		}

		enOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"UsbDevices": i18n.FormatList(usbDevicesNames, "en"),
			},
		}
	}

	if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckPlacementGroups(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckPlacementGroups started.")

	checkName := "RemoveHostCheckPlacementGroups"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	placementGroups, err := h.towerClient.GetPlacementGroupsByHostLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("fail to get host usb devices", "error", err)
		return err
	}

	placementGroupNames := make([]string, 0)

	for _, placementGroup := range placementGroups {
		placementGroupNames = append(placementGroupNames, placementGroup.Name)
	}

	logger.Info("get host vm placement groups.", "placementGroupNames", placementGroupNames)

	var zhOptions, enOptions *goeasyi18n.Options
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if len(placementGroups) != 0 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED

		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"PlacementGroups": i18n.FormatList(placementGroupNames, "zh"),
			},
		}

		enOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"PlacementGroups": i18n.FormatList(placementGroupNames, "en"),
			},
		}
	}

	if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckZkMongoServiceStatus(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckZkMongoServiceStatus started.")

	checkName := "CheckZkMongoServiceStatus"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	zkServiceName := "zookeeper.service"
	mongoServiceName := "mongod.service"

	for _, host := range cluster.Hosts {
		hostRole, err := workflowutils.GetHostRole(input.ClusterIP, host.Local_id, host.Role)
		if err != nil {
			logger.Error("fail to get host role", "host", host.Name, "error", err)
			return err
		}

		if hostRole != string(config.HostRoleMaster) {
			logger.Info("Skip check zookeeper and mongo service status of storage node.", "host_name", host.Name)
			continue
		}

		tunaClient := tuna.NewClient(host.Management_ip, config.TunaAPIToken)

		err = h.baseHandler.CheckHostServiceStatus(ctx, tunaClient, []string{zkServiceName, mongoServiceName})
		if err != nil {
			logger.Error("check host service status failed.", "host_name", host.Name, "error", err)
			checkState = serverpb.CheckState_CHECK_STATE_FAILED

			break
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckZBSChunkUseState(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckZBSChunkUseState started.")

	var err error

	checkName := "RemoveHostCheckZBSChunkUseState"

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	checkFailedHosts := make([]string, 0)

	for _, host := range cluster.Hosts {
		if host.Local_id != input.HostUUID && (host.State == tower.HostStateRemoving || host.State == tower.HostStateIdle) {
			checkFailedHosts = append(checkFailedHosts, workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware))
			logger.Error("Check host chunk use state failed", "host_uuid", host.Local_id, "state", host.State)
		}
	}

	var zhOptions, enOptions *goeasyi18n.Options
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if len(checkFailedHosts) >= 1 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED

		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(checkFailedHosts, "zh"),
			},
		}

		enOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(checkFailedHosts, "en"),
			},
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterExistPextentRecover(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterExistPextentRecover started.")

	var err error

	checkName := "CheckClusterExistPextentRecover"

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return err
	}

	exist, err := zbsClient.IsExistPextendNeedRecover()
	if err != nil {
		logger.Error("failed to check pextent need recover", "error", err)
		return err
	}

	logger.Info("check pextent need recover", "exist", exist)

	var checkState serverpb.CheckState
	if exist {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	} else {
		checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterExistPextentDead(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterExistPextentDead started.")

	var err error

	checkName := "RemoveHostCheckClusterExistPextentDead"

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return err
	}

	exist, err := zbsClient.IsExistPextendDead()
	if err != nil {
		logger.Error("failed to check existing pextent dead", "error", err)
		return err
	}

	logger.Info("check pextent existing pextent dead", "exist", exist)

	var checkState serverpb.CheckState
	if exist {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	} else {
		checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func doCheckClusterCapacitySpace(ctx context.Context, cluster *tower.QueryClusterInfoByLocalIdCluster, targetChunkIP string) (bool, uint64, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("doCheckClusterCapacitySpace started.")

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return false, 0, err
	}

	storagePool, err := zbsClient.GetSystemStoragePool(ctx)
	if err != nil {
		logger.Error("fail to get system storage pool", "error", err)
		return false, 0, err
	} else if storagePool == nil {
		logger.Error("no system storage pool exist.")
		return false, 0, nil
	}

	targetChunk, err := zbsClient.GetChunkByIP(ctx, targetChunkIP)
	if err != nil && errors.Is(err, zbsclient.ErrChunkNotFound) {
		logger.Error("no chunk with target ip exist", "ip", targetChunkIP)
		return false, 0, nil
	} else if err != nil {
		logger.Error("fail to get chunk", "chunk_ip", targetChunkIP, "error", err)
		return false, 0, err
	}

	isTieringEnabled, err := zbsClient.IsTieringEnabled(ctx)
	if err != nil {
		logger.Error("check IsTieringEnabled failed", "error", err)
		return false, 0, err
	}

	if isTieringEnabled {
		targetPerfValidSpace := *targetChunk.SpaceInfo.PerfValidDataSpace
		totalPerfValidSpace := *storagePool.Space.PerfValidDataSpace
		totalPerfAllocatedSpace := *storagePool.Space.PerfAllocatedDataSpace
		remainingPerfValidSpace := totalPerfValidSpace - targetPerfValidSpace
		logger.Info("check perf data space.", "targetPerfValidSpace", targetPerfValidSpace, "totalPerfValidSpace", totalPerfValidSpace, "totalPerfAllocatedSpace", totalPerfAllocatedSpace)

		if remainingPerfValidSpace <= totalPerfAllocatedSpace {
			logger.Error("remainingPerfValidSpace < totalPerfAllocatedSpace")
			lackSpace := totalPerfAllocatedSpace - remainingPerfValidSpace
			lackSpaceGiB := lackSpace / 1024 / 1024 / 1024
			if lackSpaceGiB < 1 {
				lackSpaceGiB = 1
			}
			return false, lackSpaceGiB, nil
		}
	}

	targetValidSpace := *targetChunk.SpaceInfo.ValidDataSpace
	totalValidSpace := *storagePool.Space.ValidDataSpace
	totalAllocatedSpace := *storagePool.Space.AllocatedDataSpace
	remainingValidSpace := totalValidSpace - targetValidSpace
	logger.Info("get target chunk space info", "targetValidSpace", targetValidSpace, "totalValidSpace", totalValidSpace, "totalAllocatedSpace", totalAllocatedSpace)

	if remainingValidSpace <= totalAllocatedSpace {
		logger.Error("remainingValidSpace < totalAllocatedSpace")
		lackSpace := totalAllocatedSpace - remainingValidSpace
		lackSpaceGiB := lackSpace / 1024 / 1024 / 1024
		if lackSpaceGiB < 1 {
			lackSpaceGiB = 1
		}
		return false, lackSpaceGiB, nil
	}

	return true, 0, nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterCapacitySpace(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterCapacitySpace started.")

	var err error

	checkName := "RemoveHostCheckClusterCapacitySpace"

	if init {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return err
	}

	checkResult, lackSpaceGiB, err := doCheckClusterCapacitySpace(ctx, cluster, targetHost.Data_ip)
	if err != nil {
		logger.Error("doCheckClusterCapacitySpace failed")
		return err
	}

	var options *goeasyi18n.Options
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if !checkResult {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED

		if lackSpaceGiB > 0 {
			options = &goeasyi18n.Options{
				Data: map[string]any{
					"Capacity": fmt.Sprintf("%d GiB", lackSpaceGiB),
				},
			}
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterExtending(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterExtending started.")

	checkName := "CheckClusterExtending"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetClusterExtendindItem()
	if err != nil {
		logger.Error("failed to get cluster extending records", "error", err)
		return err
	}

	var extendingHostNames []string

	if res != nil {
		logger.Error("Cluster in extending status", "extend_uuid", res.UUID)
		for _, host := range res.Hosts {
			extendingHostNames = append(extendingHostNames, host.HostName)
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var zhOptions, enOptions *goeasyi18n.Options
	if len(extendingHostNames) > 0 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED

		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(extendingHostNames, "zh"),
			},
		}
		enOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(extendingHostNames, "en"),
			},
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckEnteringMaintenanceHost(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckEnteringMaintenanceHost started.")

	checkName := "CheckEnteringMaintenanceHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	clusters, err := h.towerClient.QueryClustersByLocalIds(ctx, []string{input.ClusterUUID})
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	} else if len(clusters) == 0 {
		return fmt.Errorf("failed to get cluster %s", input.ClusterUUID)
	}

	var enteringMaintenanceHost string

	for _, host := range clusters[0].Hosts {
		if host.Host_state.State == tower.MaintenanceModeEnumEnteringMaintenanceMode {
			enteringMaintenanceHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get entering maintenance mode host.", "host", enteringMaintenanceHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if enteringMaintenanceHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": enteringMaintenanceHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckExistMaintenanceHost(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckExistMaintenanceHost started.")

	checkName := "CheckExistMaintenanceHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	clusters, err := h.towerClient.QueryClustersByLocalIds(ctx, []string{input.ClusterUUID})
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	} else if len(clusters) == 0 {
		return fmt.Errorf("failed to get cluster %s", input.ClusterUUID)
	}

	var maintenanceHost string

	for _, host := range clusters[0].Hosts {
		if host.Host_state.State == tower.MaintenanceModeEnumMaintenanceMode && host.Local_id != input.HostUUID {
			maintenanceHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error(" maintenance mode host.", "host", maintenanceHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if maintenanceHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": maintenanceHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckExistRemovingHost(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckExistRemovingHost started.")

	checkName := "RemoveHostCheckExistRemovingHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	var removingHost string

	for _, host := range cluster.Hosts {
		labelVal, err := workflowutils.GetHostLabelValue(tunaClient, host.Local_id, config.LcmManagerAction)
		if err != nil {
			logger.Error("failed to get host labels", "error", err)
			return err
		}

		if labelVal == config.HostRemoveRunning || (host.Local_id != input.HostUUID && labelVal == config.HostRemoveFailed) {
			removingHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get removing/remove_failed host", "hostname", removingHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if removingHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": removingHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckExistConvertingRoleHost(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckExistConvertingRoleHost started.")

	checkName := "RemoveHostCheckExistConvertingRoleHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	var convertingHost string

	for _, host := range cluster.Hosts {
		labelVal, err := workflowutils.GetHostLabelValue(tunaClient, host.Local_id, config.LcmManagerAction)
		if err != nil {
			logger.Error("failed to get host labels", "error", err)
			return err
		}

		if labelVal == config.RoleConvertRunning || labelVal == config.RoleConvertFailed {
			convertingHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get role converting / role convert failed host", "hostname", convertingHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if convertingHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": convertingHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterUpgrading(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterUpgrading started.")

	checkName := "CheckClusterUpgrading"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetClusterUpgraderProgress()
	if err != nil {
		logger.Error("failed to get cluster upgrade records", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	if res != nil {
		logger.Error("Cluster in upgrading status")
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckClusterStorageEC(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error { //nolint: funlen
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckClusterStorageEC started.")

	if !init {
		_, err := h.checkResultRepository.Get(ctx, cid)
		if err != nil && errors.Is(err, errorutil.ErrNotFound) {
			logger.Info("RemoveHostCheckClusterStorageEC is not required, skip checking.")
			return nil
		} else if err != nil {
			logger.Error("fail to get initial check result of RemoveHostCheckClusterStorageEC from db ", "error", err)
			return err
		}
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return err
	}

	checkName := "RemoveHostCheckClusterStorageEC"

	if init {
		exist, err := zbsClient.CheckExistECVolumePool(ctx)
		if err != nil {
			logger.Error("CheckExistECVolumePool failed", "error", err)
			return err
		} else if !exist {
			logger.Info("EC volume pool not exist, skip RemoveHostCheckClusterStorageEC.")
			return nil
		}

		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	ecMaxKM, err := zbsClient.GetECMaxKM(ctx)
	if err != nil {
		logger.Error("fail to get EC max km", "error", err)
		return err
	} else if ecMaxKM == nil {
		if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	result, err := zbsClient.CheckClusterECWithOfflineChunks(ctx, targetHost.Data_ip, ecMaxKM)
	if err != nil {
		logger.Error("CheckClusterECWithOfflineChunks failed", "error", err)
		return err
	}

	var options *goeasyi18n.Options
	if !result {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"Count": ecMaxKM.MaxKM,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckWitnessStatus(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckWitnessStatus started.")

	checkName := "CheckWitnessNodeStatus"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterWitnessStatus(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	if cluster.Stretch && cluster.Metro_availability_checklist.Witness.Status != tower.MetroCheckStatusEnumHealthy {
		logger.Error("Cluster witness node is not healthy", "witness_data_ip", cluster.Witness.Data_ip, "status", cluster.Metro_availability_checklist.Witness.Status)
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) RemoveHostCheckMetroClusterHealthyHostCount(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckMetroClusterHealthyHostCount started.")

	checkName := "RemoveHostCheckMetroClusterHealthyHostCount"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	primaryZoneHosts := make([]string, 0)
	secondaryZoneHosts := make([]string, 0)

	isTargetHostPrimaryZone := false
	for _, host := range cluster.Hosts {
		if host.Local_id == input.HostUUID {
			isTargetHostPrimaryZone = host.Zone.Is_preferred
			logger.Info("get target host zone", "is_preferred", isTargetHostPrimaryZone)
			continue
		}

		if host.Merged_status != tower.HostMergedStatusHealthy {
			continue
		}

		if host.Zone.Is_preferred {
			primaryZoneHosts = append(primaryZoneHosts, host.Name)
		} else {
			secondaryZoneHosts = append(secondaryZoneHosts, host.Name)
		}
	}

	var msgKey string
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if len(primaryZoneHosts) < 2 {
		logger.Error("Primary zone healthy host count is less than 2", "cluster", input.ClusterUUID, "count", len(primaryZoneHosts))

		msgKey = "ZoneNamePrimaryZone"
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	} else if len(secondaryZoneHosts) < 1 {
		logger.Error("Secondary zone healthy host count is less than 1", "cluster", input.ClusterUUID, "count", len(secondaryZoneHosts))

		msgKey = "ZoneNameSecondaryZone"
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	var zhOptions, enOptions *goeasyi18n.Options
	if msgKey != "" {
		langZh, langEn := workflowutils.GetZhAndEnI18nLanguageName(input.IsVmware)
		zoneNameZh := h.i18n.T(langZh, msgKey, goeasyi18n.Options{})
		zoneNameEn := h.i18n.T(langEn, msgKey, goeasyi18n.Options{})

		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"Zone": zoneNameZh,
			},
		}

		enOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"Zone": zoneNameEn,
			},
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) removeHostCostTimeValidateAndEvaluate(ctx context.Context, input RemoveHostCheckWorkflowInput) (bool, int, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("removeHostCostTimeValidateAndEvaluate started")

	ClusterInfo, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return false, 0, err
	}

	targetHostConnected := false
	masterCount := 0

	for _, host := range ClusterInfo.Hosts {
		if host.Local_id == input.HostUUID {
			if host.Status == tower.HostStatusConnectedHealthy {
				targetHostConnected = true
			}

			// target host role must be storage
			continue
		}

		hostRole, err := workflowutils.GetHostRole(input.ClusterIP, host.Local_id, host.Role)
		if err != nil {
			logger.Error("fail to get host role", "host", host.Name, "error", err)
			return false, 0, err
		}

		if hostRole == string(config.HostRoleMaster) {
			masterCount++
		}
	}

	if ClusterInfo.Stretch {
		masterCount++
	}

	var supportEstimateTimeCost bool
	if ClusterInfo.Type == tower.ClusterTypeSmtxElf {
		supportEstimateTimeCost = true
	} else if !targetHostConnected && masterCount == 2 {
		supportEstimateTimeCost = true
	}

	if supportEstimateTimeCost {
		estimatedTimeCost := masterCount*config.MetaRemoveNodePerNodeTimeMinute + config.MetaRemoveNodeBaseTimeMinute
		logger.Info("estimated time cost", "time_minutes", estimatedTimeCost)

		return true, estimatedTimeCost, nil
	}

	logger.Info("estimate time cost is not supported")

	return false, 0, nil
}

func (h *RemoveHostCheck) RemoveHostCostTimeEvaluate(ctx context.Context, cid string, input RemoveHostCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCostTimeEvaluate started")

	if !init {
		return nil
	}

	supportEstimateTimeCost, estimatedTimeCost, err := h.removeHostCostTimeValidateAndEvaluate(ctx, input)
	if err != nil {
		logger.Error("fail to validate and evaluate cost time", "error", err)
		return err
	}

	if !supportEstimateTimeCost {
		return nil
	}

	checkName := "CostTimeEvaluate"
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	options := &goeasyi18n.Options{
		Data: map[string]any{
			"CostTime": estimatedTimeCost,
		},
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RemoveHostCheck) CreateRemoveHostCheckTowerTask(ctx context.Context, input RemoveHostCheckWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("CreateRemoveHostCheckTowerTask started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
		},
	}

	taskArgs := map[string]string{
		"job_id":    input.JobID,
		"host_uuid": input.HostUUID,
		"state":     "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, "RemoveHostCheck", options, options, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *RemoveHostCheck) UpdateRemoveHostCheckTowerTaskWithSuccess(ctx context.Context, input RemoveHostCheckWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRemoveHostCheckTowerTaskWithSuccess started.")

	taskInput := &tower.UpdateTaskInput{
		Progress: 1.0,
		Status:   tower.TaskStatusSuccessed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "finished",
		},
		Done: true,
	}

	if err := h.baseHandler.UpdateTowerTask(ctx, taskID, taskInput); err != nil {
		logger.Error("UpdateTowerTask failed.", err)
		return err
	}

	return nil
}

func (h *RemoveHostCheck) UpdateRemoveHostCheckTowerTaskWithFailed(ctx context.Context, input RemoveHostCheckWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRemoveHostCheckTowerTaskWithFailed started.")

	taskInput := &tower.UpdateTaskInput{
		Status: tower.TaskStatusFailed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "failed",
		},
		Done: true,
	}

	if err := h.baseHandler.UpdateTowerTask(ctx, taskID, taskInput); err != nil {
		logger.Error("UpdateTowerTask failed.", err)
		return err
	}

	return nil
}

func (h *RemoveHostCheck) UpdateRemoveHostCheckJobStateWithRunning(ctx context.Context, input RemoveHostCheckWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_RUNNING, nil)
}

func (h *RemoveHostCheck) UpdateRemoveHostCheckJobStateWithSuccess(ctx context.Context, input RemoveHostCheckWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_SUCCESS, nil)
}

func (h *RemoveHostCheck) UpdateRemoveHostCheckJobStateWithFailed(ctx context.Context, input RemoveHostCheckWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, nil)
}

type RemoveHost struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	hpClient              hpoperator.Client
	i18n                  *goeasyi18n.I18n
}

func NewRemoveHost(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	hpClient hpoperator.Client,
	i18n *goeasyi18n.I18n,
) *RemoveHost {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, hpClient, i18n)

	return &RemoveHost{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		hpClient:              hpClient,
		i18n:                  i18n,
	}
}

func (h *RemoveHost) ChildRemoveHostCheckWorkflowPreparation(ctx context.Context, input RemoveHostWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("ChildRemoveHostCheckWorkflowPreparation started.")

	cwJobID, err := workflowutils.CreateJob(ctx, h.jobRepository, input.ClusterUUID, input.HostUUID, config.ActionRemoveHostCheck)
	if err != nil {
		logger.Error("fail to create child remove host check job", "error", err)
		return "", err
	}

	return cwJobID, nil
}

func (h *RemoveHost) ValidateRemoveHost2ndCheckResult(ctx context.Context, input RemoveHostWorkflowInput, cwJobID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("ValidateRemoveHost2ndCheckResult started.", "job_id", input.JobID, "precheck_job_id", cwJobID)

	checkResults, err := h.checkResultRepository.GetByJobID(ctx, cwJobID)
	if err != nil {
		logger.Error("Fail to get remove host 2nd precheck result", "error", err)
		return err
	}

	result := true

	for _, checkResult := range checkResults {
		if checkResult.State == serverpb.CheckState_CHECK_STATE_FAILED {
			logger.Error("Remove host 2nd check failed.", "failed_item", checkResult)

			result = false

			break
		}
	}

	if !result {
		msgKey := "RemoveHost2ndCheckFailed"
		options := goeasyi18n.Options{}
		langZh, langEn := workflowutils.GetZhAndEnI18nLanguageName(input.IsVmware)
		messages := []*serverpb.MessageItem{
			{
				Lang:    serverpb.MessageLang_LANG_EN,
				Message: h.i18n.T(langEn, msgKey, options),
			},
			{
				Lang:    serverpb.MessageLang_LANG_ZH,
				Message: h.i18n.T(langZh, msgKey, options),
			},
		}

		err := h.jobRepository.UpdateJobStateWithEC(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, messages, serverpb.ErrorCode_SECOND_PRE_CHECK_FAILED)
		if err != nil {
			return err
		}

		return temporal.NewNonRetryableApplicationError("Remove host 2nd check failed", "PreCheckFailed", nil, nil)
	}

	return nil
}

func (h *RemoveHost) CreateRemoveHostTowerTask(ctx context.Context, input RemoveHostWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("CreateRemoveHostTowerTask started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
		},
	}

	taskArgs := map[string]string{
		"job_id":    input.JobID,
		"host_uuid": input.HostUUID,
		"state":     "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, "RemoveHost", options, options, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *RemoveHost) UpdateRemoveHostTowerTaskWithSuccess(ctx context.Context, input RemoveHostWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRemoveHostTowerTaskWithSuccess started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
		},
	}

	taskInput := &tower.UpdateTaskInput{
		Progress: 1.0,
		Status:   tower.TaskStatusSuccessed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "finished",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, "RemoveHost", options, options, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *RemoveHost) UpdateRemoveHostTowerTaskWithFailed(ctx context.Context, input RemoveHostWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRemoveHostTowerTaskWithFailed started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
		},
	}

	taskInput := &tower.UpdateTaskInput{
		Status: tower.TaskStatusFailed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "failed",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, "RemoveHost", options, options, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *RemoveHost) getRemoveHostJobStartTime(ctx context.Context, input RemoveHostWorkflowInput) (time.Time, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("getRemoveHostJobStartTime started.")

	job, err := h.jobRepository.Get(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get job in db", "job_id", input.JobID)
		return time.Time{}, err
	}

	logger.Info("get job start time", "start_time", job.CreateTime)
	return job.CreateTime.AsTime(), nil
}

func (h *RemoveHost) stoPoolRemoveHostCostTimeEvaluate(ctx context.Context, input RemoveHostWorkflowInput) (int, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("stoPoolRemoveHostCostTimeEvaluate started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return 0, err
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("fail to get host info", "error", err)
		return 0, err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("fail to create zbs client", "error", err)
		return 0, err
	}

	targetChunk, err := zbsClient.GetChunkByIP(ctx, targetHost.Data_ip)
	if err != nil && errors.Is(err, zbsclient.ErrChunkNotFound) {
		logger.Error("no chunk with target ip exist", "ip", targetHost.Data_ip)
		return 0, nil
	} else if err != nil {
		logger.Error("fail to get target host chunk", "chunk_ip", targetHost.Data_ip, "error", err)
		return 0, err
	}

	if targetChunk.SpaceInfo == nil || targetChunk.SpaceInfo.AllocatedDataSpace == nil {
		logger.Error("chunk space info is empty", "ip", targetHost.Data_ip)
		return 0, fmt.Errorf("chunk with ip %s space info is empty", targetHost.Data_ip)
	}

	currentAllocatedSpace := *targetChunk.SpaceInfo.AllocatedDataSpace
	if targetChunk.SpaceInfo.PerfAllocatedDataSpace != nil {
		currentAllocatedSpace += *targetChunk.SpaceInfo.PerfAllocatedDataSpace
	}

	logger.Info("all allocated space", "AllocatedDataSpace", currentAllocatedSpace)

	estimatedChunkRemoveTime := int(float64(currentAllocatedSpace) / EstimatedChunkRemoveSpeed)
	logger.Info("estimated chunk remove time", "estimatedChunkRemoveTime", estimatedChunkRemoveTime)

	return (estimatedChunkRemoveTime/30 + 1) * 30, nil
}

func (h *RemoveHost) RemoveHostCostTimePreliminaryEvaluate(ctx context.Context, input RemoveHostWorkflowInput) (*RemoveProgressEvaluateInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCostTimePreliminaryEvaluate started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	masterCount := 0
	onlineMasterCount := 0
	targetHostOffline := false

	for _, host := range cluster.Hosts {
		hostRole, err := workflowutils.GetHostRole(input.ClusterIP, host.Local_id, host.Role)
		if err != nil {
			logger.Error("fail to get host role", "host", host.Name, "error", err)
			return nil, err
		}

		if hostRole == string(config.HostRoleMaster) {
			masterCount++
			if host.Merged_status != tower.HostMergedStatusDisconnected {
				onlineMasterCount++
			}
		}

		if host.Local_id == input.HostUUID && host.Merged_status == tower.HostMergedStatusDisconnected {
			targetHostOffline = true
		}
	}

	if cluster.Stretch {
		masterCount++
		onlineMasterCount++
	}

	skipStoragePoolRemoveNode := false
	if cluster.Type == tower.ClusterTypeSmtxElf || (targetHostOffline && masterCount == 2) {
		skipStoragePoolRemoveNode = true
	}

	startTime, err := h.getRemoveHostJobStartTime(ctx, input)
	if err != nil {
		logger.Error("fail to get job start time", "job_id", input.JobID)
		return nil, err
	}

	estimatedMetaRemoveTime := (onlineMasterCount*config.MetaRemoveNodePerNodeTimeMinute + config.MetaRemoveNodeBaseTimeMinute) * 60

	removeProgressEvaluateInput := &RemoveProgressEvaluateInput{
		StartTime:                 startTime,
		EstimatedMetaRemoveTime:   estimatedMetaRemoveTime,
		ProvideEstimatedTotalTime: !skipStoragePoolRemoveNode,
	}

	if !skipStoragePoolRemoveNode {
		estimatedChunkRemoveTime, err := h.stoPoolRemoveHostCostTimeEvaluate(ctx, input)
		if err != nil {
			logger.Error("fail to evaluate storage pool remove node time cost", "host", input.HostName)
			return nil, err
		}
		removeProgressEvaluateInput.EstimatedChunkRemoveTime = estimatedChunkRemoveTime
	}

	logger.Info("RemoveHostCostTimePreliminaryEvaluate finished", "removeProgressEvaluateInput", removeProgressEvaluateInput)

	return removeProgressEvaluateInput, nil
}

func (h *RemoveHost) generateRemoveHostProgress(ctx context.Context, progressEvaluateInput *RemoveProgressEvaluateInput) (float64, string) {
	logger := activity.GetLogger(ctx)
	logger.Info("generateRemoveHostProgress started.", "progressEvaluateInput", progressEvaluateInput)

	var progress float64

	estimatedTotalTime := ""
	currentTime := time.Now().UTC()
	duration := currentTime.Sub(progressEvaluateInput.StartTime)
	allUsedSec := int(duration.Seconds())

	switch progressEvaluateInput.Stage {
	case RemoveHostStageInit:
		progress = 0
		estimatedTotalTime = strconv.Itoa(allUsedSec + EstimatedInitTimeCostSec + progressEvaluateInput.EstimatedChunkRemoveTime + progressEvaluateInput.EstimatedMetaRemoveTime)
	case RemoveHostStageChunkRemove:
		totalTime := allUsedSec + progressEvaluateInput.EstimatedChunkRemoveTime + progressEvaluateInput.EstimatedMetaRemoveTime
		estimatedTotalTime = strconv.Itoa(totalTime)
		progress = float64(allUsedSec) / float64(totalTime)
	case RemoveHostStageMetaRemove:
		totalTime := allUsedSec + progressEvaluateInput.EstimatedMetaRemoveTime
		estimatedTotalTime = strconv.Itoa(totalTime)
		progress = float64(allUsedSec) / float64(totalTime)
	case RemoveHostStageFinish:
		estimatedTotalTime = strconv.Itoa(allUsedSec)
		progress = 1.0
	}
	logger.Info("generateRemoveHostProgress finished", "estimatedTotalTime", estimatedTotalTime, "progress", progress)

	return min(progress, 1.0), estimatedTotalTime
}

func (h *RemoveHost) UpdateRemoveHostProgress(ctx context.Context, input RemoveHostWorkflowInput, progressEvaluateInput *RemoveProgressEvaluateInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRemoveHostProgress started.", "stage", progressEvaluateInput.Stage)

	jobProgress, estimatedTotalTime := h.generateRemoveHostProgress(ctx, progressEvaluateInput)

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}

	progressStr := fmt.Sprintf("%.2f", jobProgress)
	jobProgressInput := &serverpb.JobProgress{
		Progress: progressStr,
	}

	if progressEvaluateInput.ProvideEstimatedTotalTime {
		jobProgressInput.TotalTime = estimatedTotalTime
	}

	if err := h.jobRepository.UpdateJobProgress(ctx, input.JobID, jobProgressInput); err != nil {
		logger.Error("fail to update job progress", "error", err)
		return err
	}

	err = h.baseHandler.UpdateTowerTaskProgress(ctx, input.JobID, towerTaskID, input.HostUUID, nil, jobProgress)
	if err != nil {
		logger.Error("failed to update tower task progress", "task_id", towerTaskID)
		return err
	}

	return nil
}

func (h *RemoveHost) UpdateRemoveHostJobStateWithRunning(ctx context.Context, input RemoveHostWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_RUNNING, nil)
}

func (h *RemoveHost) UpdateRemoveHostJobStateWithSuccess(ctx context.Context, input RemoveHostWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_SUCCESS, nil)
}

func (h *RemoveHost) UpdateRemoveHostJobStateWithFailed(ctx context.Context, input RemoveHostWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, nil)
}

func (h *RemoveHost) DeleteHostLabelRemoveHost(ctx context.Context, input RemoveHostWorkflowInput) error {
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerAction, "")
}

func (h *RemoveHost) UpdateHostLabelRemoveHostRunning(ctx context.Context, input RemoveHostWorkflowInput) error {
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerAction, config.HostRemoveRunning)
}

func (h *RemoveHost) UpdateHostLabelRemoveHostFailed(ctx context.Context, input RemoveHostWorkflowInput) error {
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerAction, config.HostRemoveFailed)
}

func (h *RemoveHost) RemoveHostVerifyHostPluginInstalledAndReady(ctx context.Context, input RemoveHostWorkflowInput) (string, error) {
	return h.baseHandler.VerifyHostPluginInstalledAndReady(ctx, input.ClusterUUID, input.JobID)
}

func (h *RemoveHost) RemoveHostCleanupHostPlugin(_ context.Context, hostPluginID string) error {
	return h.baseHandler.CleanupHostPlugin(hostPluginID)
}

func (h *RemoveHost) RemoveHostInstallHostPlugin(ctx context.Context, input RemoveHostWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostInstallHostPlugin started.")

	host, err := h.baseHandler.PickHostToInstallHostPlugin(ctx, input.ClusterUUID, input.HostUUID)
	if err != nil {
		return "", err
	}

	pkgID, err := h.baseHandler.UploadHostPluginPackage(ctx, input.ClusterUUID)
	if err != nil {
		return "", err
	}

	hpInput := h.baseHandler.PrepareInstallHostPluginInput(input.TowerClusterID, pkgID, host.Id)

	return h.baseHandler.InstallHostPlugin(ctx, host.Management_ip, input.JobID, hpInput)
}

type InfoForCmdInputs struct {
	ClusterType         tower.ClusterType
	AgentHostDataIP     string
	AgentHostMgmtIP     string
	TargetHostDataIP    string
	TargetHostMgmtIP    string
	TargetHostOffline   bool
	MasterCount         int
	OnlineHostsDataIPs  []string
	OfflineHostsDataIPs []string
}

func (h *RemoveHost) RemoveHostMigrateRecycleBinVMs(ctx context.Context, input RemoveHostWorkflowInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostMigrateRecycleBinVMs started.", "cluster", input.ClusterUUID)

	hostVMs, err := h.towerClient.GetHostVMsByHostLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host vms info", "error", err)
		return err
	}

	inRecycleBinVMs := make([]string, 0)

	for _, vm := range hostVMs.Vms {
		if vm.In_recycle_bin {
			logger.Info("get host in recycle bin vm.", "host", input.HostUUID, "vm_name", vm.Name, "vm_id", vm.Id)
			inRecycleBinVMs = append(inRecycleBinVMs, vm.Id)
		}
	}

	logger.Info("get host in recycle bin vms.", "host", input.HostUUID, "vms", inRecycleBinVMs)

	for _, vmID := range inRecycleBinVMs {
		logger.Info("migrate in recycle bin vm", "vm_id", vmID)

		err = h.towerClient.UpdateVMHost(ctx, vmID, "AUTO_SCHEDULE")
		if err != nil {
			logger.Error("fail to migrate in recycle bin vm", "host", input.HostUUID, "vm_id", vmID)
			return err
		}

		logger.Info("finish migrate in recycle bin vm", "vm_id", vmID)
	}

	return nil
}

func (h *RemoveHost) generateStoragePoolRemoveNodeCmd(ctx context.Context, targetActionData *config.VersionedAction, infoForCmdInputs InfoForCmdInputs) (*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("generateStoragePoolRemoveHostCmd started.")

	var agentCmd *config.AgentCmd

	agentCmd, err := h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameStoragePoolRemoveNode)
	if err != nil {
		return nil, errors.New("fail to get storage pool remove node command")
	}

	cmds := []string{agentCmd.Command, infoForCmdInputs.TargetHostDataIP}

	cmdQA := make([]*agentpb.QA, 0)
	for _, qa := range agentCmd.CmdQA {
		cmdQA = append(cmdQA, &agentpb.QA{
			Query:      qa.Query,
			Answer:     strings.ReplaceAll(qa.Answer, "{{management_ip}}", infoForCmdInputs.TargetHostMgmtIP),
			PassedMark: qa.PassedMark,
		})
	}

	agentInput := agentpb.TaskInput{
		Command:  strings.Join(cmds, " "),
		TargetIp: infoForCmdInputs.AgentHostDataIP,
		CmdQa:    cmdQA,
		Timeout:  int32(agentCmd.TimeoutFactor),
	}

	logger.Info("generate storage_pool_remove_node inputs", "input", agentInput.String())

	return &agentInput, nil
}

func (h *RemoveHost) generateMetaRemoveNodeCmd(ctx context.Context, targetActionData *config.VersionedAction, infoForCmdInputs InfoForCmdInputs, keepZbsMeta bool) (*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("generateMetaRemoveNodeCmd started.")

	var agentCmd *config.AgentCmd

	agentCmd, err := h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameMetaRemoveNode)
	if err != nil {
		return nil, errors.New("fail to get meta remove node command")
	}

	cmds := []string{agentCmd.Command}

	supportMultiOfflineHosts := h.checkSupportMultiOfflineHosts(targetActionData)
	if supportMultiOfflineHosts && len(infoForCmdInputs.OfflineHostsDataIPs) > 0 {
		cmds = append(cmds, "--offline_host_ips "+strings.Join(infoForCmdInputs.OfflineHostsDataIPs, ","))
	}

	if keepZbsMeta {
		cmds = append(cmds, "--keep_zbs_meta")
	}

	cmds = append(cmds, infoForCmdInputs.TargetHostDataIP)

	cmdQA := make([]*agentpb.QA, 0)
	for _, qa := range agentCmd.CmdQA {
		cmdQA = append(cmdQA, &agentpb.QA{
			Query:      qa.Query,
			Answer:     strings.ReplaceAll(qa.Answer, "{{management_ip}}", infoForCmdInputs.TargetHostMgmtIP),
			PassedMark: qa.PassedMark,
		})
	}

	baseTimeout := 180
	agentInput := agentpb.TaskInput{
		Command:  strings.Join(cmds, " "),
		TargetIp: infoForCmdInputs.AgentHostDataIP,
		CmdQa:    cmdQA,
		Timeout:  int32(agentCmd.TimeoutFactor*len(infoForCmdInputs.OnlineHostsDataIPs) + baseTimeout),
	}

	logger.Info("generate meta_remove_node inputs", "input", agentInput.String())

	return &agentInput, nil
}

func (h *RemoveHost) checkSupportMultiOfflineHosts(targetActionData *config.VersionedAction) bool {
	for _, feature := range targetActionData.Features {
		if feature == config.SupportMultiOfflineHosts {
			return true
		}
	}

	return false
}

func (h *RemoveHost) prepareInfoForRemoveHostCmdInputs(ctx context.Context, input RemoveHostWorkflowInput) (*InfoForCmdInputs, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("prepareInfoForRemoveHostCmdInputs started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(context.Background(), input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return nil, err
	}

	var targetHostOffline bool

	masterCount := 0
	infoForCmdInputs := &InfoForCmdInputs{AgentHostMgmtIP: agentAddr, ClusterType: cluster.Type}

	for _, host := range cluster.Hosts {
		if host.Management_ip == agentAddr {
			infoForCmdInputs.AgentHostDataIP = host.Data_ip
		}

		if host.Local_id == input.HostUUID {
			infoForCmdInputs.TargetHostDataIP = host.Data_ip
			infoForCmdInputs.TargetHostMgmtIP = host.Management_ip
		}

		hostRole, err := workflowutils.GetHostRole(input.ClusterIP, host.Local_id, host.Role)
		if err != nil {
			logger.Error("fail to get host role", "host", host.Name, "error", err)
			return nil, err
		}

		if hostRole == string(config.HostRoleMaster) {
			masterCount++
		}

		if host.Status == tower.HostStatusConnectedHealthy {
			infoForCmdInputs.OnlineHostsDataIPs = append(infoForCmdInputs.OnlineHostsDataIPs, host.Data_ip)
		} else {
			infoForCmdInputs.OfflineHostsDataIPs = append(infoForCmdInputs.OfflineHostsDataIPs, host.Data_ip)

			if host.Local_id == input.HostUUID {
				targetHostOffline = true
			}
		}
	}

	if infoForCmdInputs.AgentHostDataIP == "" {
		logger.Error("fail to get lcm manager agent host data ip")
		return nil, errors.New("fail to get lcm manager agent host data ip")
	} else if infoForCmdInputs.TargetHostDataIP == "" || infoForCmdInputs.TargetHostMgmtIP == "" {
		logger.Error("fail to get target host info", "host_uuid", input.HostUUID)
		return nil, fmt.Errorf("fail to get target host info, host_uuid=%s", input.HostUUID)
	}

	if cluster.Stretch {
		masterCount++
	}

	infoForCmdInputs.MasterCount = masterCount
	infoForCmdInputs.TargetHostOffline = targetHostOffline

	return infoForCmdInputs, nil
}

func (h *RemoveHost) RemoveHostGenerateLcmManagerAgentInputs(ctx context.Context, input RemoveHostWorkflowInput) ([]*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("GenerateLcmManagerAgentInputs for removing host started.")

	targetActionData, err := h.baseHandler.LoadTargetActionData(ctx, input.ClusterUUID, config.ActionRemoveHost)
	if err != nil {
		logger.Error("fail to load target action data", "error", err)
		return nil, err
	}

	agentInputs := make([]*agentpb.TaskInput, 0)

	var keepZbsMeta bool

	infoForCmdInputs, err := h.prepareInfoForRemoveHostCmdInputs(ctx, input)
	if err != nil {
		return nil, err
	}

	logger.Info("info for cmd inputs", "info", infoForCmdInputs)

	if infoForCmdInputs.ClusterType != tower.ClusterTypeSmtxElf {
		supportMultiOfflineHosts := h.checkSupportMultiOfflineHosts(targetActionData)
		// target host offline means target host not in OnlineHostsDataIPs,
		// skip storage_pool_remove_node if target host offline and other online hosts number is 2.
		if supportMultiOfflineHosts && infoForCmdInputs.TargetHostOffline && len(infoForCmdInputs.OnlineHostsDataIPs) == 2 {
			logger.Info("skip storage_pool_remove_node")

			keepZbsMeta = true
		} else {
			// generate storage_pool_remove_node inputs
			storagePoolRemoveNodeinput, err := h.generateStoragePoolRemoveNodeCmd(ctx, targetActionData, *infoForCmdInputs)
			if err != nil {
				return nil, err
			}

			agentInputs = append(agentInputs, storagePoolRemoveNodeinput)
		}
	}

	// generate meta_remove_node inputs
	metaRemoveNodeinput, err := h.generateMetaRemoveNodeCmd(ctx, targetActionData, *infoForCmdInputs, keepZbsMeta)
	if err != nil {
		return nil, err
	}

	agentInputs = append(agentInputs, metaRemoveNodeinput)

	return agentInputs, nil
}

func (h *RemoveHost) StoragePoolRemoveNode(ctx context.Context, input RemoveHostWorkflowInput, agentInput *agentpb.TaskInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("StoragePoolRemoveHost started.")

	return h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
}

func (h *RemoveHost) MetaRemoveNode(ctx context.Context, input RemoveHostWorkflowInput, agentInput *agentpb.TaskInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("MetaRemoveNode started.")

	return h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
}

func (h *RemoveHost) RemoveHostWaitingForActionDone(ctx context.Context, input RemoveHostWorkflowInput, agentInput *agentpb.TaskInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostWaitingForActionDone started.")

	return h.baseHandler.WaitingForActionDone(ctx, input.JobID, agentInput, taskID)
}

func (h *RemoveHost) RemoveHostGetTargetChunkUseState(ctx context.Context, input RemoveHostWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostCheckTargetChunkRemoving started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return "", err
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return "", err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return "", err
	}

	targetChunk, err := zbsClient.GetChunkByIP(ctx, targetHost.Data_ip)
	if err != nil && errors.Is(err, zbsclient.ErrChunkNotFound) {
		logger.Error("no chunk with target ip exist", "ip", targetHost.Data_ip)
		return ChunkStateNotFound, nil
	} else if err != nil {
		logger.Error("fail to get chunk", "chunk_ip", targetHost.Data_ip, "error", err)
		return "", err
	}

	if targetChunk.UseState == nil {
		logger.Error("can't get chunk use state", "ip", targetHost.Data_ip)
		return "", fmt.Errorf("can't get chunk %s use state", targetHost.Data_ip)
	}

	if *targetChunk.UseState == zbspb.ChunkState_CHUNK_STATE_UNKNOWN {
		logger.Error("chunk use state is unknown", "ip", targetHost.Data_ip)
		return "", fmt.Errorf("chunk %s use state is unknown", targetHost.Data_ip)
	}

	logger.Info("get chunk use state.", "ip", targetHost.Data_ip, "use_state", targetChunk.UseState.String())

	return targetChunk.UseState.String(), nil
}

func (h *RemoveHost) checkClusterChunkRemovingDone(ctx context.Context, zbsClient *zbsclient.ZbsClient, targetChunkIP string) (bool, *zbspb.Chunk, error) {
	logger := activity.GetLogger(ctx)

	targetChunk, err := zbsClient.GetChunkByIP(ctx, targetChunkIP)
	if err != nil && errors.Is(err, zbsclient.ErrChunkNotFound) {
		logger.Error("no chunk with target ip exist", "ip", targetChunkIP)
		return true, nil, nil
	} else if err != nil {
		logger.Error("fail to get chunk", "chunk_ip", targetChunkIP, "error", err)
		return false, nil, err
	}

	if targetChunk.UseState == nil {
		logger.Error("can't get chunk use state", "ip", targetChunkIP)
		return false, targetChunk, fmt.Errorf("can't get chunk %s use state", targetChunkIP)
	}

	if *targetChunk.UseState == zbspb.ChunkState_CHUNK_STATE_IDLE {
		logger.Info("chunk removed from storage pool", "ip", targetChunkIP, "use_state", targetChunk.UseState.String())
		return true, nil, nil
	}

	if *targetChunk.UseState != zbspb.ChunkState_CHUNK_STATE_REMOVING {
		logger.Error("chunk use state is not removing", "ip", targetChunkIP, "use_state", targetChunk.UseState.String())
		return false, targetChunk, temporal.NewNonRetryableApplicationError(fmt.Sprintf("chunk %s use state is not removing", targetChunkIP), "AgentTaskFailed", nil, nil)
	}

	logger.Info("chunk is removing", "ip", targetChunkIP)

	return false, targetChunk, nil
}

func (h *RemoveHost) InitStoragePoolRemoveNodeProgress(ctx context.Context, input RemoveHostWorkflowInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("InitStoragePoolRemoveNodeProgress started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return err
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("fail to get host info", "error", err)
		return err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("fail to create zbs client", "error", err)
		return err
	}

	targetChunk, err := zbsClient.GetChunkByIP(ctx, targetHost.Data_ip)
	if err != nil {
		logger.Error("fail to get target host chunk", "chunk_ip", targetHost.Data_ip, "error", err)
		return err
	}

	if targetChunk.SpaceInfo == nil || targetChunk.SpaceInfo.AllocatedDataSpace == nil {
		logger.Error("chunk space info is empty", "ip", targetHost.Data_ip)
		return fmt.Errorf("chunk with ip %s space info is empty", targetHost.Data_ip)
	}

	currentAllocatedSpace := *targetChunk.SpaceInfo.AllocatedDataSpace
	if targetChunk.SpaceInfo.PerfAllocatedDataSpace != nil {
		currentAllocatedSpace += *targetChunk.SpaceInfo.PerfAllocatedDataSpace
	}

	currentTime := time.Now().UTC()
	formattedTime := currentTime.Format(time.RFC3339)

	// FutureWork: use struct to define progress details
	jobProgress := &serverpb.JobProgress{
		Details: &serverpb.ProgressDetail{
			Name: "chunk_remove_status",
			Items: map[string]string{
				"origin_allocated_data_space":  strconv.FormatUint(currentAllocatedSpace, 10),
				"current_allocated_data_space": strconv.FormatUint(currentAllocatedSpace, 10),
				"start_time":                   formattedTime,
				"remaining_time":               "",
			},
		},
	}

	if err := h.jobRepository.UpdateJobProgress(ctx, input.JobID, jobProgress); err != nil {
		logger.Error("fail to update job progress", "error", err)
		return err
	}

	return nil
}

func (h *RemoveHost) getOriginAllocatedSpaceAndStartTime(ctx context.Context, jobID string) (uint64, time.Time, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("getOriginAllocatedSpaceAndStartTime started.")

	job, err := h.jobRepository.Get(ctx, jobID)
	if err != nil {
		logger.Error("failed to get job in db", "job_id", jobID)
		return 0, time.Time{}, err
	}

	originAllocatedSpaceStr := job.Progress.Details.Items["origin_allocated_data_space"]

	originAllocatedSpace, err := strconv.ParseUint(originAllocatedSpaceStr, 10, 64)
	if err != nil {
		logger.Error("fail to parse origin allocated space from string to uint64", err)
		return 0, time.Time{}, err
	}

	startTimeStr := job.Progress.Details.Items["start_time"]

	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		logger.Error("fail to parse start time", "error", err)
		return 0, time.Time{}, err
	}

	logger.Info("get origin allocated space and start time", "space", originAllocatedSpace, "start_time", startTimeStr)

	return originAllocatedSpace, startTime, nil
}

func (h *RemoveHost) removeHostUpdateProgress(ctx context.Context, jobID string, hostUUID string, targetChunk *zbspb.Chunk, targetChunkIP string, originAllocatedSpace uint64, startTime time.Time, towerTaskID string, progressEvaluateInput *RemoveProgressEvaluateInput) error { //nolint: funlen
	logger := activity.GetLogger(ctx)
	logger.Info("removeHostUpdateProgress started.")

	currentTime := time.Now().UTC()
	chunkRemoveDuration := currentTime.Sub(startTime)

	allDuration := currentTime.Sub(progressEvaluateInput.StartTime)
	allUsedSec := int(allDuration.Seconds())

	if targetChunk == nil || *targetChunk.UseState == zbspb.ChunkState_CHUNK_STATE_IDLE {
		logger.Error("chunk with target ip removed from storage pool", "ip", targetChunkIP)

		estimatedTotalTime := allUsedSec + progressEvaluateInput.EstimatedMetaRemoveTime
		progress := float64(allUsedSec) / float64(estimatedTotalTime)

		jobProgress := &serverpb.JobProgress{
			Progress:  fmt.Sprintf("%.2f", progress),
			TotalTime: strconv.Itoa(estimatedTotalTime),
			Details: &serverpb.ProgressDetail{
				Items: map[string]string{
					"current_allocated_data_space": "0",
					"remaining_time":               "0",
				},
			},
		}

		if err := h.jobRepository.UpdateJobProgress(ctx, jobID, jobProgress); err != nil {
			logger.Error("fail to update job progress", "error", err)
			return err
		}

		if err := h.baseHandler.UpdateTowerTaskProgress(ctx, jobID, towerTaskID, hostUUID, nil, progress); err != nil {
			logger.Error("failed to update tower task progress", "task_id", towerTaskID)
			return err
		}

		return nil
	}

	if targetChunk.SpaceInfo == nil || targetChunk.SpaceInfo.AllocatedDataSpace == nil {
		logger.Error("chunk space info is empty", "chunk_id", *targetChunk.Id)
		return errors.New("chunk space info is empty")
	}

	currentAllocatedSpace := *targetChunk.SpaceInfo.AllocatedDataSpace
	logger.Info("get current allocated data space", "AllocatedDataSpace", currentAllocatedSpace)

	if targetChunk.SpaceInfo.PerfAllocatedDataSpace != nil {
		currentAllocatedSpace += *targetChunk.SpaceInfo.PerfAllocatedDataSpace
		logger.Info("get current perf allocated data space", "PerfAllocatedDataSpace", *targetChunk.SpaceInfo.PerfAllocatedDataSpace)
	}

	deltaSpace := originAllocatedSpace - currentAllocatedSpace
	chunkRemoveRemainingTimeStr := ""
	expectedTotalTimeStr := ""

	var progress float64

	if deltaSpace > 0 {
		remainingTime := float64(currentAllocatedSpace) / float64(deltaSpace) * chunkRemoveDuration.Seconds()
		chunkRemoveRemainingTimeStr = strconv.Itoa(int(remainingTime))
		expectedTotalTime := allUsedSec + int(remainingTime) + progressEvaluateInput.EstimatedMetaRemoveTime

		progress = float64(allUsedSec) / float64(expectedTotalTime)
		expectedTotalTimeStr = strconv.Itoa(expectedTotalTime)
	} else {
		expectedTotalTime := allUsedSec + progressEvaluateInput.EstimatedChunkRemoveTime + progressEvaluateInput.EstimatedMetaRemoveTime

		progress = float64(allUsedSec) / float64(expectedTotalTime)
		expectedTotalTimeStr = strconv.Itoa(expectedTotalTime)
	}

	jobProgress := &serverpb.JobProgress{
		Progress:  fmt.Sprintf("%.2f", progress),
		TotalTime: expectedTotalTimeStr,
		Details: &serverpb.ProgressDetail{
			Items: map[string]string{
				"current_allocated_data_space": strconv.FormatUint(currentAllocatedSpace, 10),
				"remaining_time":               chunkRemoveRemainingTimeStr,
			},
		},
	}

	if err := h.jobRepository.UpdateJobProgress(ctx, jobID, jobProgress); err != nil {
		logger.Error("fail to update job progress", "error", err)
		return err
	}

	if err := h.baseHandler.UpdateTowerTaskProgress(ctx, jobID, towerTaskID, hostUUID, nil, progress); err != nil {
		logger.Error("failed to update tower task progress", "task_id", towerTaskID)
		return err
	}

	return nil
}

func (h *RemoveHost) RemoveHostWaitingForChunkRemoved(ctx context.Context, input RemoveHostWorkflowInput, progressEvaluateInput *RemoveProgressEvaluateInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostWaitingForChunkRemoved started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return err
	}

	originAllocatedSpace, startTime, err := h.getOriginAllocatedSpaceAndStartTime(ctx, input.JobID)
	if err != nil {
		logger.Error("fail to get origin allocated space and start time from db", "error", err)
		return err
	}

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}

	timeout := time.After(time.Duration(36000) * time.Second)

	for {
		select {
		case <-timeout:
			logger.Error("waiting for chunk remove timeout", "ip", targetHost.Data_ip)
			return fmt.Errorf("waiting for chunk remove timeout, ip=%s", targetHost.Data_ip)

		default:
			time.Sleep(30 * time.Second)

			done, targetChunk, err := h.checkClusterChunkRemovingDone(ctx, zbsClient, targetHost.Data_ip)
			if err != nil {
				logger.Error(fmt.Sprintf("checkClusterChunkRemovingDone failed, error: %v, error_type: %T", err, err))

				if errors.Is(err, rpc.ErrShutdown) || strings.Contains(err.Error(), "connection is shut down") {
					zbsClient, err = workflowutils.InitZbsClient(cluster)
					if err != nil {
						logger.Error("failed to create zbs client", "error", err)
						return err
					}

					continue
				}

				return err
			}

			if err := h.removeHostUpdateProgress(ctx, input.JobID, input.HostUUID, targetChunk, targetHost.Data_ip, originAllocatedSpace, startTime, towerTaskID, progressEvaluateInput); err != nil {
				logger.Error("fail to update remove host progress, skip update it this time.", "error", err)
			}

			if done {
				return nil
			}
		}
	}
}

func (h *RemoveHost) metaRemoveHostUpdateProgress(ctx context.Context, jobID string, hostUUID string, towerTaskID string, progressEvaluateInput *RemoveProgressEvaluateInput, estimatedTotalTime int) error {
	logger := activity.GetLogger(ctx)
	logger.Info("metaRemoveHostUpdateProgress started.")

	currentTime := time.Now().UTC()
	allDuration := currentTime.Sub(progressEvaluateInput.StartTime)
	allUsedSec := int(allDuration.Seconds())

	newEstimatedTotalTime := estimatedTotalTime
	if estimatedTotalTime-allUsedSec < 30 {
		newEstimatedTotalTime = allUsedSec + 40
	}

	progress := float64(allUsedSec) / float64(newEstimatedTotalTime)

	jobProgress := &serverpb.JobProgress{
		Progress: fmt.Sprintf("%.2f", progress),
	}

	if progressEvaluateInput.ProvideEstimatedTotalTime {
		jobProgress.TotalTime = strconv.Itoa(newEstimatedTotalTime)
	}

	if err := h.jobRepository.UpdateJobProgress(ctx, jobID, jobProgress); err != nil {
		logger.Error("fail to update job progress", "error", err)
		return err
	}

	if err := h.baseHandler.UpdateTowerTaskProgress(ctx, jobID, towerTaskID, hostUUID, nil, progress); err != nil {
		logger.Error("failed to update tower task progress", "task_id", towerTaskID)
		return err
	}

	logger.Info("metaRemoveHostUpdateProgress finished.", "job", jobID, "progress", progress)
	return nil
}

func (h *RemoveHost) RemoveHostWaitingForMetaRemoved(ctx context.Context, input RemoveHostWorkflowInput, agentInput *agentpb.TaskInput, taskID string, progressEvaluateInput *RemoveProgressEvaluateInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RemoveHostWaitingForMetaRemoved started.")

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return err
	}

	job, err := h.jobRepository.Get(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get job in db", "job_id", input.JobID)
		return err
	}

	var estimatedTotalTime int
	if job.Progress.TotalTime != "" {
		estimatedTotalTime, err = strconv.Atoi(job.Progress.TotalTime)
		if err != nil {
			logger.Error("fail to convert string to int", "string", job.Progress.TotalTime)
			return err
		}
	} else {
		currentTime := time.Now().UTC()
		allDuration := currentTime.Sub(progressEvaluateInput.StartTime)
		allUsedSec := int(allDuration.Seconds())

		estimatedTotalTime = allUsedSec + progressEvaluateInput.EstimatedMetaRemoveTime
	}

	timeout := time.After(time.Duration(agentInput.Timeout) * time.Second)

	agentClient := agentclient.NewTaskManagerClient(agentAddr)
	taskInput := &agentpb.GetTaskRequest{TaskId: taskID}

	for {
		select {
		case <-timeout:
			slog.Error("Timeout reached waiting for task completion")
			return fmt.Errorf("timeout waiting for task %s completion", taskID)

		default:
			time.Sleep(30 * time.Second)

			done, err := h.baseHandler.CheckAgentTaskStatus(ctx, agentClient, taskInput)
			if err != nil {
				return err
			}

			if done {
				return nil
			}

			if err := h.metaRemoveHostUpdateProgress(ctx, input.JobID, input.HostUUID, towerTaskID, progressEvaluateInput, estimatedTotalTime); err != nil {
				logger.Error("fail to update remove host progress, skip update it this time.", "error", err)
			}
		}
	}
}

func (h *RemoveHost) SyncClusterAfterRemoveHost(ctx context.Context, input RemoveHostWorkflowInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("SyncClusterAfterRemoveHost started.")

	err := h.towerClient.UpdateClusterByAdminAPI(ctx, input.TowerClusterID)
	if err != nil {
		logger.Error("fail to sync cluster info after host removed", "error", err)
		return temporal.NewNonRetryableApplicationError("fail to sync cluster info after host removed", "PostRemoveHost", nil, nil)
	}

	logger.Info("SyncClusterAfterRemoveHost finished.")

	return nil
}

func (h *RemoveHost) StoreRemoveHostJobLogs(ctx context.Context, input RemoveHostWorkflowInput, agentTaskIDs []string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("StoreJobLogs started.", input)

	return h.baseHandler.StoreJobLogs(ctx, input.JobID, agentTaskIDs)
}
