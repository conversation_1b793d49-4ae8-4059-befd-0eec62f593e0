package removehost

import (
	"errors"
	"time"

	"github.com/gofrs/uuid"
	"go.temporal.io/sdk/workflow"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
)

type RemoveHostCheckWorkflowInput struct {
	Header         workflowutils.CustomedHeader `json:"header"`
	JobID          string                       `json:"job_id"`
	TowerClusterID string                       `json:"tower_cluster_id"`
	ClusterUUID    string                       `json:"cluster_uuid"`
	ClusterIP      string                       `json:"cluster_vip"`
	HostName       string                       `json:"host_name"`
	HostUUID       string                       `json:"host_uuid"`
	IsVmware       bool                         `json:"is_vmware"`
}

func RemoveHostCheckWorkflow(ctx workflow.Context, input RemoveHostCheckWorkflowInput) error { //nolint: funlen
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNamePrecheckDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	logger := workflow.GetLogger(ctx)
	logger.Info("RemoveHostCheckWorkflow workflow started", "JobID", input.JobID)

	var h *RemoveHostCheck
	var towerTaskID string

	defer func() {
		if err != nil {
			if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostCheckJobStateWithFailed, input).Get(ctx, nil); e != nil {
				logger.Error("fail to update role convert check job state with failed", "err", e)
			}

			if towerTaskID != "" {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostCheckTowerTaskWithFailed, input, towerTaskID).Get(ctx, nil); e != nil {
					logger.Error("fail to update role convert check tower task with failed", "err", e)
				}
			}
		}
	}()

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.CreateRemoveHostCheckTowerTask, input).Get(ctx, &towerTaskID); err != nil {
		logger.Error("failed update job state")
		return err
	}

	selector := workflow.NewSelector(ctx)

	activities, err := h.GetRemoveHostActivities(input)
	if err != nil {
		logger.Error("get activity failed.", "Error", err)
		return workflow.NewContinueAsNewError(ctx, err, "failed to get activities")
	}

	cids := make([]string, len(activities))
	for i := 0; i < len(activities); i++ {
		_ = workflow.SideEffect(ctx, func(_ workflow.Context) interface{} {
			tmpID, _ := uuid.NewV7()
			return tmpID.String()
		}).Get(&cids[i])
	}

	for i, activity := range activities {
		// init check result
		future := workflow.ExecuteActivity(ctx, activity, cids[i], input, true)

		selector.AddFuture(future, func(f workflow.Future) {
			err1 := f.Get(ctx, nil)
			if err1 != nil {
				logger.Error("Activity failed.", "Error", err1)
				err = err1
				return
			}

			logger.Info("Activity completed.", "check_id", cids[i])
		})
	}

	for i := 0; i < len(activities); i++ {
		selector.Select(ctx)
		if err != nil {
			logger.Error("Activity failed.", "Error", err)
			return err
		}
	}

	logger.Info("check result init completed.")

	if err := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostCheckJobStateWithRunning, input).Get(ctx, nil); err != nil {
		logger.Error("failed update job state")
		return err
	}

	// do check
	precheckSelector := workflow.NewSelector(ctx)
	for i, activity := range activities {
		future := workflow.ExecuteActivity(ctx, activity, cids[i], input, false)

		precheckSelector.AddFuture(future, func(f workflow.Future) {
			err1 := f.Get(ctx, nil)
			if err1 != nil {
				logger.Error("Activity failed.", "Error", err1)
				err = err1
				return
			}

			logger.Info("Activity completed.", "check_id", cids[i])
		})
	}

	for i := 0; i < len(activities); i++ {
		precheckSelector.Select(ctx)
		if err != nil {
			logger.Error("Activity failed.", "Error", err)
			return err
		}
	}

	if err := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostCheckJobStateWithSuccess, input).Get(ctx, nil); err != nil {
		logger.Error("failed update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostCheckTowerTaskWithSuccess, input, towerTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to update remove host check tower task with success")
		return err
	}

	logger.Info("RemoveHostCheckWorkflow completed.")

	return nil
}

type RemoveHostWorkflowInput struct {
	Header         workflowutils.CustomedHeader `json:"header"`
	JobID          string                       `json:"job_id"`
	TowerClusterID string                       `json:"tower_cluster_id"`
	ClusterUUID    string                       `json:"cluster_uuid"`
	ClusterIP      string                       `json:"cluster_vip"`
	HostName       string                       `json:"host_name"`
	HostUUID       string                       `json:"host_uuid"`
	SkipPrecheck   bool                         `json:"skip_precheck"`
	IsVmware       bool                         `json:"is_vmware"`
}

type RemoveProgressEvaluateInput struct {
	StartTime                 time.Time       `json:"start_time"`
	EstimatedChunkRemoveTime  int             `json:"estimated_chunk_remove_time"` // seconds
	EstimatedMetaRemoveTime   int             `json:"estimated_meta_remove_time"`  // seconds
	ProvideEstimatedTotalTime bool            `json:"provide_estimated_total_time"`
	Stage                     RemoveHostStage `json:"stage"`
}

func ChildRemoveHostCheckWorkflow(ctx workflow.Context, input RemoveHostWorkflowInput, cwJobID string) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("RemoveHostCheck child workflow started", "JobID", input.JobID)

	cwo := workflow.ChildWorkflowOptions{
		WorkflowID: "remove-host-2nd-check-" + cwJobID,
	}

	ctx = workflow.WithChildOptions(ctx, cwo)

	cwInput := RemoveHostCheckWorkflowInput{
		Header:         input.Header,
		JobID:          cwJobID,
		TowerClusterID: input.TowerClusterID,
		ClusterUUID:    input.ClusterUUID,
		ClusterIP:      input.ClusterIP,
		HostName:       input.HostName,
		HostUUID:       input.HostUUID,
		IsVmware:       input.IsVmware,
	}

	err := workflow.ExecuteChildWorkflow(ctx, RemoveHostCheckWorkflow, cwInput).Get(ctx, nil)
	if err != nil {
		logger.Error("Received child remove host check workflow failure.", "Error", err)
		return err
	}

	logger.Info("Child remove host check workflow completed.")

	return nil
}

func RemoveHostWorkflow(ctx workflow.Context, input RemoveHostWorkflowInput) error { //nolint: funlen,gocyclo
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	ctxActionWait, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameWaitingForRemoveHostDone)
	if err != nil {
		return err
	}

	ctxRemoveChunkWait, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameWaitingForRemoveChunkDone)
	if err != nil {
		return err
	}

	logger := workflow.GetLogger(ctx)
	logger.Info("RemoveHost workflow started", "JobID", input.JobID)

	var (
		h                           *RemoveHost
		realActionStarted           bool
		towerTaskID                 string
		hpID                        string
		hostRemoved                 bool
		removeProgressEvaluateInput *RemoveProgressEvaluateInput
	)

	// lcm-manager-agent task ids, used for collecting logs from lcm-manager-agent
	agentTaskIDs := make([]string, 0)

	defer func() {
		if errors.Is(ctx.Err(), workflow.ErrCanceled) {
			// When the Workflow is canceled, it has to get a new disconnected context to execute any Activities
			ctx, _ = workflow.NewDisconnectedContext(ctx)
		}

		if realActionStarted {
			if e := workflow.ExecuteActivity(ctx, h.StoreRemoveHostJobLogs, input, agentTaskIDs).Get(ctx, nil); e != nil {
				logger.Error("fail to store lcm manager agent logs", "err", e)
			}
		}

		if hpID != "" {
			if e := workflow.ExecuteActivity(ctx, h.RemoveHostCleanupHostPlugin, hpID).Get(ctx, nil); e != nil {
				logger.Error("fail to cleanup host plugin", "err", e)
			}
		}

		if err != nil {
			// input.SkipPrecheck == true means that the action is retry action, host label should mark as failed if job runs failed.
			if (input.SkipPrecheck || realActionStarted) && !hostRemoved {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateHostLabelRemoveHostFailed, input).Get(ctx, nil); e != nil {
					logger.Error("fail to update lcm manager action label to failed", "err", e)
				}
			} else if !hostRemoved {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.DeleteHostLabelRemoveHost, input).Get(ctx, nil); e != nil {
					logger.Error("fail to delete lcm manager action label", "err", e)
				}
			}

			if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostJobStateWithFailed, input).Get(ctx, nil); e != nil {
				logger.Error("fail to update job state with failed", "err", e)
			}

			if e := workflow.ExecuteActivity(ctx, h.SyncClusterAfterRemoveHost, input).Get(ctx, nil); e != nil {
				logger.Warn("fail to sync cluster after remove host", "err", e)
			}

			if towerTaskID != "" {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostTowerTaskWithFailed, input, towerTaskID).Get(ctx, nil); e != nil {
					logger.Error("fail to update remove host tower task")
				}
			}
		}
	}()

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostJobStateWithRunning, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.CreateRemoveHostTowerTask, input).Get(ctx, &towerTaskID); err != nil {
		logger.Error("fail to create remove host tower task")
		return err
	}

	if !input.SkipPrecheck {
		var cwJobID string
		if err = workflow.ExecuteActivity(ctx, h.ChildRemoveHostCheckWorkflowPreparation, input).Get(ctx, &cwJobID); err != nil {
			logger.Error("ChildRemoveHostCheckWorkflowPreparation failed", "error", err)
			return err
		}

		err = ChildRemoveHostCheckWorkflow(ctx, input, cwJobID)
		if err != nil {
			logger.Error("ChildRemoveHostCheckWorkflow run failed")
			return err
		}

		if err = workflow.ExecuteActivity(ctx, h.ValidateRemoveHost2ndCheckResult, input, cwJobID).Get(ctx, nil); err != nil {
			logger.Error("Validate ValidateRemoveHost2ndCheckResult failed", "error", err)
			return err
		}

		logger.Info("Second time remove host check finished.")
	}

	if err = workflow.ExecuteActivity(ctx, h.RemoveHostCostTimePreliminaryEvaluate, input).Get(ctx, &removeProgressEvaluateInput); err != nil {
		logger.Error("RemoveHostCostTimePreliminaryEvaluate run failed.", "error", err)
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateHostLabelRemoveHostRunning, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update host label role convert running")
		return err
	}

	removeProgressEvaluateInput.Stage = RemoveHostStageInit
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostProgress, input, removeProgressEvaluateInput).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}

	if e := workflow.ExecuteActivity(ctx, h.SyncClusterAfterRemoveHost, input).Get(ctx, nil); e != nil {
		logger.Warn("fail to sync cluster after removing host started", "err", e)
	}

	if !input.IsVmware {
		if err = workflow.ExecuteActivity(ctxStateUpdate, h.RemoveHostMigrateRecycleBinVMs, input).Get(ctx, nil); err != nil {
			logger.Error("fail to migrate host in recycle bin vms", "error", err)
			return err
		}
	}

	if err = workflow.ExecuteActivity(ctx, h.RemoveHostVerifyHostPluginInstalledAndReady, input).Get(ctx, &hpID); err != nil {
		logger.Error("fail to verify host plugin installed and ready")
		return err
	}

	if hpID == "" {
		if err = workflow.ExecuteActivity(ctx, h.RemoveHostInstallHostPlugin, input).Get(ctx, &hpID); err != nil {
			logger.Error("fail to install host plugin")
			return err
		}
	}

	var agentInputs []*agentpb.TaskInput
	if err = workflow.ExecuteActivity(ctx, h.RemoveHostGenerateLcmManagerAgentInputs, input).Get(ctx, &agentInputs); err != nil {
		logger.Error("fail to generate lcm manager agent inputs")
		return err
	}

	if len(agentInputs) == 0 || len(agentInputs) > 2 {
		err = errors.New("fail to generate lcm manager agent inputs")
		return err
	}

	var (
		storagePoolRemoveNodeInput *agentpb.TaskInput
		metaRemoveNodeInput        *agentpb.TaskInput
	)

	if len(agentInputs) == 2 {
		storagePoolRemoveNodeInput = agentInputs[0]
		metaRemoveNodeInput = agentInputs[1]
	} else if len(agentInputs) == 1 {
		metaRemoveNodeInput = agentInputs[0]
	}

	var agentTaskID string

	realActionStarted = true

	if storagePoolRemoveNodeInput != nil {
		removeProgressEvaluateInput.Stage = RemoveHostStageChunkRemove
		if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostProgress, input, removeProgressEvaluateInput).Get(ctx, nil); err != nil {
			logger.Error("fail to update job progress")
			return err
		}

		var chunkUseState string
		if err = workflow.ExecuteActivity(ctx, h.RemoveHostGetTargetChunkUseState, input).Get(ctx, &chunkUseState); err != nil {
			logger.Error("fail to get chunk in use state", "err", err)
			return err
		}

		if chunkUseState == ChunkStateRemoving || chunkUseState == ChunkStateInUse {
			if err = workflow.ExecuteActivity(ctx, h.InitStoragePoolRemoveNodeProgress, input).Get(ctx, nil); err != nil {
				logger.Error("fail to init storage_pool_remove_node progress", "err", err)
				return err
			}
		}

		if chunkUseState == ChunkStateInUse {
			if err = workflow.ExecuteActivity(ctx, h.StoragePoolRemoveNode, input, storagePoolRemoveNodeInput).Get(ctx, &agentTaskID); err != nil {
				logger.Error("fail to trigger storage_pool_remove_node cmd", "err", err)
				return err
			}

			agentTaskIDs = append(agentTaskIDs, agentTaskID)

			if err = workflow.ExecuteActivity(ctxActionWait, h.RemoveHostWaitingForActionDone, input, storagePoolRemoveNodeInput, agentTaskID).Get(ctx, nil); err != nil {
				logger.Error("fail to check storage pool remove node result", "err", err)
				return err
			}
		}

		if chunkUseState == ChunkStateRemoving || chunkUseState == ChunkStateInUse {
			if err = workflow.ExecuteActivity(ctxRemoveChunkWait, h.RemoveHostWaitingForChunkRemoved, input, removeProgressEvaluateInput).Get(ctx, nil); err != nil {
				logger.Error("fail to check chunk remove result", "err", err)
				return err
			}
		}
	}

	removeProgressEvaluateInput.Stage = RemoveHostStageMetaRemove
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostProgress, input, removeProgressEvaluateInput).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}

	if err = workflow.ExecuteActivity(ctx, h.MetaRemoveNode, input, metaRemoveNodeInput).Get(ctx, &agentTaskID); err != nil {
		logger.Error("fail to trigger meta_remove_node cmd", "err", err)
		return err
	}

	agentTaskIDs = append(agentTaskIDs, agentTaskID)

	if err = workflow.ExecuteActivity(ctxActionWait, h.RemoveHostWaitingForMetaRemoved, input, metaRemoveNodeInput, agentTaskID, removeProgressEvaluateInput).Get(ctx, nil); err != nil {
		logger.Error("fail to check meta remove node result", "err", err)
		return err
	}

	hostRemoved = true

	removeProgressEvaluateInput.Stage = RemoveHostStageFinish
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostProgress, input, removeProgressEvaluateInput).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostJobStateWithSuccess, input).Get(ctx, nil); err != nil {
		logger.Error("failed update job state")
		return err
	}

	if e := workflow.ExecuteActivity(ctx, h.SyncClusterAfterRemoveHost, input).Get(ctx, nil); e != nil {
		logger.Warn("fail to sync cluster after remove host", "err", e)
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRemoveHostTowerTaskWithSuccess, input, towerTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to update remove host tower task")
		return err
	}

	return nil
}
