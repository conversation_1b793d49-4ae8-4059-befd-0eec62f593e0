package rdmatoggle

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/eduardolat/goeasyi18n"
	"go.temporal.io/sdk/activity"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	agentclient "github.smartx.com/LCM/lcm-manager/pkg/client/agent"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	tower "github.smartx.com/LCM/lcm-manager/third_party/tower"
)

func (h *RdmaToggleActivities) SaveWorkflowProgress(ctx context.Context, jobID string, progress *workflowutils.WorkflowProgress) error {
	progressBytes, err := json.Marshal(progress)
	if err != nil {
		return fmt.Errorf("failed to marshal workflow progress: %w", err)
	}

	job, err := h.jobRepository.Get(ctx, jobID)
	if err != nil {
		return fmt.Errorf("failed to get job details: %w", err)
	}
	details := (*job).Details

	details["workflow_progress"] = string(progressBytes)

	if err := h.jobRepository.UpdateJobDetail(ctx, jobID, details); err != nil {
		return fmt.Errorf("failed to save workflow progress to db: %w", err)
	}

	return nil
}

func (h *RdmaToggleActivities) LoadWorkflowProgress(ctx context.Context, jobID string) (*workflowutils.WorkflowProgress, error) {
	job, err := h.jobRepository.Get(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to get job details: %w", err)
	}
	var progress workflowutils.WorkflowProgress
	progressStr, exist := (*job).Details["workflow_progress"]
	if !exist {
		return nil, fmt.Errorf("workflow_progress not found in job details")
	}
	if err := json.Unmarshal([]byte(progressStr), &progress); err != nil {
		return nil, fmt.Errorf("failed to unmarshal workflow progress: %w", err)
	}

	return &progress, nil
}

type RdmaToggleActivities struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	i18n                  *goeasyi18n.I18n
}

func NewRdmaToggleActivities(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	hpClient hpoperator.Client,
	i18n *goeasyi18n.I18n,
) *RdmaToggleActivities {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, hpClient, i18n)

	return &RdmaToggleActivities{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		i18n:                  i18n,
	}
}

// --- job control ---
func (h *RdmaToggleActivities) RdmaToggleUpdateJobState(ctx context.Context, input RdmaToggleWorkflowInput, state serverpb.JobState) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, state, nil)
}

// --- tower job control ----
func (h *RdmaToggleActivities) RdmaToggleCreateTowerTask(ctx context.Context, input RdmaToggleWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleCreateTowerTask started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
		},
	}

	taskArgs := map[string]string{
		"job_id":    input.JobID,
		"host_uuid": input.HostUUID,
		"state":     "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, input.TowerTaskCfgName, options, options, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *RdmaToggleActivities) RdmaToggleUpdateTowerTask(ctx context.Context, input RdmaToggleWorkflowInput, taskID string, status tower.TaskStatus, wfprogress *workflowutils.WorkflowProgress) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggle UpdateTowerTask started.", "status", status)

	taskInput := &tower.UpdateTaskInput{
		Status: status,
	}
	taskArgs := map[string]string{
		"job_id":    input.JobID,
		"host_uuid": input.HostUUID,
	}

	if wfprogress != nil {
		progressBytes, err := json.Marshal(wfprogress)
		if err != nil {
			return fmt.Errorf("failed to marshal workflow progress: %w", err)
		}
		taskArgs["detailed_progress"] = string(progressBytes)
	}
	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
		},
	}

	switch status {
	case tower.TaskStatusExecuting:
		taskInput.Done = false
		taskInput.Args = taskArgs
		if err := h.baseHandler.UpdateTowerTask(ctx, taskID, taskInput); err != nil {
			logger.Error("UpdateTowerTask failed.", err)
			return err
		}

	case tower.TaskStatusSuccessed:
		taskInput.Progress = 1.0
		taskInput.Done = true
		taskInput.Args = taskArgs
		auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, input.TowerTaskCfgName, options, options, input.IsVmware)

		if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
			logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
			return err
		}

	case tower.TaskStatusFailed:
		taskInput.Done = true
		taskInput.Args = taskArgs
		auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, input.TowerTaskCfgName, options, options, input.IsVmware)

		if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
			logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
			return err
		}
	}

	return nil
}

func (h *RdmaToggleActivities) RdmaToggleStoreJobLogs(ctx context.Context, input RdmaToggleWorkflowInput, agentTaskIDs []string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleStoreJobLogs started.", input)

	return h.baseHandler.StoreJobLogs(ctx, input.JobID, agentTaskIDs)
}

func (h *RdmaToggleActivities) RdmaToggleCleanupHostPlugin(_ context.Context, hostPluginID string) error {
	return h.baseHandler.CleanupHostPlugin(hostPluginID)
}

func (h *RdmaToggleActivities) RdmaToggleVerifyHostPluginInstalledAndReady(ctx context.Context, input RdmaToggleWorkflowInput) (string, error) {
	return h.baseHandler.VerifyHostPluginInstalledAndReady(ctx, input.ClusterUUID, input.JobID)
}

func (h *RdmaToggleActivities) RdmaToggleInstallHostPlugin(ctx context.Context, input RdmaToggleWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleInstallHostPlugin started.")

	host, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		return "", err
	}

	pkgID, err := h.baseHandler.UploadHostPluginPackage(ctx, input.ClusterUUID)
	if err != nil {
		return "", err
	}

	hpInput := h.baseHandler.PrepareInstallHostPluginInput(input.TowerClusterID, pkgID, host.Id)

	return h.baseHandler.InstallHostPlugin(ctx, host.Management_ip, input.JobID, hpInput)
}

func (h *RdmaToggleActivities) RdmaToggleGetClusterIPs(ctx context.Context, input RdmaToggleWorkflowInput) ([]string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleGetClusterIPs started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	var ipList []string
	for _, host := range cluster.Hosts {
		ipList = append(ipList, host.Management_ip)
	}

	logger.Info("get cluster IPs result: ", ipList)
	return ipList, nil
}

// Step 1: Pre-check all nodes
func (h *RdmaToggleActivities) RdmaToggleRunPreCheckOnHost(ctx context.Context, input RdmaToggleWorkflowInput, targetHostMgtIp string) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleRunPreCheckOnHost started.")

	targetActionData, err := h.baseHandler.LoadTargetActionData(ctx, input.ClusterUUID, config.ActionRDMAToggle)
	if err != nil {
		logger.Error("fail to load target action data", "error", err)
		return "", err
	}

	var agentCmd *config.AgentCmd
	switch input.TargetState {
	case serverpb.RDMAState_RDMA_ON:
		agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleEnablePrecheck)
	case serverpb.RDMAState_RDMA_OFF:
		agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleDisablePrecheck)
	}
	if err != nil {
		return "", errors.New("fail to get RDMA Toggle command")
	}

	cmds := []string{agentCmd.Command}

	agentInput := agentpb.TaskInput{
		Command:  strings.Join(cmds, " "),
		TargetIp: targetHostMgtIp,
		CmdQa:    nil,
		Timeout:  int32(agentCmd.TimeoutFactor),
	}

	maxTimeoutInSec := int32(0)
	taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, &agentInput)
	if err != nil {
		return "", err
	}
	if agentInput.GetTimeout() > maxTimeoutInSec {
		maxTimeoutInSec = agentInput.GetTimeout()
	}

	err = h.RdmaToggleWaitingForAgentTasks(ctx, input, []string{taskID}, maxTimeoutInSec)
	if err != nil {
		return "", err
	}

	logger.Info("RdmaToggleRunPreCheckOnHost On Host " + targetHostMgtIp + " done.")
	return taskID, err
}

// Step 2: Configure all nodes
func (h *RdmaToggleActivities) RdmaToggleRunChangeConfigOnHost(ctx context.Context, input RdmaToggleWorkflowInput, targetHostMgtIp string) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleRunChangeConfigOnHost started.")

	var agentCmd *config.AgentCmd
	var err error
	var targetActionData *config.VersionedAction

	targetActionData, err = h.baseHandler.LoadTargetActionData(ctx, input.ClusterUUID, config.ActionRDMAToggle)
	if err != nil {
		logger.Error("fail to load target action data", "error", err)
		return "", err
	}
	switch input.TargetState {
	case serverpb.RDMAState_RDMA_ON:
		agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleEnableChangeConfig)
	case serverpb.RDMAState_RDMA_OFF:
		agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleDisableChangeConfig)
	}
	if err != nil {
		return "", errors.New("fail to get RDMA Toggle command")
	}

	cmds := []string{agentCmd.Command}

	agentInput := agentpb.TaskInput{
		Command:  strings.Join(cmds, " "),
		TargetIp: targetHostMgtIp,
		CmdQa:    nil,
		Timeout:  int32(agentCmd.TimeoutFactor),
	}

	maxTimeoutInSec := int32(0)
	taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, &agentInput)
	if err != nil {
		return "", err
	}
	if agentInput.GetTimeout() > maxTimeoutInSec {
		maxTimeoutInSec = agentInput.GetTimeout()
	}

	err = h.RdmaToggleWaitingForAgentTasks(ctx, input, []string{taskID}, maxTimeoutInSec)
	output, errGetOutput := h.RdmaToggleGetAgentTaskOutput(ctx, input, taskID)
	if err != nil {
		return "", fmt.Errorf("output: %s, %v,%v", output, err, errGetOutput)
	}

	logger.Info("RdmaToggleRunChangeConfigOnHost On Host " + targetHostMgtIp + " done.")
	return output, err
}

// Step 3: Restart all nodes
func (h *RdmaToggleActivities) RdmaToggleRunRestartOnHost(ctx context.Context, input RdmaToggleWorkflowInput, targetHostMgtIp string, subStep string) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleRunRestartOnHost started.")

	targetActionData, err := h.baseHandler.LoadTargetActionData(ctx, input.ClusterUUID, config.ActionRDMAToggle)
	if err != nil {
		logger.Error("fail to load target action data", "error", err)
		return "", err
	}

	var agentCmd *config.AgentCmd = nil
	switch input.TargetState {
	case serverpb.RDMAState_RDMA_ON:
		switch subStep {
		case SubStepEnterMaintenance:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleEnableEnterMaintenance)
		case SubStepRestartService:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleEnableRestartService)
		case SubStepExitMaintenance:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleEnableExitMaintenance)
		}
	case serverpb.RDMAState_RDMA_OFF:
		switch subStep {
		case SubStepEnterMaintenance:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleDisableEnterMaintenance)
		case SubStepRestartService:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleDisableRestartService)
		case SubStepExitMaintenance:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleDisableExitMaintenance)
		case SubStepAdjustResourceQuota:
			agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameRdmaToggleDisableAdjustResourceQuota)
		}
	}
	if err != nil || agentCmd == nil {
		return "", errors.New("fail to get RDMA Toggle command")
	}

	cmds := []string{agentCmd.Command}

	agentInput := agentpb.TaskInput{
		Command:  strings.Join(cmds, " "),
		TargetIp: targetHostMgtIp,
		CmdQa:    nil,
		Timeout:  int32(agentCmd.TimeoutFactor),
	}

	maxTimeoutInSec := int32(0)
	taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, &agentInput)
	if err != nil {
		return "", err
	}

	if agentInput.GetTimeout() > maxTimeoutInSec {
		maxTimeoutInSec = agentInput.GetTimeout()
	}

	err = h.RdmaToggleWaitingForAgentTasks(ctx, input, []string{taskID}, maxTimeoutInSec)
	output, errGetOutput := h.RdmaToggleGetAgentTaskOutput(ctx, input, taskID)
	if err != nil {
		return "", fmt.Errorf("output: %s, %v,%v", output, err, errGetOutput)
	}

	logger.Info("RdmaToggleRunChangeConfigOnHost On Host " + targetHostMgtIp + " done.")
	return output, err
}

func (h *RdmaToggleActivities) RdmaToggleTriggerAgentTasks(ctx context.Context, input RdmaToggleWorkflowInput, agentInputs []*agentpb.TaskInput) ([]string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggle TriggerAgentTasks started.")

	var taskIDs []string
	for _, agentInput := range agentInputs {
		taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
		if err != nil {
			return nil, err
		}
		taskIDs = append(taskIDs, taskID)
	}

	return taskIDs, nil
}

func (h *RdmaToggleActivities) RdmaToggleTriggerAgentTaskAndWait(ctx context.Context, input RdmaToggleWorkflowInput, agentInputs []*agentpb.TaskInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggle TriggerAgentTasks started.")

	var taskIDs []string
	maxTimeoutInSec := int32(0)
	for _, agentInput := range agentInputs {
		taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
		if err != nil {
			return err
		}
		taskIDs = append(taskIDs, taskID)
		if agentInput.GetTimeout() > maxTimeoutInSec {
			maxTimeoutInSec = agentInput.GetTimeout()
		}
	}

	err := h.RdmaToggleWaitingForAgentTasks(ctx, input, taskIDs, maxTimeoutInSec)
	return err
}

// RdmaToggleWaitingForAgentTasks wait for all agent tasks done and update progress if not timeout
func (h *RdmaToggleActivities) RdmaToggleWaitingForAgentTasks(ctx context.Context, input RdmaToggleWorkflowInput, taskIDs []string, timeoutSec int32) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleWaitingForAgentTasks started: %s.", taskIDs)

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}
	fmt.Println("towerTaskID", towerTaskID)

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return err
	}

	timeout := time.After(time.Duration(timeoutSec) * time.Second)
	agentClient := agentclient.NewTaskManagerClient(agentAddr)

	for {
		select {
		case <-timeout:
			slog.Error("Timeout reached waiting for tasks completion", "taskIDs", taskIDs)
			return fmt.Errorf("timeout waiting for tasks completion: %v", taskIDs)

		default:
			time.Sleep(1 * time.Second)

			allDone := true
			for _, taskID := range taskIDs {
				taskInput := &agentpb.GetTaskRequest{TaskId: taskID}
				done, err := h.baseHandler.CheckAgentTaskStatus(ctx, agentClient, taskInput)
				if err != nil {
					return err
				}
				if !done {
					allDone = false
					break
				}
			}

			if allDone {
				return nil
			}

		}
	}
}

func (h *RdmaToggleActivities) RdmaToggleGetAgentTaskOutput(ctx context.Context, input RdmaToggleWorkflowInput, taskID string) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RdmaToggleGetAgentTaskOutput started.", "taskID", taskID)

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return "", err
	}

	agentClient := agentclient.NewTaskManagerClient(agentAddr)

	output, err := h.baseHandler.GetAgentTaskOutput(ctx, agentClient, taskID)
	if err != nil {
		logger.Error("failed to get agent task output", "error", err)
		return output, err
	}

	return output, nil
}
