package rdmatoggle

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
)

func TestBuildProgressTreeOfSetRdmaOn(t *testing.T) {
	taskName := "test-rdma-on-task"
	clusterHostMgtIpList := []string{"*************", "*************", "*************"}

	progress := buildProgressTreeOfSetRdmaOn(taskName, clusterHostMgtIpList)

	// Verify task name
	assert.Equal(t, taskName, progress.TaskName)

	// Verify root node
	assert.Equal(t, "root", progress.Root.Key)
	assert.Equal(t, taskName, progress.Root.Name)
	assert.Equal(t, workflowutils.StatusPending, progress.Root.Status)

	// Verify main phases exist
	assert.Len(t, progress.Root.Children, 3)

	// Find phases by key
	var preCheckPhase, changeConfigPhase, restartPhase *workflowutils.WfProgressNode
	for i := range progress.Root.Children {
		switch progress.Root.Children[i].Key {
		case "pre_check":
			preCheckPhase = &progress.Root.Children[i]
		case "change_config":
			changeConfigPhase = &progress.Root.Children[i]
		case "restart_storage_service":
			restartPhase = &progress.Root.Children[i]
		}
	}

	// Verify pre-check phase
	assert.NotNil(t, preCheckPhase)
	assert.Equal(t, "Pre-check Phase", preCheckPhase.Name)
	assert.Len(t, preCheckPhase.Children, 3)

	// Verify change config phase
	assert.NotNil(t, changeConfigPhase)
	assert.Equal(t, "Change Config", changeConfigPhase.Name)
	assert.Len(t, changeConfigPhase.Children, 3)

	// Verify restart phase
	assert.NotNil(t, restartPhase)
	assert.Equal(t, "Restart Storage Service", restartPhase.Name)
	assert.Len(t, restartPhase.Children, 3)

	// Verify each restart task has 3 sub-steps
	for _, restartTask := range restartPhase.Children {
		assert.Len(t, restartTask.Children, 3)

		// Verify sub-step keys
		subStepKeys := make([]string, 3)
		for i, subStep := range restartTask.Children {
			subStepKeys[i] = subStep.Key
		}
		assert.Contains(t, subStepKeys, restartTask.Key+"_enter_maintenance")
		assert.Contains(t, subStepKeys, restartTask.Key+"_restart_service")
		assert.Contains(t, subStepKeys, restartTask.Key+"_exit_maintenance")
	}

	// Print the structure for visual verification
	jsonData, err := json.MarshalIndent(progress, "", "  ")
	assert.NoError(t, err)
	t.Logf("RDMA ON Progress Tree:\n%s", string(jsonData))
}

func TestBuildProgressTreeOfSetRdmaOff(t *testing.T) {
	taskName := "test-rdma-off-task"
	clusterHostMgtIpList := []string{"*************", "*************"}

	progress := buildProgressTreeOfSetRdmaOff(taskName, clusterHostMgtIpList)

	// Verify task name
	assert.Equal(t, taskName, progress.TaskName)

	// Verify root node
	assert.Equal(t, "root", progress.Root.Key)
	assert.Equal(t, taskName, progress.Root.Name)
	assert.Equal(t, workflowutils.StatusPending, progress.Root.Status)

	// Verify only change config phase exists for RDMA OFF
	assert.Len(t, progress.Root.Children, 1)
	changeConfigPhase := progress.Root.Children[0]
	assert.Equal(t, "change_config", changeConfigPhase.Key)
	assert.Equal(t, "Change Config", changeConfigPhase.Name)
	assert.Len(t, changeConfigPhase.Children, 2)

	// Verify each host has a config change task
	for i, configTask := range changeConfigPhase.Children {
		expectedKey := clusterHostMgtIpList[i] + "_change_config"
		assert.Equal(t, expectedKey, configTask.Key)
		assert.Equal(t, "Change Config on "+clusterHostMgtIpList[i], configTask.Name)
		assert.Len(t, configTask.Children, 0) // No sub-tasks for RDMA OFF
	}

	// Print the structure for visual verification
	jsonData, err := json.MarshalIndent(progress, "", "  ")
	assert.NoError(t, err)
	t.Logf("RDMA OFF Progress Tree:\n%s", string(jsonData))
}

func TestProgressTreeSelection(t *testing.T) {
	clusterHostMgtIpList := []string{"*************"}

	// Test RDMA ON selection
	inputOn := RdmaToggleWorkflowInput{
		TargetState:      server_pb.RDMAState_RDMA_ON,
		TowerTaskCfgName: "SetRdmaOn",
		JobID:            "test-job-1",
	}

	progressOn := buildProgressTreeOfSetRdmaOn(inputOn.TowerTaskCfgName+"-"+inputOn.JobID, clusterHostMgtIpList)
	assert.Contains(t, progressOn.TaskName, "SetRdmaOn")
	assert.Len(t, progressOn.Root.Children, 3) // pre_check, change_config, restart_storage_service

	// Test RDMA OFF selection
	inputOff := RdmaToggleWorkflowInput{
		TargetState:      server_pb.RDMAState_RDMA_OFF,
		TowerTaskCfgName: "SetRdmaOff",
		JobID:            "test-job-2",
	}

	progressOff := buildProgressTreeOfSetRdmaOff(inputOff.TowerTaskCfgName+"-"+inputOff.JobID, clusterHostMgtIpList)
	assert.Contains(t, progressOff.TaskName, "SetRdmaOff")
	assert.Len(t, progressOff.Root.Children, 1) // only change_config
}
