package rdmatoggle

import (
	"encoding/json"
	"errors"
	"fmt"

	"go.temporal.io/sdk/workflow"

	server_pb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	tower "github.smartx.com/LCM/lcm-manager/third_party/tower"
)

type RdmaToggleWorkflowInput struct {
	Header           workflowutils.CustomedHeader `json:"header"`
	JobID            string                       `json:"job_id"`
	TowerClusterID   string                       `json:"tower_cluster_id"`
	ClusterUUID      string                       `json:"cluster_uuid"`
	ClusterName      string                       `json:"cluster_name"`
	ClusterIP        string                       `json:"cluster_vip"`
	HostUUID         string                       `json:"host_uuid"`
	HostName         string                       `json:"host_name"`
	IsVmware         bool                         `json:"is_vmware"`
	TowerTaskCfgName string                       `json:"tower_task_cfg_name"`
	TargetState      server_pb.RDMAState          `json:"target_state"`
}

const (
	SubStepEnterMaintenance    = "enter_maintenance"
	SubStepRestartService      = "restart_service"
	SubStepExitMaintenance     = "exit_maintenance"
	SubStepAdjustResourceQuota = "adjust_resource_quota"
)

func cleanupOnError(ctx workflow.Context, ctxStateUpdate workflow.Context, s *workflowState, input RdmaToggleWorkflowInput, err error) {
	logger := workflow.GetLogger(ctx)

	if errors.Is(ctx.Err(), workflow.ErrCanceled) {
		ctx, _ = workflow.NewDisconnectedContext(ctx)
	}

	if e := workflow.ExecuteActivity(ctx, s.activities.RdmaToggleStoreJobLogs, input, s.agentTaskIDs).Get(ctx, nil); e != nil {
		logger.Error("fail to store lcm manager agent logs", "err", e)
	}

	if s.hostPluginID != "" {
		if e := workflow.ExecuteActivity(ctx, s.activities.RdmaToggleCleanupHostPlugin, s.hostPluginID).Get(ctx, nil); e != nil {
			logger.Error("fail to cleanup host plugin", "err", e)
		}
	}

	if err != nil {
		if e := workflow.ExecuteActivity(ctxStateUpdate, s.activities.RdmaToggleUpdateJobState, input, server_pb.JobState_JOB_STATE_FAILED).Get(ctx, nil); e != nil {
			logger.Error("fail to update rdma toggle job state with failed", "err", e)
		}

		if s.towerTaskID != "" {
			if e := workflow.ExecuteActivity(ctxStateUpdate, s.activities.RdmaToggleUpdateTowerTask, input, s.towerTaskID, tower.TaskStatusFailed, s.workflowProgress).Get(ctx, nil); e != nil {
				logger.Error("fail to update rdma toggle tower task")
			}
		}
	}
}

type workflowState struct {
	activities   *RdmaToggleActivities
	towerTaskID  string
	hostPluginID string
	// lcm-manager-agent task ids, used for collecting logs from lcm-manager-agent
	agentTaskIDs         []string
	workflowProgress     workflowutils.WorkflowProgress
	clusterHostMgtIpList []string
}

func buildProgressTreeOfSetRdmaOn(taskName string, clusterHostMgtIpList []string) *workflowutils.WorkflowProgress {
	progress := workflowutils.NewWorkflowProgress(taskName)

	// Add root node
	progress.AddNode("", workflowutils.WfProgressNode{
		Key:    "root",
		Name:   taskName,
		Status: workflowutils.StatusPending,
	})

	// Add main phases
	progress.AddNode("root", workflowutils.WfProgressNode{
		Key:    "pre_check",
		Name:   "Pre-check Phase",
		Status: workflowutils.StatusPending,
	})

	progress.AddNode("root", workflowutils.WfProgressNode{
		Key:    "change_config",
		Name:   "Change Config",
		Status: workflowutils.StatusPending,
	})

	progress.AddNode("root", workflowutils.WfProgressNode{
		Key:    "restart_storage_service",
		Name:   "Restart Storage Service",
		Status: workflowutils.StatusPending,
	})

	// Add per-host pre-check tasks
	for _, ip := range clusterHostMgtIpList {
		progress.AddNode("pre_check", workflowutils.WfProgressNode{
			Key:    ip + "_pre_check",
			Name:   "Pre-check on " + ip,
			Status: workflowutils.StatusPending,
		})
	}

	// Add per-host config change tasks
	for _, ip := range clusterHostMgtIpList {
		progress.AddNode("change_config", workflowutils.WfProgressNode{
			Key:    ip + "_change_config",
			Name:   "Change Config on " + ip,
			Status: workflowutils.StatusPending,
		})
	}

	// Add per-host restart tasks with sub-steps
	for _, ip := range clusterHostMgtIpList {
		restartTaskKey := ip + "_restart_storage_service"
		progress.AddNode("restart_storage_service", workflowutils.WfProgressNode{
			Key:    restartTaskKey,
			Name:   "Restart Storage Service on " + ip,
			Status: workflowutils.StatusPending,
		})

		// Add sub-steps for restart
		progress.AddNode(restartTaskKey, workflowutils.WfProgressNode{
			Key:    restartTaskKey + "_enter_maintenance",
			Name:   "Enter Maintenance on " + ip,
			Status: workflowutils.StatusPending,
		})

		progress.AddNode(restartTaskKey, workflowutils.WfProgressNode{
			Key:    restartTaskKey + "_restart_service",
			Name:   "Restart Service on " + ip,
			Status: workflowutils.StatusPending,
		})

		progress.AddNode(restartTaskKey, workflowutils.WfProgressNode{
			Key:    restartTaskKey + "_exit_maintenance",
			Name:   "Exit Maintenance on " + ip,
			Status: workflowutils.StatusPending,
		})
	}

	return progress
}

func buildProgressTreeOfSetRdmaOff(taskName string, clusterHostMgtIpList []string) *workflowutils.WorkflowProgress {
	progress := workflowutils.NewWorkflowProgress(taskName)

	// Add root node
	progress.AddNode("", workflowutils.WfProgressNode{
		Key:    "root",
		Name:   taskName,
		Status: workflowutils.StatusPending,
	})

	// Add per-host config change tasks
	for _, ip := range clusterHostMgtIpList {
		setRdmaOffKey := ip + "_set_rdma_off"

		progress.AddNode("root", workflowutils.WfProgressNode{
			Key:    setRdmaOffKey,
			Name:   "Set RDMA Off on " + ip,
			Status: workflowutils.StatusPending,
		})

		progress.AddNode(setRdmaOffKey, workflowutils.WfProgressNode{
			Key:    setRdmaOffKey + "_change_config",
			Name:   "Change Config on " + ip,
			Status: workflowutils.StatusPending,
		})
		// Add sub-steps for restart
		progress.AddNode(setRdmaOffKey, workflowutils.WfProgressNode{
			Key:    setRdmaOffKey + "_enter_maintenance",
			Name:   "Enter Maintenance on " + ip,
			Status: workflowutils.StatusPending,
		})

		progress.AddNode(setRdmaOffKey, workflowutils.WfProgressNode{
			Key:    setRdmaOffKey + "_restart_service",
			Name:   "Restart Service on " + ip,
			Status: workflowutils.StatusPending,
		})

		progress.AddNode(setRdmaOffKey, workflowutils.WfProgressNode{
			Key:    setRdmaOffKey + "_exit_maintenance",
			Name:   "Exit Maintenance on " + ip,
			Status: workflowutils.StatusPending,
		})
		progress.AddNode(setRdmaOffKey, workflowutils.WfProgressNode{
			Key:    setRdmaOffKey + "_adjust_resource_quota",
			Name:   "Adjust Resource Quota on " + ip,
			Status: workflowutils.StatusPending,
		})
	}

	return progress
}

func RdmaToggleWorkflow(ctx workflow.Context, input RdmaToggleWorkflowInput) error {
	S := &workflowState{}
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	// ctxActionWait, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameWaitingForRDMAToggle)
	// if err != nil {
	// 	return err
	// }

	logger := workflow.GetLogger(ctx)
	logger.Info("RdmaToggleWorkflow workflow started", "JobID", input.JobID)

	// 1. update job state: running
	if err = workflow.ExecuteActivity(ctxStateUpdate, S.activities.RdmaToggleUpdateJobState, input, server_pb.JobState_JOB_STATE_RUNNING).Get(ctx, nil); err != nil {
		logger.Error("fail to update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, S.activities.RdmaToggleCreateTowerTask, input).Get(ctx, &S.towerTaskID); err != nil {
		logger.Error("fail to create rdma toggle tower task")
		cleanupOnError(ctx, ctxStateUpdate, S, input, err)
		return err
	}

	// 2. host plugin: install
	if err = workflow.ExecuteActivity(ctx, S.activities.RdmaToggleVerifyHostPluginInstalledAndReady, input).Get(ctx, &S.hostPluginID); err != nil {
		logger.Error("fail to verify host plugin installed and ready")
		cleanupOnError(ctx, ctxStateUpdate, S, input, err)
		return err
	}

	if S.hostPluginID == "" {
		if err = workflow.ExecuteActivity(ctx, S.activities.RdmaToggleInstallHostPlugin, input).Get(ctx, &S.hostPluginID); err != nil {
			logger.Error("fail to install host plugin")
			cleanupOnError(ctx, ctxStateUpdate, S, input, err)
			return err
		}
	}

	if err := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleGetClusterIPs, input).Get(ctx, &S.clusterHostMgtIpList); err != nil {
		logger.Error("fail to get cluster IPs")
		cleanupOnError(ctx, ctxStateUpdate, S, input, err)
		return err
	}

	// Build progress tree based on target state
	taskName := input.TowerTaskCfgName + "-" + input.JobID
	if input.TargetState == server_pb.RDMAState_RDMA_ON {
		S.workflowProgress = *buildProgressTreeOfSetRdmaOn(taskName, S.clusterHostMgtIpList)
	} else {
		S.workflowProgress = *buildProgressTreeOfSetRdmaOff(taskName, S.clusterHostMgtIpList)
	}

	// 3. Step 1: Pre-check all nodes (only for RDMA ON)
	if input.TargetState == server_pb.RDMAState_RDMA_ON {
		logger.Info("Starting RDMA toggle pre-check phase")

		// Update pre-check phase to running
		S.workflowProgress.UpdateNodeStatus("pre_check", workflowutils.StatusRunning, nil)

		futures := make(map[string]workflow.Future, len(S.clusterHostMgtIpList))
		taskIds := make(map[string]string, len(S.clusterHostMgtIpList))
		outputs := make(map[string]string, len(S.clusterHostMgtIpList))

		// Start all activities in parallel and update individual node status
		for _, ip := range S.clusterHostMgtIpList {
			S.workflowProgress.UpdateNodeStatus(ip+"_pre_check", workflowutils.StatusRunning, nil)
			futures[ip] = workflow.ExecuteActivity(ctx, S.activities.RdmaToggleRunPreCheckOnHost, input, ip)
		}

		// Wait for all activities to complete
		for ip, future := range futures {
			var taskId string
			if err := future.Get(ctx, &taskId); err != nil {
				logger.Error("fail to generate pre-check agent inputs", "error", err)
				S.workflowProgress.UpdateNodeStatus(ip+"_pre_check", workflowutils.StatusFailed, []string{err.Error()})
				var output string
				if errOutput := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleGetAgentTaskOutput, input, taskId).Get(ctx, &output); errOutput != nil || output != "" {
					logger.Error("fail to get agent task output", "error", errOutput)
				}
				taskIds[ip] = taskId
				outputs[ip] = output
				cleanupOnError(ctx, ctxStateUpdate, S, input, fmt.Errorf("output: %s, error: %v", outputs[ip], err))
				return err
			} else {
				taskIds[ip] = taskId
				S.workflowProgress.UpdateNodeStatus(ip+"_pre_check", workflowutils.StatusSuccess, nil)
				var output string
				if errOutput := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleGetAgentTaskOutput, input, taskIds[ip]).Get(ctx, &output); errOutput != nil {
					logger.Error("fail to get agent task output", "error", errOutput)
				}
				outputs[ip] = output
			}
		}
		// Collect all check results
		checkResults := make(map[string]bool)
		expectedFields := []string{"memory", "metro_availability", "bond_mode", "rdma_support",
			"roce_mode", "network_isolation", "vlan_config", "can_update_rdma"}

		type CheckDetail struct {
			SuccessIPs []string
			FailedIPs  []string
			Hostname   string
		}
		checkDetails := make(map[string]*CheckDetail)

		// Initialize check details for all expected fields
		for _, field := range expectedFields {
			checkDetails[field] = &CheckDetail{
				SuccessIPs: make([]string, 0),
				FailedIPs:  make([]string, 0),
			}
		}

		// Process each host's output
		for ip, output := range outputs {
			if output == "" {
				continue
			}

			var result map[string]bool
			if err := json.Unmarshal([]byte(output), &result); err != nil {
				cleanupOnError(ctx, ctxStateUpdate, S, input, fmt.Errorf("invalid json output: %s", output))
				return fmt.Errorf("invalid json output: %s", output)
			}

			// Update check details for each field
			for field, passed := range result {
				if detail, exists := checkDetails[field]; exists {
					if passed {
						detail.SuccessIPs = append(detail.SuccessIPs, ip)
						checkResults[field] = true
					} else {
						detail.FailedIPs = append(detail.FailedIPs, ip)
						checkResults[field] = false
					}
					// Store hostname if this is the first time we see this field
					if detail.Hostname == "" {
						detail.Hostname = ip // Using IP as hostname for now, can be modified if needed
					}
				}
			}
		}

		// Verify all required fields exist and are true
		var failedChecks []string
		for _, field := range expectedFields {
			if val, exists := checkResults[field]; !exists || !val {
				failedChecks = append(failedChecks, field)
			}
		}

		if len(failedChecks) > 0 {
			err := fmt.Errorf("pre-check failed for: %v", failedChecks)
			cleanupOnError(ctx, ctxStateUpdate, S, input, err)
			return err
		}
		// Convert map values to slice for appending
		for _, taskId := range taskIds {
			S.agentTaskIDs = append(S.agentTaskIDs, taskId)
		}
	}

	// 4. Step 2: Configure all nodes
	logger.Info("Starting RDMA toggle config phase")

	// Update config phase to running
	S.workflowProgress.UpdateNodeStatus("change_config", workflowutils.StatusRunning, nil)

	for _, ip := range S.clusterHostMgtIpList {
		var changeConfigTaskKeyOfNode string
		if input.TargetState == server_pb.RDMAState_RDMA_ON {
			changeConfigTaskKeyOfNode = ip + "_change_config"
		} else {
			changeConfigTaskKeyOfNode = ip + "_set_rdma_off_change_config"
		}
		// Update individual node status to running
		S.workflowProgress.UpdateNodeStatus(changeConfigTaskKeyOfNode, workflowutils.StatusRunning, nil)

		var taskId string
		if err := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleRunChangeConfigOnHost, input, ip).Get(ctx, &taskId); err != nil {
			logger.Error("fail to change config on host", "ip", ip, "error", err)
			S.workflowProgress.UpdateNodeStatus(changeConfigTaskKeyOfNode, workflowutils.StatusFailed, []string{err.Error()})
			cleanupOnError(ctx, ctxStateUpdate, S, input, err)
			return err
		}

		// Update individual node status to success
		S.workflowProgress.UpdateNodeStatus(changeConfigTaskKeyOfNode, workflowutils.StatusSuccess, nil)
		S.agentTaskIDs = append(S.agentTaskIDs, taskId)
	}

	// 5. Step 3: Restart all nodes
	logger.Info("Starting RDMA toggle restart phase")

	// Update restart phase to running
	S.workflowProgress.UpdateNodeStatus("restart_storage_service", workflowutils.StatusRunning, nil)

	for _, ip := range S.clusterHostMgtIpList {
		var baseTaskKey string
		if input.TargetState == server_pb.RDMAState_RDMA_ON {
			baseTaskKey = ip + "_restart_storage_service"
		} else {
			baseTaskKey = ip + "_set_rdma_off"
		}

		// Update main restart task to running
		S.workflowProgress.UpdateNodeStatus(baseTaskKey, workflowutils.StatusRunning, nil)

		// Enter maintenance
		S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_enter_maintenance", workflowutils.StatusRunning, nil)
		var TaskId string
		if err := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleRunRestartOnHost, input, ip, SubStepEnterMaintenance).Get(ctx, &TaskId); err != nil {
			logger.Error("fail to enter maintenance", "ip", ip, "error", err)
			S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_enter_maintenance", workflowutils.StatusFailed, []string{err.Error()})
			cleanupOnError(ctx, ctxStateUpdate, S, input, err)
			return err
		}
		S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_enter_maintenance", workflowutils.StatusSuccess, nil)
		S.agentTaskIDs = append(S.agentTaskIDs, TaskId)

		// Restart service
		S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_restart_service", workflowutils.StatusRunning, nil)
		if err := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleRunRestartOnHost, input, ip, SubStepRestartService).Get(ctx, &TaskId); err != nil {
			logger.Error("fail to restart service", "ip", ip, "error", err)
			S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_restart_service", workflowutils.StatusFailed, []string{err.Error()})
			cleanupOnError(ctx, ctxStateUpdate, S, input, err)
			return err
		}
		S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_restart_service", workflowutils.StatusSuccess, nil)
		S.agentTaskIDs = append(S.agentTaskIDs, TaskId)

		// Exit maintenance
		S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_exit_maintenance", workflowutils.StatusRunning, nil)
		if err := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleRunRestartOnHost, input, ip, SubStepExitMaintenance).Get(ctx, &TaskId); err != nil {
			logger.Error("fail to exit maintenance", "ip", ip, "error", err)
			S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_exit_maintenance", workflowutils.StatusFailed, []string{err.Error()})
			cleanupOnError(ctx, ctxStateUpdate, S, input, err)
			return err
		}
		S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_exit_maintenance", workflowutils.StatusSuccess, nil)
		S.agentTaskIDs = append(S.agentTaskIDs, TaskId)

		// Update main restart task to success
		S.workflowProgress.UpdateNodeStatus(baseTaskKey, workflowutils.StatusSuccess, nil)

		if input.TargetState == server_pb.RDMAState_RDMA_OFF {
			S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_adjust_resource_quota", workflowutils.StatusRunning, nil)
			if err := workflow.ExecuteActivity(ctx, S.activities.RdmaToggleRunRestartOnHost, input, ip, SubStepAdjustResourceQuota).Get(ctx, &TaskId); err != nil {
				logger.Error("fail to adjust_resource_quota", "ip", ip, "error", err)
				S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_adjust_resource_quota", workflowutils.StatusFailed, []string{err.Error()})
				cleanupOnError(ctx, ctxStateUpdate, S, input, err)
				return err
			}
			S.workflowProgress.UpdateNodeStatus(baseTaskKey+"_adjust_resource_quota", workflowutils.StatusSuccess, nil)
			S.agentTaskIDs = append(S.agentTaskIDs, TaskId)

		}
	}

	// 6. update job state: success
	// Update root progress to success
	S.workflowProgress.UpdateNodeStatus("root", workflowutils.StatusSuccess, nil)

	if err = workflow.ExecuteActivity(ctxStateUpdate, S.activities.RdmaToggleUpdateJobState, input, server_pb.JobState_JOB_STATE_SUCCESS).Get(ctx, nil); err != nil {
		logger.Error("failed update rdma toggle job state")
		cleanupOnError(ctx, ctxStateUpdate, S, input, err)
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, S.activities.RdmaToggleUpdateTowerTask, input, S.towerTaskID, tower.TaskStatusSuccessed, &S.workflowProgress).Get(ctx, nil); err != nil {
		logger.Error("fail to update rdma toggle tower task")
		cleanupOnError(ctx, ctxStateUpdate, S, input, err)
		return err
	}

	logger.Info("RdmaToggleWorkflow completed.")
	cleanupOnError(ctx, ctxStateUpdate, S, input, err)

	return nil
}
