package utils

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWorkflowProgress_MultiLevel(t *testing.T) {
	// 创建工作流进度
	wp := NewWorkflowProgress("cluster-xxx-rdma-enable")

	wp.AddNode("", WfProgressNode{
		Key:     "root",
		Name:    "cluster-xxx-rdma-enable",
		Details: []string{},
		Status:  StatusPending,
	})

	// 第一层：主要阶段
	wp.AddNode("root", WfProgressNode{
		Key:    "pre_check",
		Name:   "Pre-check Phase",
		Status: StatusPending,
	})
	wp.AddNode("root", WfProgressNode{
		Key:    "change_config",
		Name:   "Change Config",
		Status: StatusPending,
	})
	wp.AddNode("root", WfProgressNode{
		Key:    "restart_storage_service",
		Name:   "Restart Storage Service",
		Status: StatusPending,
	})

	// 第二层：分节点的子进度

	for _, ip := range []string{"*************", "*************", "*************"} {
		wp.AddNode("pre_check", WfProgressNode{
			Key:    ip + "_pre_check",
			Name:   "i18n precheck on " + ip,
			Status: StatusPending,
		})
	}

	for _, ip := range []string{"*************", "*************", "*************"} {
		configTaskKey := ip + "_change_config"
		wp.AddNode("change_config", WfProgressNode{
			Key:    configTaskKey,
			Name:   "i18n change config on " + ip,
			Status: StatusPending,
		})
		wp.AddNode(configTaskKey, WfProgressNode{
			Key:    configTaskKey + "_enter_maintenance",
			Name:   "i18n enter maintenance on " + ip,
			Status: StatusPending,
		})
		wp.AddNode(configTaskKey, WfProgressNode{
			Key:    configTaskKey + "_restart_service",
			Name:   "i18n restart service on " + ip,
			Status: StatusPending,
		})
		wp.AddNode(configTaskKey, WfProgressNode{
			Key:    configTaskKey + "_exit_maintenance",
			Name:   "i18n exit maintenance on " + ip,
			Status: StatusPending,
		})

	}

	// 模拟执行过程
	fmt.Println("=== Initial State ===")
	printProgress(t, wp)

	// 开始执行
	wp.UpdateNodeStatus("pre_check", StatusRunning, []string{"fake detail"})
	wp.UpdateNodeStatus("change_config", StatusRunning, []string{"fake detail"})
	printProgress(t, wp)

	for _, ip := range []string{"*************", "*************", "*************"} {
		wp.UpdateNodeStatus(ip+"_pre_check", StatusSuccess, []string{"fake detail"})
	}
	printProgress(t, wp)

	// 验证状态传播
	assert.Equal(t, StatusSuccess, wp.Root.Children[0].Status)             // pre_check should be success
	assert.Equal(t, StatusRunning, wp.Root.Children[1].Status)             // change_config should be running
	assert.Equal(t, StatusRunning, wp.Root.Status)                         // root should be running
	assert.Equal(t, StatusSuccess, wp.Root.Children[0].Children[0].Status) // *************_pre_check should be success
	assert.Equal(t, StatusSuccess, wp.Root.Children[0].Children[1].Status) // *************_pre_check should be success
	assert.Equal(t, StatusSuccess, wp.Root.Children[0].Children[2].Status) // *************_pre_check should be success
}

func printProgress(t *testing.T, wp *WorkflowProgress) {
	jsonData, err := json.MarshalIndent(wp, "", "  ")
	assert.NoError(t, err)
	fmt.Println(string(jsonData))
}
