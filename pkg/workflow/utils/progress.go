package utils

import "fmt"

type StepStatus string

const (
	StatusPending StepStatus = "Pending"
	StatusRunning StepStatus = "Running"
	StatusSuccess StepStatus = "Success" // Changed to match example
	StatusFailed  StepStatus = "Failed"
)

type WfProgressNode struct {
	Key      string           `json:"key"`
	Name     string           `json:"name"`
	Status   StepStatus       `json:"status"`
	Children []WfProgressNode `json:"children,omitempty"`
	Details  []string         `json:"details,omitempty"`
}

type WorkflowProgress struct {
	TaskName string         `json:"task_name"`
	Root     WfProgressNode `json:"root"`
}

func NewWorkflowProgress(taskName string) *WorkflowProgress {
	return &WorkflowProgress{
		TaskName: taskName,
		Root:     WfProgressNode{},
	}
}

// AddNode adds a new child node to the specified parent
func (wp *WorkflowProgress) AddNode(parentKey string, node WfProgressNode) error {
	if parentKey == "" {
		wp.Root = node
		return nil
	}

	return wp.addNodeRecursive(&wp.Root, parentKey, node)
}

func (wp *WorkflowProgress) addNodeRecursive(current *WfProgressNode, parentKey string, newNode WfProgressNode) error {
	if current.Key == parentKey {
		current.Children = append(current.Children, newNode)
		return nil
	}

	for i := range current.Children {
		if err := wp.addNodeRecursive(&current.Children[i], parentKey, newNode); err == nil {
			return nil
		}
	}

	return fmt.Errorf("parent node not found: %s", parentKey)
}

// UpdateNodeStatus updates a node's status and cascades the update to parent
func (wp *WorkflowProgress) UpdateNodeStatus(nodeKey string, status StepStatus, details []string) error {
	return wp.updateNodeStatusRecursive(&wp.Root, nodeKey, status, details)
}

func (wp *WorkflowProgress) updateNodeStatusRecursive(current *WfProgressNode, nodeKey string, status StepStatus, details []string) error {
	if current.Key == nodeKey {
		current.Status = status
		if len(details) > 0 {
			current.Details = details
		}
		return nil
	}

	for i := range current.Children {
		if err := wp.updateNodeStatusRecursive(&current.Children[i], nodeKey, status, details); err == nil {
			wp.updateParentStatus(current)
			return nil
		}
	}

	return fmt.Errorf("node not found: %s", nodeKey)
}

func (wp *WorkflowProgress) updateParentStatus(node *WfProgressNode) {
	allSuccess := true
	hasRunning := false
	hasFailed := false

	for _, child := range node.Children {
		switch child.Status {
		case StatusSuccess:
			continue
		case StatusFailed:
			hasFailed = true
			allSuccess = false
		case StatusRunning:
			hasRunning = true
			allSuccess = false
		case StatusPending:
			allSuccess = false
		}
	}

	switch {
	case allSuccess:
		node.Status = StatusSuccess
	case hasFailed:
		node.Status = StatusFailed
	case hasRunning:
		node.Status = StatusRunning
	default:
		node.Status = StatusPending
	}
}

// Example usage:
//   wp := NewWorkflowProgress("cluster-A")
//   wp.AddNode("", TreeNode{
//     Key:    "root",
//     Name:   "Cluster Init",
//     Status: StatusRunning,
//   })
//   wp.AddNode("root", TreeNode{
//     Key:    "step1",
//     Name:   "Download Images",
//     Status: StatusSuccess,
//   })
