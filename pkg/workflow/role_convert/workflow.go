package roleconvert

import (
	"errors"

	"github.com/gofrs/uuid"
	"go.temporal.io/sdk/workflow"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
)

type RoleConvertCheckWorkflowInput struct {
	Header         workflowutils.CustomedHeader `json:"header"`
	JobID          string                       `json:"job_id"`
	TowerClusterID string                       `json:"tower_cluster_id"`
	ClusterUUID    string                       `json:"cluster_uuid"`
	ClusterIP      string                       `json:"cluster_vip"`
	HostName       string                       `json:"host_name"`
	HostUUID       string                       `json:"host_uuid"`
	TargetRole     string                       `json:"target_role"`
	IsVmware       bool                         `json:"is_vmware"`
}

func RoleConvertCheckWorkflow(ctx workflow.Context, input RoleConvertCheckWorkflowInput) error { //nolint: funlen
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNamePrecheckDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	logger := workflow.GetLogger(ctx)
	logger.Info("RoleConvertCheck workflow started", "JobID", input.JobID)

	var h *RoleConvertCheck
	var towerTaskID string

	defer func() {
		if err != nil {
			if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertCheckJobStateWithFailed, input).Get(ctx, nil); e != nil {
				logger.Error("fail to update role convert check job state with failed", "err", e)
			}

			if towerTaskID != "" {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertCheckTowerTaskWithFailed, input, towerTaskID).Get(ctx, nil); e != nil {
					logger.Error("fail to update role convert check tower task with failed", "err", e)
				}
			}
		}
	}()

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.CreateRoleConvertCheckTowerTask, input).Get(ctx, &towerTaskID); err != nil {
		logger.Error("failed update job state")
		return err
	}

	selector := workflow.NewSelector(ctx)

	activities, err := h.GetActivities(input)
	if err != nil {
		logger.Error("get activity failed.", "Error", err)
		return workflow.NewContinueAsNewError(ctx, err, "failed to get activities")
	}

	cids := make([]string, len(activities))
	for i := 0; i < len(activities); i++ {
		_ = workflow.SideEffect(ctx, func(_ workflow.Context) interface{} {
			tmpID, _ := uuid.NewV7()
			return tmpID.String()
		}).Get(&cids[i])
	}

	for i, activity := range activities {
		// init check result
		future := workflow.ExecuteActivity(ctx, activity, cids[i], input, true)

		selector.AddFuture(future, func(f workflow.Future) {
			err1 := f.Get(ctx, nil)
			if err1 != nil {
				logger.Error("Activity failed.", "Error", err1)
				err = err1
				return
			}

			logger.Info("Activity completed.", "check_id", cids[i])
		})
	}

	for i := 0; i < len(activities); i++ {
		selector.Select(ctx)
		if err != nil {
			logger.Error("Activity failed.", "Error", err)
			return err
		}
	}

	logger.Info("check result init completed.")

	if err := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertCheckJobStateWithRunning, input).Get(ctx, nil); err != nil {
		logger.Error("failed update job state")
		return err
	}

	// do check
	precheckSelector := workflow.NewSelector(ctx)
	for i, activity := range activities {
		future := workflow.ExecuteActivity(ctx, activity, cids[i], input, false)

		precheckSelector.AddFuture(future, func(f workflow.Future) {
			err1 := f.Get(ctx, nil)
			if err1 != nil {
				logger.Error("Activity failed.", "Error", err1)
				err = err1
				return
			}

			logger.Info("Activity completed.", "check_id", cids[i])
		})
	}

	for i := 0; i < len(activities); i++ {
		precheckSelector.Select(ctx)
		if err != nil {
			logger.Error("Activity failed.", "Error", err)
			return err
		}
	}

	if err := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertCheckJobStateWithSuccess, input).Get(ctx, nil); err != nil {
		logger.Error("failed update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertCheckTowerTaskWithSuccess, input, towerTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to update role convert check tower task with success")
		return err
	}

	logger.Info("RoleConvertCheck workflow completed.")

	return nil
}

type RoleConvertWorkflowInput struct {
	Header         workflowutils.CustomedHeader `json:"header"`
	JobID          string                       `json:"job_id"`
	TowerClusterID string                       `json:"tower_cluster_id"`
	ClusterUUID    string                       `json:"cluster_uuid"`
	ClusterIP      string                       `json:"cluster_vip"`
	HostName       string                       `json:"host_name"`
	HostUUID       string                       `json:"host_uuid"`
	TargetRole     string                       `json:"target_role"`
	SkipPrecheck   bool                         `json:"skip_precheck"`
	IsVmware       bool                         `json:"is_vmware"`
}

func ChildRoleConvertCheckWorkflow(ctx workflow.Context, input RoleConvertWorkflowInput, cwJobID string) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("RoleConvertCheck child workflow started", "JobID", input.JobID)

	cwo := workflow.ChildWorkflowOptions{
		WorkflowID: "role-convert-2nd-check-" + cwJobID,
	}

	ctx = workflow.WithChildOptions(ctx, cwo)

	cwInput := RoleConvertCheckWorkflowInput{
		Header:         input.Header,
		JobID:          cwJobID,
		TowerClusterID: input.TowerClusterID,
		ClusterUUID:    input.ClusterUUID,
		ClusterIP:      input.ClusterIP,
		HostName:       input.HostName,
		HostUUID:       input.HostUUID,
		TargetRole:     input.TargetRole,
		IsVmware:       input.IsVmware,
	}

	err := workflow.ExecuteChildWorkflow(ctx, RoleConvertCheckWorkflow, cwInput).Get(ctx, nil)
	if err != nil {
		logger.Error("Received child role convert check workflow failure.", "Error", err)
		return err
	}

	logger.Info("Child role convert check workflow completed.")

	return nil
}

func RoleConvertWorkflow(ctx workflow.Context, input RoleConvertWorkflowInput) error { //nolint: funlen,gocyclo
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	ctxActionWait, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameWaitingForRoleConvertDone)
	if err != nil {
		return err
	}

	logger := workflow.GetLogger(ctx)
	logger.Info("RoleConvert workflow started", "JobID", input.JobID)

	var h *RoleConvert
	var realActionStarted bool
	var rollBackHostRoleInfo bool
	var towerTaskID string
	var hpID string

	// lcm-manager-agent task ids, used for collecting logs from lcm-manager-agent
	agentTaskIDs := make([]string, 0)

	defer func() {
		if errors.Is(ctx.Err(), workflow.ErrCanceled) {
			// When the Workflow is canceled, it has to get a new disconnected context to execute any Activities
			ctx, _ = workflow.NewDisconnectedContext(ctx)
		}

		if rollBackHostRoleInfo && err != nil {
			var rollbackAgentTaskID string
			if e := workflow.ExecuteActivity(ctx, h.RollBackHostRoleInfo, input).Get(ctx, &rollbackAgentTaskID); e != nil {
				logger.Error("fail to roll back host role info", "err", e)
			}

			if rollbackAgentTaskID != "" {
				agentTaskIDs = append(agentTaskIDs, rollbackAgentTaskID)
			}
		}

		if realActionStarted {
			if e := workflow.ExecuteActivity(ctx, h.StoreRoleConvertJobLogs, input, agentTaskIDs).Get(ctx, nil); e != nil {
				logger.Error("fail to store lcm manager agent logs", "err", e)
			}
		}

		if hpID != "" {
			if e := workflow.ExecuteActivity(ctx, h.RoleConvertCleanupHostPlugin, hpID).Get(ctx, nil); e != nil {
				logger.Error("fail to cleanup host plugin", "err", e)
			}
		}

		if err != nil {
			// input.SkipPrecheck == true means that the action is retry action, host label should mark as failed if job runs failed.
			if input.SkipPrecheck || realActionStarted {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateHostLabelRoleConvertFailed, input).Get(ctx, nil); e != nil {
					logger.Error("fail to update lcm manager action label to failed", "err", e)
				}
			} else {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.DeleteHostLabelRoleConvert, input).Get(ctx, nil); e != nil {
					logger.Error("fail to delete lcm manager action label", "err", e)
				}
			}

			if e := workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertJobStateWithFailed, input).Get(ctx, nil); e != nil {
				logger.Error("fail to update job state with failed", "err", e)
			}

			if e := workflow.ExecuteActivity(ctx, h.SyncClusterAfterRoleConvert, input).Get(ctx, nil); e != nil {
				logger.Warn("fail to sync cluster after role convert", "err", e)
			}

			if towerTaskID != "" {
				if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertTowerTaskWithFailed, input, towerTaskID).Get(ctx, nil); err != nil {
					logger.Error("fail to update role convert tower task")
				}
			}
		}
	}()

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertJobStateWithRunning, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.CreateRoleConvertTowerTask, input).Get(ctx, &towerTaskID); err != nil {
		logger.Error("fail to create role convert tower task")
		return err
	}

	if !input.SkipPrecheck {
		var cwJobID string
		if err = workflow.ExecuteActivity(ctx, h.ChildRoleConvertCheckWorkflowPreparation, input).Get(ctx, &cwJobID); err != nil {
			logger.Error("ChildRoleConvertCheckWorkflowPreparation failed", "error", err)
			return err
		}

		err = ChildRoleConvertCheckWorkflow(ctx, input, cwJobID)
		if err != nil {
			logger.Error("ChildRoleConvertCheckWorkflow run failed")
			return err
		}

		if err = workflow.ExecuteActivity(ctx, h.ValidateRoleConvert2ndCheckResult, input, cwJobID).Get(ctx, nil); err != nil {
			logger.Error("Validate RoleConvert2ndCheckResult failed", "error", err)
			return err
		}

		logger.Info("Second time role convert check finished.")
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateHostLabelRoleConvertRunning, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update host label role convert running")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertProgress, input, 0.0).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}

	if e := workflow.ExecuteActivity(ctx, h.SyncClusterAfterRoleConvert, input).Get(ctx, nil); e != nil {
		logger.Warn("fail to sync cluster after role converting started", "err", e)
	}

	if err = workflow.ExecuteActivity(ctx, h.VerifyHostPluginInstalledAndReady, input).Get(ctx, &hpID); err != nil {
		logger.Error("fail to verify host plugin installed and ready")
		return err
	}

	if hpID == "" {
		if err = workflow.ExecuteActivity(ctx, h.RoleConvertInstallHostPlugin, input).Get(ctx, &hpID); err != nil {
			logger.Error("fail to install host plugin")
			return err
		}
	}

	var agentInputs []*agentpb.TaskInput
	if err = workflow.ExecuteActivity(ctx, h.GenerateLcmManagerAgentInputs, input).Get(ctx, &agentInputs); err != nil {
		logger.Error("fail to generate lcm manager agent inputs")
		return err
	}

	if agentInputs == nil {
		err = errors.New("fail to generate lcm manager agent inputs")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertProgress, input, 0.05).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}
	agentTaskNumber := len(agentInputs)
	var agentTaskIndex int

	roleConvertInput := agentInputs[0]
	realActionStarted = true
	rollBackHostRoleInfo = true

	logger.Info("roleConvertInput", "roleConvertInput", roleConvertInput)

	var agentTaskID string
	if err = workflow.ExecuteActivity(ctx, h.ConvertTargetHostRole, input, roleConvertInput).Get(ctx, &agentTaskID); err != nil {
		logger.Error("fail to convert target host role", "err", err)
		return err
	}

	agentTaskIDs = append(agentTaskIDs, agentTaskID)

	if err = workflow.ExecuteActivity(ctxActionWait, h.RoleConvertWaitingForActionDone, input, roleConvertInput, agentTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to check convert target host role result", "err", err)
		return err
	}

	agentTaskIndex++
	progress := (float64(agentTaskIndex)/float64(agentTaskNumber))*0.9 + 0.05

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertProgress, input, progress).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}

	serviceRestartInputs := agentInputs[1:]
	for _, serviceRestartInput := range serviceRestartInputs {
		var agentTaskID string
		if err = workflow.ExecuteActivity(ctx, h.RestartHostsServices, input, serviceRestartInput).Get(ctx, &agentTaskID); err != nil {
			logger.Error("fail to restart host services", "err", err)
			return err
		}

		agentTaskIDs = append(agentTaskIDs, agentTaskID)

		if err = workflow.ExecuteActivity(ctxActionWait, h.RoleConvertWaitingForActionDone, input, serviceRestartInput, agentTaskID).Get(ctx, nil); err != nil {
			logger.Error("fail to check restart host services result", "err", err)
			return err
		}

		agentTaskIndex++
		progress := (float64(agentTaskIndex)/float64(agentTaskNumber))*0.9 + 0.05

		if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertProgress, input, progress).Get(ctx, nil); err != nil {
			logger.Error("fail to update job progress")
			return err
		}
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateHostLabelHostRole, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update host role label", "err", err)
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.DeleteHostLabelRoleConvert, input).Get(ctx, nil); err != nil {
		logger.Error("fail to delete lcm manager action label", "err", err)
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertProgress, input, 1.0).Get(ctx, nil); err != nil {
		logger.Error("fail to update job progress")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertJobStateWithSuccess, input).Get(ctx, nil); err != nil {
		logger.Error("failed update job state")
		return err
	}

	if e := workflow.ExecuteActivity(ctx, h.SyncClusterAfterRoleConvert, input).Get(ctx, nil); e != nil {
		logger.Warn("fail to sync cluster after role convert", "err", e)
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.UpdateRoleConvertTowerTaskWithSuccess, input, towerTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to update role convert tower task")
		return err
	}

	return nil
}
