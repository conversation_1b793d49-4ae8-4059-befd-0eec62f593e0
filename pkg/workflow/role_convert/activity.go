package roleconvert

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"sort"
	"strings"

	"github.com/eduardolat/goeasyi18n"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	towerclient "github.smartx.com/LCM/lcm-manager/pkg/client/tower"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/i18n"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
	"github.smartx.com/LCM/lcm-manager/third_party/tuna"
)

type RoleConvertCheck struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	i18n                  *goeasyi18n.I18n
}

func NewRoleConvertCheck(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	i18n *goeasyi18n.I18n,
) *RoleConvertCheck {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, nil, i18n)

	return &RoleConvertCheck{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		i18n:                  i18n,
	}
}

func (h *RoleConvertCheck) GetActivities(input RoleConvertCheckWorkflowInput) ([]any, error) {
	// Can't use towerClient in RoleConvertCheck here, because GetActivities is called by temporal workflow,
	// and GetActivities is not called as an activity. towerClient init in activity instead of workflow.
	// So is necessary to init towerClient here to get cluster type.
	towerClient, err := towerclient.NewTowerClient()
	if err != nil {
		slog.Error("failed to init tower client", "error", err)
		return nil, err
	}

	cluster, err := towerClient.GetClusterInfoByLocalID(context.Background(), input.ClusterUUID)
	if err != nil {
		slog.Error("failed to get cluster info", "error", err)
		return nil, err
	}

	activities := []any{h.RoleConvertCostTimeEvaluate}

	if input.TargetRole == serverpb.HostRole_ROLE_MASTER.String() {
		activities = append(activities, h.RoleConvertToMasterCheckTargetHostStatus)
		activities = append(activities, h.RoleConvertToMasterCheckOtherHostStatus)
	} else {
		activities = append(activities, h.RoleConvertToStorageCheckHostsStatus)
	}

	activities = append(activities, h.RoleConvertCheckClusterMasterCount)
	activities = append(activities, h.RoleConvertCheckZkMongoServiceStatus)
	if cluster.Type == tower.ClusterTypeSmtxElf {
		slog.Info("skip check pextent recover for SMTXELF")
	} else {
		activities = append(activities, h.RoleConvertCheckClusterExistPextentRecover)
	}

	activities = append(activities, h.RoleConvertCheckClusterExtending)
	activities = append(activities, h.RoleConvertCheckEnteringMaintenanceHost)
	activities = append(activities, h.RoleConvertCheckExistMaintenanceHost)
	activities = append(activities, h.RoleConvertCheckExistRemovingHost)
	activities = append(activities, h.RoleConvertCheckExistConvertingRoleHost)
	activities = append(activities, h.RoleConvertCheckClusterUpgrading)

	if cluster.Stretch {
		activities = append(activities, h.RoleConvertCheckWitnessStatus)
	}

	return activities, nil
}

func (h *RoleConvertCheck) RoleConvertToStorageCheckHostsStatus(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertToStorageCheckHostsStatus started.", "JobID", input.JobID)

	checkName := "RoleConvertToStorageCheckHostsStatus"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	var onlineUnhealthyHosts []string

	for _, host := range cluster.Hosts {
		if host.Merged_status != tower.HostMergedStatusDisconnected && host.Merged_status != tower.HostMergedStatusHealthy {
			logger.Info("Get online unhealthy host", "host", host.Name, "merged_status", host.Merged_status)
			onlineUnhealthyHosts = append(onlineUnhealthyHosts, host.Name)
		}
	}

	var zhOptions, enOptions *goeasyi18n.Options

	unHealthyHostsCount := len(onlineUnhealthyHosts)
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if unHealthyHostsCount > 0 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(onlineUnhealthyHosts, "zh"),
			},
		}
		enOptions = &goeasyi18n.Options{
			Count: &unHealthyHostsCount,
			Data: map[string]any{
				"HostNames": i18n.FormatList(onlineUnhealthyHosts, "en"),
			},
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertToMasterCheckTargetHostStatus(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertToMasterCheckTargetHostStatus started.", "JobID", input.JobID)

	checkName := "CheckTargetHostStatusToMaster"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get host info", "error", err)
		return err
	}

	logger.Info("get target host merged_status,", "merged_status", targetHost.Merged_status)

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetHostState(input.HostUUID)
	if err != nil {
		logger.Error("failed to get host state", "error", err)
		return err
	}

	hostState := res.HostState.State
	logger.Info("get target host state,", "host_state", hostState)

	checkState := serverpb.CheckState_CHECK_STATE_FAILED
	var provideCheckFailedMessage bool

	if targetHost.Merged_status == tower.HostMergedStatusHealthy && hostState == tuna.HostStateInuse {
		checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
	} else if hostState != tuna.HostStateInuse {
		provideCheckFailedMessage = true
	}

	if checkState == serverpb.CheckState_CHECK_STATE_FAILED && !provideCheckFailedMessage {
		if err := h.baseHandler.UpdateCheckResultWithoutMessage(cid, input.JobID, checkName, checkState, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertToMasterCheckOtherHostStatus(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertToMasterCheckOtherHostStatus started.", "JobID", input.JobID)

	checkName := "RoleConvertToMasterCheckOtherHostStatus"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	var unHealthyHosts []string

	for _, host := range cluster.Hosts {
		if host.Local_id == input.HostUUID {
			continue
		}

		if host.Merged_status != tower.HostMergedStatusHealthy {
			logger.Info("Get unhealthy host", "host", host.Name, "merged_status", host.Merged_status)
			unHealthyHosts = append(unHealthyHosts, host.Name)
		}
	}

	var zhOptions, enOptions *goeasyi18n.Options

	unHealthyHostsCount := len(unHealthyHosts)
	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	if unHealthyHostsCount > 0 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(unHealthyHosts, "zh"),
			},
		}
		enOptions = &goeasyi18n.Options{
			Count: &unHealthyHostsCount,
			Data: map[string]any{
				"HostNames": i18n.FormatList(unHealthyHosts, "en"),
			},
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckClusterMasterCount(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckClusterMasterCount started")

	checkName := "RoleConvertCheckClusterMasterCount"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	targetHostConnected := false
	healthyMasterCount := 0

	ClusterInfo, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	for _, host := range ClusterInfo.Hosts {
		if host.Role != string(config.HostRoleMaster) && host.Local_id != input.HostUUID {
			continue
		}

		if host.Status == tower.HostStatusConnectedHealthy {
			if host.Local_id == input.HostUUID {
				targetHostConnected = true
				if strings.HasSuffix(strings.ToLower(input.TargetRole), string(config.HostRoleMaster)) {
					healthyMasterCount++
				}
			} else {
				healthyMasterCount++
			}
		}
	}

	if ClusterInfo.Stretch {
		healthyMasterCount++
	}

	var checkState serverpb.CheckState

	if !targetHostConnected {
		if healthyMasterCount == 2 {
			checkState = serverpb.CheckState_CHECK_STATE_WARNING
		} else if healthyMasterCount <= 1 || healthyMasterCount > 5 {
			checkState = serverpb.CheckState_CHECK_STATE_FAILED
		} else if healthyMasterCount >= 3 && healthyMasterCount <= 5 {
			checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
		}
	} else {
		if healthyMasterCount < 3 || healthyMasterCount > 5 {
			checkState = serverpb.CheckState_CHECK_STATE_FAILED
		} else if healthyMasterCount >= 3 && healthyMasterCount <= 5 {
			checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
		}
	}

	logger.Info("check result", "connect status", targetHostConnected, "count", healthyMasterCount, "result", checkState.String())

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"Count": healthyMasterCount,
		},
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckZkMongoServiceStatus(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckZkMongoServiceStatus started.")

	checkName := "CheckZkMongoServiceStatus"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	zkServiceName := "zookeeper.service"
	mongoServiceName := "mongod.service"

	for _, host := range cluster.Hosts {
		hostRole, err := workflowutils.GetHostRole(input.ClusterIP, host.Local_id, host.Role)
		if err != nil {
			logger.Error("fail to get host role", "host", host.Name, "error", err)
			return err
		}

		if hostRole != string(config.HostRoleMaster) {
			logger.Info("Skip check zookeeper and mongo service status of storage node.", "host_name", host.Name)
			continue
		}

		if strings.HasSuffix(strings.ToLower(input.TargetRole), string(config.HostRoleStorage)) && host.Merged_status == tower.HostMergedStatusDisconnected {
			logger.Info("Skip check zookeeper and mongo service status of disconnected node while converting to storage node.", "host_name", host.Name)
			continue
		}

		tunaClient := tuna.NewClient(host.Management_ip, config.TunaAPIToken)

		err = h.baseHandler.CheckHostServiceStatus(ctx, tunaClient, []string{zkServiceName, mongoServiceName})
		if err != nil {
			logger.Error("check host service status failed.", "host_name", host.Name, "error", err)
			checkState = serverpb.CheckState_CHECK_STATE_FAILED

			break
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckClusterExistPextentRecover(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckClusterExistPextentRecover started.")

	checkName := "CheckClusterExistPextentRecover"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	zbsClient, err := workflowutils.InitZbsClient(cluster)
	if err != nil {
		logger.Error("failed to create zbs client", "error", err)
		return err
	}

	exist, err := zbsClient.IsExistPextendNeedRecover()
	if err != nil {
		logger.Error("failed to check pextent need recover", "error", err)
		return err
	}

	logger.Info("check pextent need recover", "exist", exist)

	var checkState serverpb.CheckState
	if exist {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	} else {
		checkState = serverpb.CheckState_CHECK_STATE_SUCCESS
	}

	if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckClusterExtending(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckClusterExtending started.")

	checkName := "CheckClusterExtending"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetClusterExtendindItem()
	if err != nil {
		logger.Error("failed to get cluster extending records", "error", err)
		return err
	}

	var extendingHostNames []string

	if res != nil {
		logger.Error("Cluster in extending status", "extend_uuid", res.UUID)

		for _, host := range res.Hosts {
			extendingHostNames = append(extendingHostNames, host.HostName)
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var zhOptions, enOptions *goeasyi18n.Options

	if len(extendingHostNames) > 0 {
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
		zhOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(extendingHostNames, "zh"),
			},
		}
		enOptions = &goeasyi18n.Options{
			Data: map[string]any{
				"HostNames": i18n.FormatList(extendingHostNames, "en"),
			},
		}
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, zhOptions, enOptions, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckClusterUpgrading(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckClusterUpgrading started.")

	checkName := "CheckClusterUpgrading"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	res, err := tunaClient.GetClusterUpgraderProgress()
	if err != nil {
		logger.Error("failed to get cluster upgrade records", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	if res != nil {
		logger.Error("Cluster in upgrading status")
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckEnteringMaintenanceHost(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckEnteringMaintenanceHost started.")

	checkName := "CheckEnteringMaintenanceHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	clusters, err := h.towerClient.QueryClustersByLocalIds(ctx, []string{input.ClusterUUID})
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	} else if len(clusters) == 0 {
		return fmt.Errorf("failed to get cluster %s", input.ClusterUUID)
	}

	var enteringMaintenanceHost string

	for _, host := range clusters[0].Hosts {
		if host.Host_state.State == tower.MaintenanceModeEnumEnteringMaintenanceMode {
			enteringMaintenanceHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get entering maintenance mode host.", "host", enteringMaintenanceHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if enteringMaintenanceHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": enteringMaintenanceHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckExistMaintenanceHost(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckExistMaintenanceHost started.")

	checkName := "CheckExistMaintenanceHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	clusters, err := h.towerClient.QueryClustersByLocalIds(ctx, []string{input.ClusterUUID})
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	} else if len(clusters) == 0 {
		return fmt.Errorf("failed to get cluster %s", input.ClusterUUID)
	}

	var maintenanceHost string

	for _, host := range clusters[0].Hosts {
		if host.Host_state.State == tower.MaintenanceModeEnumMaintenanceMode && host.Local_id != input.HostUUID {
			maintenanceHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get maintenance mode host.", "host", maintenanceHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if maintenanceHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": maintenanceHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckExistRemovingHost(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckExistRemovingHost started.")

	checkName := "RoleConvertCheckExistRemovingHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	var removingHost string

	for _, host := range cluster.Hosts {
		labelVal, err := workflowutils.GetHostLabelValue(tunaClient, host.Local_id, config.LcmManagerAction)
		if err != nil {
			logger.Error("failed to get host labels", "error", err)
			return err
		}

		if labelVal == config.HostRemoveRunning || labelVal == config.HostRemoveFailed {
			removingHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get removing/remove_failed host", "hostname", removingHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if removingHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": removingHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckExistConvertingRoleHost(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckExistConvertingRoleHost started.")

	checkName := "RoleConvertCheckExistConvertingRoleHost"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	tunaClient := tuna.NewClient(cluster.Ip, config.TunaAPIToken)

	var convertingHost string

	for _, host := range cluster.Hosts {
		labelVal, err := workflowutils.GetHostLabelValue(tunaClient, host.Local_id, config.LcmManagerAction)
		if err != nil {
			logger.Error("failed to get host labels", "error", err)
			return err
		}

		if labelVal == config.RoleConvertRunning || (host.Local_id != input.HostUUID && labelVal == config.RoleConvertFailed) {
			convertingHost = workflowutils.PickHostName(host.Name, host.Scvm_name, input.IsVmware)
			logger.Error("Get role converting / role convert failed host", "hostname", convertingHost)

			break
		}
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	var options *goeasyi18n.Options
	if convertingHost != "" {
		options = &goeasyi18n.Options{
			Data: map[string]any{
				"HostName": convertingHost,
			},
		}
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCheckWitnessStatus(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCheckWitnessStatus started.")

	checkName := "CheckWitnessNodeStatus"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	cluster, err := h.towerClient.GetClusterWitnessStatus(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS

	if cluster.Stretch && cluster.Metro_availability_checklist.Witness.Status != tower.MetroCheckStatusEnumHealthy {
		logger.Error("Cluster witness node is not healthy", "witness_data_ip", cluster.Witness.Data_ip, "status", cluster.Metro_availability_checklist.Witness.Status)
		checkState = serverpb.CheckState_CHECK_STATE_FAILED
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, nil, nil, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) RoleConvertCostTimeEvaluate(ctx context.Context, cid string, input RoleConvertCheckWorkflowInput, init bool) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertCostTimeEvaluate started")

	checkName := "CostTimeEvaluate"

	if init {
		if err := h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, serverpb.CheckState_CHECK_STATE_PENDING, nil, nil, input.IsVmware); err != nil {
			return err
		}

		return nil
	}

	ClusterInfo, err := h.towerClient.GetClusterInfoByLocalID(ctx, input.ClusterUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	onlineHostCount := 0

	for _, host := range ClusterInfo.Hosts {
		if host.Status == tower.HostStatusConnectedHealthy {
			onlineHostCount++
		}
	}

	if ClusterInfo.Stretch {
		onlineHostCount++
	}

	estimatedTimeCost := onlineHostCount * config.PerNodeRoleConvertTimeMinute
	logger.Info("estimated time cost", "online_host_count", onlineHostCount, "time", estimatedTimeCost)

	checkState := serverpb.CheckState_CHECK_STATE_SUCCESS
	options := &goeasyi18n.Options{
		Data: map[string]any{
			"CostTime": estimatedTimeCost,
		},
	}

	if err = h.baseHandler.UpdateCheckResult(cid, input.JobID, checkName, checkState, options, options, input.IsVmware); err != nil {
		return err
	}

	return nil
}

func (h *RoleConvertCheck) CreateRoleConvertCheckTowerTask(ctx context.Context, input RoleConvertCheckWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("CreateRoleConvertCheckTowerTask started.", input)

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
		},
	}

	taskArgs := map[string]string{
		"job_id":      input.JobID,
		"host_uuid":   input.HostUUID,
		"target_role": input.TargetRole,
		"state":       "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, "RoleConvertCheck", options, options, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *RoleConvertCheck) UpdateRoleConvertCheckTowerTaskWithSuccess(ctx context.Context, input RoleConvertCheckWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRoleConvertCheckTowerTaskWithSuccess started.")

	taskInput := &tower.UpdateTaskInput{
		Progress: 1.0,
		Status:   tower.TaskStatusSuccessed,
		Args: map[string]string{
			"job_id":      input.JobID,
			"host_uuid":   input.HostUUID,
			"target_role": input.TargetRole,
			"state":       "finished",
		},
		Done: true,
	}

	if err := h.baseHandler.UpdateTowerTask(ctx, taskID, taskInput); err != nil {
		logger.Error("UpdateTowerTask failed.", err)
		return err
	}

	return nil
}

func (h *RoleConvertCheck) UpdateRoleConvertCheckTowerTaskWithFailed(ctx context.Context, input RoleConvertCheckWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRoleConvertCheckTowerTaskWithFailed started.")

	taskInput := &tower.UpdateTaskInput{
		Status: tower.TaskStatusFailed,
		Args: map[string]string{
			"job_id":      input.JobID,
			"host_uuid":   input.HostUUID,
			"target_role": input.TargetRole,
			"state":       "failed",
		},
		Done: true,
	}

	if err := h.baseHandler.UpdateTowerTask(ctx, taskID, taskInput); err != nil {
		logger.Error("UpdateTowerTask failed.", err)
		return err
	}

	return nil
}

func (h *RoleConvertCheck) UpdateRoleConvertCheckJobStateWithRunning(ctx context.Context, input RoleConvertCheckWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_RUNNING, nil)
}

func (h *RoleConvertCheck) UpdateRoleConvertCheckJobStateWithSuccess(ctx context.Context, input RoleConvertCheckWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_SUCCESS, nil)
}

func (h *RoleConvertCheck) UpdateRoleConvertCheckJobStateWithFailed(ctx context.Context, input RoleConvertCheckWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, nil)
}

type RoleConvert struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	hpClient              hpoperator.Client
	i18n                  *goeasyi18n.I18n
}

func NewRoleConvert(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	hpClient hpoperator.Client,
	i18n *goeasyi18n.I18n,
) *RoleConvert {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, hpClient, i18n)

	return &RoleConvert{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		hpClient:              hpClient,
		i18n:                  i18n,
	}
}

func (h *RoleConvert) ChildRoleConvertCheckWorkflowPreparation(ctx context.Context, input RoleConvertWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("ChildRoleConvertCheckWorkflowPreparation started.")

	var jobName string
	if input.TargetRole == serverpb.HostRole_ROLE_STORAGE.String() {
		jobName = config.ActionConvertToStorageCheck
	}

	if input.TargetRole == serverpb.HostRole_ROLE_MASTER.String() {
		jobName = config.ActionConvertToMasterCheck
	}

	cwJobID, err := workflowutils.CreateJob(ctx, h.jobRepository, input.ClusterUUID, input.HostUUID, jobName)
	if err != nil {
		logger.Error("fail to create child role convert check job", "error", err)
		return "", err
	}

	return cwJobID, nil
}

func (h *RoleConvert) ValidateRoleConvert2ndCheckResult(ctx context.Context, input RoleConvertWorkflowInput, cwJobID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("ValidateRoleConvert2ndCheckResult started.", "job_id", input.JobID, "precheck_job_id", cwJobID)

	checkResults, err := h.checkResultRepository.GetByJobID(ctx, cwJobID)
	if err != nil {
		logger.Error("Fail to get role convert 2nd precheck result", "error", err)
		return err
	}

	result := true

	for _, checkResult := range checkResults {
		if checkResult.State == serverpb.CheckState_CHECK_STATE_FAILED {
			result = false
			break
		}
	}

	if !result {
		msgKey := "RoleConvert2ndCheckFailed"
		options := goeasyi18n.Options{}
		langZh, langEn := workflowutils.GetZhAndEnI18nLanguageName(input.IsVmware)
		messages := []*serverpb.MessageItem{
			{
				Lang:    serverpb.MessageLang_LANG_EN,
				Message: h.i18n.T(langEn, msgKey, options),
			},
			{
				Lang:    serverpb.MessageLang_LANG_ZH,
				Message: h.i18n.T(langZh, msgKey, options),
			},
		}

		err := h.jobRepository.UpdateJobStateWithEC(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, messages, serverpb.ErrorCode_SECOND_PRE_CHECK_FAILED)
		if err != nil {
			return err
		}

		return temporal.NewNonRetryableApplicationError("Role convert 2nd check failed", "PreCheckFailed", nil, nil)
	}

	return nil
}

func (h *RoleConvert) generateRoleConvertTowerTaskOptions(ctx context.Context, input RoleConvertWorkflowInput) (*goeasyi18n.Options, *goeasyi18n.Options, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("generateRoleConvertTowerTaskOptions started.")

	var msgKey string

	if input.TargetRole == serverpb.HostRole_ROLE_MASTER.String() {
		msgKey = "RoleNameMasterNode"
	} else if input.TargetRole == serverpb.HostRole_ROLE_STORAGE.String() {
		cluster, err := h.towerClient.GetClusterInfoByLocalID(context.Background(), input.ClusterUUID)
		if err != nil {
			logger.Error("fail to get cluster info", "error", err)
			return nil, nil, err
		}

		if cluster.Type == tower.ClusterTypeSmtxElf {
			msgKey = "RoleNameComputeNode"
		} else {
			msgKey = "RoleNameStorageNode"
		}
	}

	langZh, langEn := workflowutils.GetZhAndEnI18nLanguageName(input.IsVmware)
	roleNameZh := h.i18n.T(langZh, msgKey, goeasyi18n.Options{})
	roleNameEn := h.i18n.T(langEn, msgKey, goeasyi18n.Options{})

	zhOptions := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
			"RoleName": roleNameZh,
		},
	}

	enOptions := &goeasyi18n.Options{
		Data: map[string]any{
			"HostName": input.HostName,
			"RoleName": roleNameEn,
		},
	}

	return zhOptions, enOptions, nil
}

func (h *RoleConvert) CreateRoleConvertTowerTask(ctx context.Context, input RoleConvertWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("CreateRoleConvertTowerTask started.")

	zhOptions, enOptions, err := h.generateRoleConvertTowerTaskOptions(ctx, input)
	if err != nil {
		logger.Error("fail to generate role convert tower task options")
		return "", err
	}

	taskArgs := map[string]string{
		"job_id":      input.JobID,
		"host_uuid":   input.HostUUID,
		"target_role": input.TargetRole,
		"state":       "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, "RoleConvert", zhOptions, enOptions, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *RoleConvert) UpdateRoleConvertTowerTaskWithSuccess(ctx context.Context, input RoleConvertWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRoleConvertTowerTaskWithSuccess started.")

	zhOptions, enOptions, err := h.generateRoleConvertTowerTaskOptions(ctx, input)
	if err != nil {
		logger.Error("fail to generate role convert tower task options")
		return err
	}

	taskInput := &tower.UpdateTaskInput{
		Progress: 1.0,
		Status:   tower.TaskStatusSuccessed,
		Args: map[string]string{
			"job_id":      input.JobID,
			"host_uuid":   input.HostUUID,
			"target_role": input.TargetRole,
			"state":       "finished",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, "RoleConvert", zhOptions, enOptions, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *RoleConvert) UpdateRoleConvertTowerTaskWithFailed(ctx context.Context, input RoleConvertWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRoleConvertTowerTaskWithFailed started.")

	zhOptions, enOptions, err := h.generateRoleConvertTowerTaskOptions(ctx, input)
	if err != nil {
		logger.Error("fail to generate role convert tower task options")
		return err
	}

	taskInput := &tower.UpdateTaskInput{
		Status: tower.TaskStatusFailed,
		Args: map[string]string{
			"job_id":      input.JobID,
			"host_uuid":   input.HostUUID,
			"target_role": input.TargetRole,
			"state":       "failed",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, "RoleConvert", zhOptions, enOptions, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *RoleConvert) UpdateRoleConvertProgress(ctx context.Context, input RoleConvertWorkflowInput, jobProgress float64) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateRoleConvertProgress started.", "jobProgress", jobProgress)

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}

	progressStr := fmt.Sprintf("%.2f", jobProgress)
	jobProgressInput := &serverpb.JobProgress{
		Progress: progressStr,
	}

	if err := h.jobRepository.UpdateJobProgress(ctx, input.JobID, jobProgressInput); err != nil {
		logger.Error("fail to update job progress", "error", err)
		return err
	}

	taskArgs := map[string]string{
		"job_id":      input.JobID,
		"host_uuid":   input.HostUUID,
		"target_role": input.TargetRole,
		"state":       "running",
	}

	err = h.baseHandler.UpdateTowerTaskProgress(ctx, input.JobID, towerTaskID, input.HostUUID, taskArgs, jobProgress)
	if err != nil {
		logger.Error("failed to update tower task progress", "task_id", towerTaskID)
		return err
	}

	return nil
}

func (h *RoleConvert) UpdateRoleConvertJobStateWithRunning(ctx context.Context, input RoleConvertWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_RUNNING, nil)
}

func (h *RoleConvert) UpdateRoleConvertJobStateWithSuccess(ctx context.Context, input RoleConvertWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_SUCCESS, nil)
}

func (h *RoleConvert) UpdateRoleConvertJobStateWithFailed(ctx context.Context, input RoleConvertWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, nil)
}

func (h *RoleConvert) DeleteHostLabelRoleConvert(ctx context.Context, input RoleConvertWorkflowInput) error {
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerAction, "")
}

func (h *RoleConvert) UpdateHostLabelRoleConvertRunning(ctx context.Context, input RoleConvertWorkflowInput) error {
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerAction, config.RoleConvertRunning)
}

func (h *RoleConvert) UpdateHostLabelRoleConvertFailed(ctx context.Context, input RoleConvertWorkflowInput) error {
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerAction, config.RoleConvertFailed)
}

func (h *RoleConvert) UpdateHostLabelHostRole(ctx context.Context, input RoleConvertWorkflowInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("UpdateHostLabelHostRole started.")

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("failed to get cluster info", "error", err)
		return err
	}

	if targetHost.Status != tower.HostStatusConnectedHealthy && input.TargetRole == serverpb.HostRole_ROLE_STORAGE.String() {
		logger.Info("Target host is offline, update host role label to storage", "host", input.HostUUID)
		return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerHostRole, config.HostLabelRoleStorage)
	}

	logger.Info("Target role is not storage or target host is online, delete host role label", "host", input.HostUUID)
	return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerHostRole, "")
}

func (h *RoleConvert) VerifyHostPluginInstalledAndReady(ctx context.Context, input RoleConvertWorkflowInput) (string, error) {
	return h.baseHandler.VerifyHostPluginInstalledAndReady(ctx, input.ClusterUUID, input.JobID)
}

func (h *RoleConvert) RoleConvertCleanupHostPlugin(_ context.Context, hostPluginID string) error {
	return h.baseHandler.CleanupHostPlugin(hostPluginID)
}

func (h *RoleConvert) RoleConvertInstallHostPlugin(ctx context.Context, input RoleConvertWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertInstallHostPlugin started.")

	host, err := h.baseHandler.PickHostToInstallHostPlugin(ctx, input.ClusterUUID, input.HostUUID)
	if err != nil {
		return "", err
	}

	pkgID, err := h.baseHandler.UploadHostPluginPackage(ctx, input.ClusterUUID)
	if err != nil {
		return "", err
	}

	hpInput := h.baseHandler.PrepareInstallHostPluginInput(input.TowerClusterID, pkgID, host.Id)

	return h.baseHandler.InstallHostPlugin(ctx, host.Management_ip, input.JobID, hpInput)
}

func (h *RoleConvert) GenerateLcmManagerAgentInputs(ctx context.Context, input RoleConvertWorkflowInput) ([]*agentpb.TaskInput, error) { //nolint: funlen,gocyclo
	logger := activity.GetLogger(ctx)
	logger.Info("GenerateLcmManagerAgentInputs started.")

	var targetActionName string

	if input.TargetRole == serverpb.HostRole_ROLE_STORAGE.String() {
		targetActionName = config.ActionConvertToStorage
	} else {
		targetActionName = config.ActionConvertToMaster
	}

	targetActionData, err := h.baseHandler.LoadTargetActionData(ctx, input.ClusterUUID, targetActionName)
	if err != nil {
		logger.Error("fail to load target action data", "error", err)
		return nil, err
	}

	cluster, err := h.towerClient.GetClusterInfoByLocalID(context.Background(), input.ClusterUUID)
	if err != nil {
		slog.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	job, err := h.jobRepository.Get(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get job with id: ", "job_id", input.JobID)
		return nil, err
	}

	agentAddr, exist := job.Details["agent_addr"]
	if !exist {
		return nil, errors.New("no agent ip found in job")
	}

	var agentHost tower.QueryClusterInfoByLocalIdClusterHostsHost
	var targetHost tower.QueryClusterInfoByLocalIdClusterHostsHost
	var targetHostOffline bool
	offlineHostsIP := make([]string, 0)
	onlineHostsIP := make([]string, 0)

	for _, host := range cluster.Hosts {
		if host.Management_ip == agentAddr {
			agentHost = host
		}

		if host.Local_id == input.HostUUID {
			targetHost = host
		}

		if host.Status == tower.HostStatusConnectedHealthy {
			onlineHostsIP = append(onlineHostsIP, host.Data_ip)
		} else {
			offlineHostsIP = append(offlineHostsIP, host.Data_ip)

			if host.Local_id == input.HostUUID {
				targetHostOffline = true
			}
		}
	}

	sort.Strings(onlineHostsIP)

	var supportMultiOfflineHosts bool
	for _, feature := range targetActionData.Features {
		if feature == config.SupportMultiOfflineHosts {
			supportMultiOfflineHosts = true
		}
	}

	if !supportMultiOfflineHosts && len(offlineHostsIP) > 0 {
		logger.Info("not support multi offline hosts", "offline_host", offlineHostsIP)
		return nil, fmt.Errorf("not support action with offline host %s", offlineHostsIP)
	}

	agentInputs := make([]*agentpb.TaskInput, 0)

	// generate role convert inputs
	for _, agentCmd := range targetActionData.AgentCmds {
		if agentCmd.Name != config.CmdNameConvertToStorage && agentCmd.Name != config.CmdNameConvertToMaster {
			logger.Info(fmt.Sprintf("skip command: %s", agentCmd.Name))
			continue
		}

		cmds := []string{agentCmd.Command}
		if agentCmd.Name == config.CmdNameConvertToStorage && supportMultiOfflineHosts {
			if len(offlineHostsIP) > 0 {
				cmds = append(cmds, "--offline_host_ips "+strings.Join(offlineHostsIP, ","))
			}

			if targetHostOffline {
				cmds = append(cmds, "--is_alive false")
			}

			// convert_to_storage requires online master >= 2, so len(onlineHostsIp) == 2 means online maser == 2 here.
			if targetHostOffline && len(onlineHostsIP) == 2 {
				cmds = append(cmds, "--ignore_recover_status")
			}
		}

		cmds = append(cmds, targetHost.Data_ip)

		var cmdQA []*agentpb.QA
		for _, qa := range agentCmd.CmdQA {
			cmdQA = append(cmdQA, &agentpb.QA{
				Query:      qa.Query,
				Answer:     qa.Answer,
				PassedMark: qa.PassedMark,
			})
		}

		agentInput := agentpb.TaskInput{
			Command:  strings.Join(cmds, " "),
			TargetIp: agentHost.Data_ip,
			CmdQa:    cmdQA,
			Timeout:  int32(agentCmd.TimeoutFactor * len(onlineHostsIP)),
		}

		logger.Info("generate role convert inputs", "input", agentInput.String())
		agentInputs = append(agentInputs, &agentInput)
	}

	if len(agentInputs) < 1 {
		return nil, errors.New("fail to generate role convert commands")
	}

	// generate restart service input
	for _, agentCmd := range targetActionData.AgentCmds {
		if agentCmd.Name != config.CmdNameRestartService {
			continue
		}

		for _, hostIP := range onlineHostsIP {
			cmds := []string{agentCmd.Command}

			// convert_to_storage requires online master >= 2, so len(onlineHostsIp) == 2 means online maser == 2 here.
			if targetHostOffline && agentCmd.Name == config.CmdNameConvertToStorage && supportMultiOfflineHosts && len(onlineHostsIP) == 2 {
				cmds = append(cmds, "--ignore_recover_status")
			}

			agentInput := agentpb.TaskInput{
				Command:  strings.Join(cmds, " "),
				TargetIp: hostIP,
				Timeout:  int32(agentCmd.TimeoutFactor),
			}
			agentInputs = append(agentInputs, &agentInput)
			logger.Info("generate restart service input", "input", agentInput.String())
		}
	}

	if len(agentInputs) < len(onlineHostsIP)+1 {
		logger.Info("fail to generate role convert commands", "expected_items", len(onlineHostsIP)+1, "actual_items", len(agentInputs))
		return nil, fmt.Errorf("fail to generate role convert commands, %d expected but %d provided", len(onlineHostsIP)+1, len(agentInputs))
	}

	return agentInputs, nil
}

func (h *RoleConvert) ConvertTargetHostRole(ctx context.Context, input RoleConvertWorkflowInput, agentInput *agentpb.TaskInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("ConvertTargetHostRole started.")

	return h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
}

func (h *RoleConvert) RestartHostsServices(ctx context.Context, input RoleConvertWorkflowInput, agentInput *agentpb.TaskInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RestartHostsServices started.")

	return h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
}

func (h *RoleConvert) RoleConvertWaitingForActionDone(ctx context.Context, input RoleConvertWorkflowInput, agentInput *agentpb.TaskInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RoleConvertWaitingForActionDone started.")

	return h.baseHandler.WaitingForActionDone(ctx, input.JobID, agentInput, taskID)
}

func (h *RoleConvert) SyncClusterAfterRoleConvert(ctx context.Context, input RoleConvertWorkflowInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("SyncClusterAfterRoleConvert started.")

	err := h.towerClient.UpdateClusterByAdminAPI(ctx, input.TowerClusterID)
	if err != nil {
		logger.Error("fail to sync cluster info after role convert", "error", err)
		return temporal.NewNonRetryableApplicationError("fail to sync cluster info after role convert", "PostRoleConvert", nil, nil)
	}

	logger.Info("SyncClusterAfterRoleConvert finished.")

	return nil
}

func (h *RoleConvert) StoreRoleConvertJobLogs(ctx context.Context, input RoleConvertWorkflowInput, agentTaskIDs []string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("StoreRoleConvertJobLogs started.")

	return h.baseHandler.StoreJobLogs(ctx, input.JobID, agentTaskIDs)
}

func (h *RoleConvert) rollBackHostRoleInfoByAgent(ctx context.Context, input RoleConvertWorkflowInput, targetHostDataIP string) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RollBackHostRoleInfoByAgent started.")

	var cmdStr string

	if input.TargetRole == serverpb.HostRole_ROLE_STORAGE.String() {
		cmdStr = "sudo sed -i 's/^role = storage/role = master/' /etc/zbs/zbs.conf && sudo sed -i 's/storage/master/' /etc/zbs/role && sudo zbs-node collect_node_info && echo 'rollback host role successfully.'"
	} else {
		cmdStr = "sudo sed -i 's/^role = master/role = storage/' /etc/zbs/zbs.conf && sudo sed -i 's/master/storage/' /etc/zbs/role && sudo zbs-node collect_node_info && echo 'rollback host role successfully.'"
	}

	agentInput := &agentpb.TaskInput{
		Command:  cmdStr,
		TargetIp: targetHostDataIP,
		Timeout:  60,
	}

	taskID, err := h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
	if err != nil {
		logger.Error("Fail to create lcm manager agent task for RollBackHostRoleInfo", "error", err)
		return "", err
	}

	if err := h.baseHandler.WaitingForActionDone(ctx, input.JobID, agentInput, taskID); err != nil {
		return "", err
	}

	return taskID, nil
}

func (h *RoleConvert) rollBackHostRoleInfoByLabel(ctx context.Context, input RoleConvertWorkflowInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("RollBackHostRoleInfoByLabel started.")

	if input.TargetRole == serverpb.HostRole_ROLE_STORAGE.String() {
		logger.Info("update host role label to roll back host role", "host", input.HostUUID, "origin_role", config.HostLabelRoleMaster)
		return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerHostRole, config.HostLabelRoleMaster)
	} else {
		logger.Info("update host role label to roll back host role", "host", input.HostUUID, "origin_role", config.HostLabelRoleStorage)
		return h.baseHandler.UpdateHostLabel(ctx, input.ClusterUUID, input.HostUUID, config.LcmManagerHostRole, config.HostLabelRoleStorage)
	}
}

func (h *RoleConvert) RollBackHostRoleInfo(ctx context.Context, input RoleConvertWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("RollBackHostRoleInfo started.")

	targetHost, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		logger.Error("fail to get target host info", "error", err)
		return "", err
	}

	taskID, err := h.rollBackHostRoleInfoByAgent(ctx, input, targetHost.Data_ip)
	if err == nil {
		return taskID, nil
	}

	logger.Warn("fail to roll back host role by lcm-manager agent with host data ip", "host", input.HostName, "data_ip", targetHost.Data_ip, "error", err)

	taskID, err = h.rollBackHostRoleInfoByAgent(ctx, input, targetHost.Management_ip)
	if err == nil {
		return taskID, nil
	}

	logger.Warn("fail to roll back host role by lcm-manager agent with host management ip", "host", input.HostName, "data_ip", targetHost.Management_ip, "error", err)

	return "", h.rollBackHostRoleInfoByLabel(ctx, input)
}
