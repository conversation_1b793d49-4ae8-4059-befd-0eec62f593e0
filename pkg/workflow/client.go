package workflow

import (
	"context"
	"errors"
	"log/slog"

	"go.temporal.io/sdk/client"

	"github.smartx.com/LCM/lcm-manager/pkg/config"
	rdmatoggle "github.smartx.com/LCM/lcm-manager/pkg/workflow/rdma_toggle"
	removehost "github.smartx.com/LCM/lcm-manager/pkg/workflow/remove_host"
	roleconvert "github.smartx.com/LCM/lcm-manager/pkg/workflow/role_convert"
	timesync "github.smartx.com/LCM/lcm-manager/pkg/workflow/time_sync"
)

type Client struct {
	client.Client
}

func NewClient(hostPort, namespace string) (*Client, error) {
	c, err := client.Dial(client.Options{
		HostPort:  hostPort,
		Namespace: namespace,
		Logger:    slog.Default(),
	})
	if err != nil {
		slog.Error("unable to create temporal client", "error", err)
		return nil, err
	}

	return &Client{Client: c}, nil
}

func (c *Client) Close() {
	c.Client.Close()
}

func (c *Client) ExecuteRoleConvertCheckWorkflow(ctx context.Context, input roleconvert.RoleConvertCheckWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "role-convert-check-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, roleconvert.RoleConvertCheckWorkflow, input)

	return err
}

func (c *Client) ExecuteRoleConvertWorkflow(ctx context.Context, input roleconvert.RoleConvertWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "role-convert-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, roleconvert.RoleConvertWorkflow, input)

	return err
}

func (c *Client) ExecuteRemoveHostCheckWorkflow(ctx context.Context, input removehost.RemoveHostCheckWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "remove-host-check-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, removehost.RemoveHostCheckWorkflow, input)

	return err
}

func (c *Client) ExecuteRemoveHostWorkflow(ctx context.Context, input removehost.RemoveHostWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "remove-host-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, removehost.RemoveHostWorkflow, input)

	return err
}

func (c *Client) ExecuteTimeSyncWorkflow(ctx context.Context, input timesync.TimeSyncWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "time-sync-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, timesync.TimeSyncWorkflow, input)

	return err
}

func (c *Client) ExecuteRdmaToggleCheckWorkflow(ctx context.Context, input rdmatoggle.RdmaToggleWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "rdma-toggle-check-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, rdmatoggle.RdmaToggleWorkflow, input)

	return err
}

func (c *Client) ExecuteRdmaToggleWorkflow(ctx context.Context, input rdmatoggle.RdmaToggleWorkflowInput) error {
	wfOptions := client.StartWorkflowOptions{
		ID:        "rdma-toggle-" + input.ClusterName + "-" + input.JobID,
		TaskQueue: config.TemporalWorkerName,
	}

	if input.IsVmware {
		return errors.New("RDMA toggle is not supported on VMware platform")
	}

	_, err := c.Client.ExecuteWorkflow(ctx, wfOptions, rdmatoggle.RdmaToggleWorkflow, input)

	return err
}
