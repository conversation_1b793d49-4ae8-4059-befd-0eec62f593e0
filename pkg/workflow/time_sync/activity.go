package timesync

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/eduardolat/goeasyi18n"
	"go.temporal.io/sdk/activity"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	serverpb "github.smartx.com/LCM/lcm-manager/gen/proto/server/v1"
	agentclient "github.smartx.com/LCM/lcm-manager/pkg/client/agent"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/pkg/server/postgres"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
	hpoperator "github.smartx.com/LCM/lcm-manager/third_party/host_plugin_operator"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
)

type TimeSyncActivities struct {
	baseHandler           *workflowutils.ActivityBaseHandler
	jobRepository         postgres.IJobRepository
	checkResultRepository postgres.ICheckResultRepository
	towerClient           tower.Client
	i18n                  *goeasyi18n.I18n
}

func NewTimeSyncActivities(
	jobRepository postgres.IJobRepository,
	checkResultRepository postgres.ICheckResultRepository,
	towerClient tower.Client,
	hpClient hpoperator.Client,
	i18n *goeasyi18n.I18n,
) *TimeSyncActivities {
	baseHandler := workflowutils.NewActivityBaseHandler(jobRepository, checkResultRepository, towerClient, hpClient, i18n)

	return &TimeSyncActivities{
		baseHandler:           baseHandler,
		jobRepository:         jobRepository,
		checkResultRepository: checkResultRepository,
		towerClient:           towerClient,
		i18n:                  i18n,
	}
}

func (h *TimeSyncActivities) TimeSyncStoreJobLogs(ctx context.Context, input TimeSyncWorkflowInput, agentTaskIDs []string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSyncStoreJobLogs started.", input)

	return h.baseHandler.StoreJobLogs(ctx, input.JobID, agentTaskIDs)
}

func (h *TimeSyncActivities) TimeSyncCleanupHostPlugin(_ context.Context, hostPluginID string) error {
	return h.baseHandler.CleanupHostPlugin(hostPluginID)
}

func (h *TimeSyncActivities) TimeSyncVerifyHostPluginInstalledAndReady(ctx context.Context, input TimeSyncWorkflowInput) (string, error) {
	return h.baseHandler.VerifyHostPluginInstalledAndReady(ctx, input.ClusterUUID, input.JobID)
}

func (h *TimeSyncActivities) TimeSyncInstallHostPlugin(ctx context.Context, input TimeSyncWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSyncInstallHostPlugin started.")

	host, err := h.towerClient.GetHostInfoByLocalID(ctx, input.HostUUID)
	if err != nil {
		return "", err
	}

	pkgID, err := h.baseHandler.UploadHostPluginPackage(ctx, input.ClusterUUID)
	if err != nil {
		return "", err
	}

	hpInput := h.baseHandler.PrepareInstallHostPluginInput(input.TowerClusterID, pkgID, host.Id)

	return h.baseHandler.InstallHostPlugin(ctx, host.Management_ip, input.JobID, hpInput)
}

func (h *TimeSyncActivities) TimeSyncGenerateLcmManagerAgentInputs(ctx context.Context, input TimeSyncWorkflowInput) (*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSyncGenerateLcmManagerAgentInputs started.")

	targetActionData, err := h.baseHandler.LoadTargetActionData(ctx, input.ClusterUUID, config.ActionTimeSync)
	if err != nil {
		logger.Error("fail to load target action data", "error", err)
		return nil, err
	}

	infoForCmdInputs, err := h.prepareInfoTimeSyncCmdInputs(ctx, input)
	if err != nil {
		return nil, err
	}

	logger.Info("info for cmd inputs", "info", infoForCmdInputs)

	timeSyncNodeAgentInput, err := h.generateTimeSyncNodeCmd(ctx, targetActionData, *infoForCmdInputs)
	if err != nil {
		return nil, err
	}

	return timeSyncNodeAgentInput, nil
}

func (h *TimeSyncActivities) generateTimeSyncNodeCmd(ctx context.Context, targetActionData *config.VersionedAction, infoForCmdInputs InfoForCmdInputs) (*agentpb.TaskInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("generateTimeSyncNodeCmd started.")

	var agentCmd *config.AgentCmd
	var err error
	if infoForCmdInputs.Time == "" {
		agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameTimeSyncWithNtpServer)
		if err != nil {
			return nil, fmt.Errorf("fail to get time sync command: %w", err)
		}
	} else {
		agentCmd, err = h.baseHandler.GetAgentCmdByName(targetActionData.AgentCmds, config.CmdNameTimeSyncInternal)
		if err != nil {
			return nil, fmt.Errorf("fail to get time sync command: %w", err)
		}

		agentCmd.Command = strings.ReplaceAll(agentCmd.Command, "{{time}}", infoForCmdInputs.Time)
	}

	cmds := []string{agentCmd.Command}

	agentInput := agentpb.TaskInput{
		Command:  strings.Join(cmds, " "),
		TargetIp: infoForCmdInputs.AgentHostDataIP,
		CmdQa:    nil,
		Timeout:  int32(agentCmd.TimeoutFactor),
	}

	logger.Info("generate time_sync agent cmd inputs", "input", agentInput.String())

	return &agentInput, nil
}

func (h *TimeSyncActivities) TimeSyncTriggerAgentTask(ctx context.Context, input TimeSyncWorkflowInput, agentInput *agentpb.TaskInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSync TriggerAgentTask started.")

	return h.baseHandler.CreateLcmAgentTask(ctx, input.JobID, agentInput)
}

// TimeSyncWaitingForAgentTask wait for agent task done and update progress if not timeout
func (h *TimeSyncActivities) TimeSyncWaitingForAgentTask(ctx context.Context, input TimeSyncWorkflowInput, agentInput *agentpb.TaskInput, taskID string, progressEvaluateInput *TimeSyncProgressEvaluateInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSyncWaitingForAgentTask started.")

	towerTaskID, err := h.baseHandler.GetTowerTaskIDFromDB(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get tower task id in db", "job_id", input.JobID)
		return err
	}

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return err
	}

	timeout := time.After(time.Duration(agentInput.Timeout) * time.Second)

	agentClient := agentclient.NewTaskManagerClient(agentAddr)
	taskInput := &agentpb.GetTaskRequest{TaskId: taskID}

	for {
		select {
		case <-timeout:
			slog.Error("Timeout reached waiting for task completion")
			return fmt.Errorf("timeout waiting for task %s completion", taskID)

		default:
			time.Sleep(30 * time.Second)

			done, err := h.baseHandler.CheckAgentTaskStatus(ctx, agentClient, taskInput)
			if err != nil {
				return err
			}

			if done {
				return nil
			}

			if err := h.metaTimeSyncUpdateProgress(ctx, input.JobID, input.HostUUID, towerTaskID, progressEvaluateInput); err != nil {
				logger.Error("fail to update time sync progress, skip update it this time.", "error", err)
			}
		}
	}
}

type InfoForCmdInputs struct {
	AgentHostDataIP string
	AgentHostMgmtIP string
	Time            string
}

func (h *TimeSyncActivities) TimeSyncCostTimePreliminaryEvaluate(ctx context.Context, input TimeSyncWorkflowInput) (*TimeSyncProgressEvaluateInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSync CostTimePreliminaryEvaluate started.")

	startTime, err := h.getTimeSyncJobStartTime(ctx, input)
	if err != nil {
		logger.Error("fail to get job start time", "job_id", input.JobID)
		return nil, err
	}

	// set a static time now, not scale based on number of nodes
	estimatedSyncTime := config.TimeSyncBaseTimeMinute * 60

	timeSyncProgressEvaluateInput := &TimeSyncProgressEvaluateInput{
		StartTime:             startTime,
		EstimatedTimeSyncTime: estimatedSyncTime,
	}

	logger.Info("TimeSync CostTimePreliminaryEvaluate finished", "timeSyncProgressEvaluateInput", timeSyncProgressEvaluateInput)

	return timeSyncProgressEvaluateInput, nil
}

func (h *TimeSyncActivities) prepareInfoTimeSyncCmdInputs(ctx context.Context, input TimeSyncWorkflowInput) (*InfoForCmdInputs, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("prepareInfoTimeSyncCmdInputs started.")

	cluster, err := h.towerClient.GetClusterInfoByLocalID(context.Background(), input.ClusterUUID)
	if err != nil {
		logger.Error("fail to get cluster info", "error", err)
		return nil, err
	}

	agentAddr, err := h.baseHandler.GetJobLcmAgentAddrFromDB(ctx, input.JobID)
	if err != nil {
		return nil, err
	}

	infoForCmdInputs := &InfoForCmdInputs{AgentHostMgmtIP: agentAddr, Time: input.Time}

	for _, host := range cluster.Hosts {
		if host.Management_ip == agentAddr {
			infoForCmdInputs.AgentHostDataIP = host.Data_ip
		}
	}

	if infoForCmdInputs.AgentHostDataIP == "" {
		logger.Error("fail to get lcm manager agent host data ip")
		return nil, errors.New("fail to get lcm manager agent host data ip")
	}

	return infoForCmdInputs, nil
}

func (h *TimeSyncActivities) metaTimeSyncUpdateProgress(ctx context.Context, jobID string, hostUUID string, towerTaskID string, progressEvaluateInput *TimeSyncProgressEvaluateInput) error {
	logger := activity.GetLogger(ctx)
	logger.Info("meta TimeSync UpdateProgress started.")

	currentTime := time.Now().UTC()
	allDuration := currentTime.Sub(progressEvaluateInput.StartTime)
	allUsedSec := int(allDuration.Seconds())

	progress := float64(allUsedSec) / float64(progressEvaluateInput.EstimatedTimeSyncTime)
	if progress >= 1.0 {
		progress = 0.99
	}

	jobProgress := &serverpb.JobProgress{
		Progress: fmt.Sprintf("%.2f", progress),
	}

	if err := h.jobRepository.UpdateJobProgress(ctx, jobID, jobProgress); err != nil {
		logger.Error("fail to update job progress", "error", err)
		return err
	}

	if err := h.baseHandler.UpdateTowerTaskProgress(ctx, jobID, towerTaskID, hostUUID, nil, progress); err != nil {
		logger.Error("failed to update tower task progress", "task_id", towerTaskID)
		return err
	}

	logger.Info("meta TimeSync UpdateProgress finished.", "job", jobID, "progress", progress)
	return nil
}

func (h *TimeSyncActivities) getTimeSyncJobStartTime(ctx context.Context, input TimeSyncWorkflowInput) (time.Time, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("getTimeSyncJobStartTime started.")

	job, err := h.jobRepository.Get(ctx, input.JobID)
	if err != nil {
		logger.Error("failed to get job in db", "job_id", input.JobID)
		return time.Time{}, err
	}

	logger.Info("get job start time", "start_time", job.CreateTime)
	return job.CreateTime.AsTime(), nil
}

// --- job control ---
func (h *TimeSyncActivities) TimeSyncUpdateJobStateWithRunning(ctx context.Context, input TimeSyncWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_RUNNING, nil)
}

func (h *TimeSyncActivities) TimeSyncUpdateJobStateWithSuccess(ctx context.Context, input TimeSyncWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_SUCCESS, nil)
}

func (h *TimeSyncActivities) TimeSyncUpdateJobStateWithFailed(ctx context.Context, input TimeSyncWorkflowInput) error {
	return h.jobRepository.UpdateJobState(ctx, input.JobID, serverpb.JobState_JOB_STATE_FAILED, nil)
}

// --- tower job control ----
func (h *TimeSyncActivities) TimeSyncCreateTowerTask(ctx context.Context, input TimeSyncWorkflowInput) (string, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSyncCreateTowerTask started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
			"Time":        strings.ReplaceAll(input.Time, "+", " "), // avoid URL encode in web UI
		},
	}

	taskArgs := map[string]string{
		"job_id":    input.JobID,
		"host_uuid": input.HostUUID,
		"state":     "running",
	}

	taskInput := h.baseHandler.BuildCreateTowerTaskInput(input.TowerClusterID, input.TowerTaskCfgName, options, options, taskArgs, input.IsVmware)

	taskID, err := h.baseHandler.CreateTowerTask(ctx, taskInput, input.Header)
	if err != nil {
		return "", err
	}

	if err := h.jobRepository.UpdateJobDetail(ctx, input.JobID, map[string]string{"tower_task_id": taskID}); err != nil {
		logger.Error("fail to update job details with tower_task_id")
		return "", err
	}

	return taskID, nil
}

func (h *TimeSyncActivities) TimeSyncUpdateTowerTaskWithSuccess(ctx context.Context, input TimeSyncWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSync UpdateTowerTaskWithSuccess started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
			"Time":        strings.ReplaceAll(input.Time, "+", " "), // avoid URL encode in web UI
		},
	}

	taskInput := &tower.UpdateTaskInput{
		Progress: 1.0,
		Status:   tower.TaskStatusSuccessed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "finished",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, input.TowerTaskCfgName, options, options, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("UpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}

func (h *TimeSyncActivities) TimeSyncUpdateTowerTaskWithFailed(ctx context.Context, input TimeSyncWorkflowInput, taskID string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("TimeSyncUpdateTowerTaskWithFailed started.")

	options := &goeasyi18n.Options{
		Data: map[string]any{
			"ClusterName": input.ClusterName,
			"Time":        strings.ReplaceAll(input.Time, "+", " "), // avoid URL encode in web UI
		},
	}

	taskInput := &tower.UpdateTaskInput{
		Status: tower.TaskStatusFailed,
		Args: map[string]string{
			"job_id":    input.JobID,
			"host_uuid": input.HostUUID,
			"state":     "failed",
		},
		Done: true,
	}

	auditInput := h.baseHandler.BuildTowerAuditEventInput(input.JobID, input.TowerClusterID, input.TowerTaskCfgName, options, options, input.IsVmware)

	if err := h.baseHandler.UpdateTowerTaskWithAuditLog(ctx, taskID, taskInput, &input.Header, auditInput); err != nil {
		logger.Error("TimeSyncUpdateTowerTaskWithAuditLog failed.", err)
		return err
	}

	return nil
}
