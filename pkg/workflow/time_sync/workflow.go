package timesync

import (
	"errors"
	"time"

	"go.temporal.io/sdk/workflow"

	agentpb "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	workflowutils "github.smartx.com/LCM/lcm-manager/pkg/workflow/utils"
)

type TimeSyncWorkflowInput struct {
	Header           workflowutils.CustomedHeader `json:"header"`
	JobID            string                       `json:"job_id"`
	TowerClusterID   string                       `json:"tower_cluster_id"`
	ClusterUUID      string                       `json:"cluster_uuid"`
	ClusterName      string                       `json:"cluster_name"`
	Time             string                       `json:"time"` // only used for correct time for cluster without external ntp server
	ClusterIP        string                       `json:"cluster_vip"`
	HostUUID         string                       `json:"host_uuid"`
	HostName         string                       `json:"host_name"`
	IsVmware         bool                         `json:"is_vmware"` // used for i18n
	TowerTaskCfgName string                       `json:"tower_task_cfg_name"`
}

type TimeSyncProgressEvaluateInput struct {
	StartTime             time.Time `json:"start_time"`
	EstimatedTimeSyncTime int       `json:"estimated_time_sync_time"` // seconds
}

func TimeSyncWorkflow(ctx workflow.Context, input TimeSyncWorkflowInput) error { //nolint: funlen
	var err error

	ctx, err = workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameDefault)
	if err != nil {
		return err
	}

	ctxStateUpdate, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameStateUpdate)
	if err != nil {
		return err
	}

	ctxActionWait, err := workflowutils.GetContextWithActivityOptions(ctx, config.ActivityOptionsNameWaitingForTimeSync)
	if err != nil {
		return err
	}

	logger := workflow.GetLogger(ctx)
	logger.Info("TimeSyncWorkflow workflow started", "JobID", input.JobID)

	var (
		h                             *TimeSyncActivities
		realActionStarted             bool
		towerTaskID                   string
		hostPluginID                  string
		timeSyncProgressEvaluateInput *TimeSyncProgressEvaluateInput
	)

	// lcm-manager-agent task ids, used for collecting logs from lcm-manager-agent
	agentTaskIDs := make([]string, 0)

	defer func() {
		if errors.Is(ctx.Err(), workflow.ErrCanceled) {
			// When the Workflow is canceled, it has to get a new disconnected context to execute any Activities
			ctx, _ = workflow.NewDisconnectedContext(ctx)
		}

		// 5. store job logs
		if realActionStarted {
			if e := workflow.ExecuteActivity(ctx, h.TimeSyncStoreJobLogs, input, agentTaskIDs).Get(ctx, nil); e != nil {
				logger.Error("fail to store lcm manager agent logs", "err", e)
			}
		}

		// 6. host plugin: clean
		if hostPluginID != "" {
			if e := workflow.ExecuteActivity(ctx, h.TimeSyncCleanupHostPlugin, hostPluginID).Get(ctx, nil); e != nil {
				logger.Error("fail to cleanup host plugin", "err", e)
			}
		}

		// 7. update job state: failed
		if err != nil {
			if e := workflow.ExecuteActivity(ctxStateUpdate, h.TimeSyncUpdateJobStateWithFailed, input).Get(ctx, nil); e != nil {
				logger.Error("fail to update time sync job state with failed", "err", e)
			}

			if towerTaskID != "" {
				if e := workflow.ExecuteActivity(ctxStateUpdate, h.TimeSyncUpdateTowerTaskWithFailed, input, towerTaskID).Get(ctx, nil); e != nil {
					logger.Error("fail to update time sync tower task")
				}
			}
		}
	}()

	// 1. update job state: running
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.TimeSyncUpdateJobStateWithRunning, input).Get(ctx, nil); err != nil {
		logger.Error("fail to update job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.TimeSyncCreateTowerTask, input).Get(ctx, &towerTaskID); err != nil {
		logger.Error("fail to create time sync tower task")
		return err
	}

	if err = workflow.ExecuteActivity(ctx, h.TimeSyncCostTimePreliminaryEvaluate, input).Get(ctx, &timeSyncProgressEvaluateInput); err != nil {
		logger.Error("TimeSyncCostTimePreliminaryEvaluate run failed.", "error", err)
		return err
	}

	// 2. host plugin: install
	if err = workflow.ExecuteActivity(ctx, h.TimeSyncVerifyHostPluginInstalledAndReady, input).Get(ctx, &hostPluginID); err != nil {
		logger.Error("fail to verify host plugin installed and ready")
		return err
	}

	if hostPluginID == "" {
		if err = workflow.ExecuteActivity(ctx, h.TimeSyncInstallHostPlugin, input).Get(ctx, &hostPluginID); err != nil {
			logger.Error("fail to install host plugin")
			return err
		}
	}

	// 3. run main activity
	var timeSyncAgentTaskInput *agentpb.TaskInput
	if err = workflow.ExecuteActivity(ctx, h.TimeSyncGenerateLcmManagerAgentInputs, input).Get(ctx, &timeSyncAgentTaskInput); err != nil {
		logger.Error("fail to generate lcm manager agent inputs")
		return err
	}

	if timeSyncAgentTaskInput == nil {
		err = errors.New("fail to generate lcm manager agent inputs")
		return err
	}

	var agentTaskID string
	if err = workflow.ExecuteActivity(ctx, h.TimeSyncTriggerAgentTask, input, timeSyncAgentTaskInput).Get(ctx, &agentTaskID); err != nil {
		logger.Error("fail to trigger time sync cmd", "err", err)
		return err
	}
	agentTaskIDs = append(agentTaskIDs, agentTaskID)
	realActionStarted = true

	if err = workflow.ExecuteActivity(ctxActionWait, h.TimeSyncWaitingForAgentTask, input, timeSyncAgentTaskInput, agentTaskID, timeSyncProgressEvaluateInput).Get(ctx, nil); err != nil {
		logger.Error("fail to wait for time sync cmd", "err", err)
		return err
	}

	// 4. update job state: success
	if err = workflow.ExecuteActivity(ctxStateUpdate, h.TimeSyncUpdateJobStateWithSuccess, input).Get(ctx, nil); err != nil {
		logger.Error("failed update time sync job state")
		return err
	}

	if err = workflow.ExecuteActivity(ctxStateUpdate, h.TimeSyncUpdateTowerTaskWithSuccess, input, towerTaskID).Get(ctx, nil); err != nil {
		logger.Error("fail to update time sync tower task")
		return err
	}

	logger.Info("TimeSyncWorkflow completed.")

	return nil
}
