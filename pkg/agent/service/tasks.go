package service

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/gofrs/uuid"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	agentv1 "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/agent/tasks"
)

type Server struct {
	agentv1.UnimplementedTaskManagerServer
	tm *tasks.TaskManager
}

func NewServer() *Server {
	return &Server{
		tm: tasks.NewTaskManager(),
	}
}

func (s *Server) GetTask(_ context.Context, input *agentv1.GetTaskRequest) (*agentv1.GetTaskReply, error) {
	resp := &agentv1.GetTaskReply{}

	if input.TaskId == "" {
		resp.Ec = agentv1.ErrorCode_EC_INVALID_PARAMETER
		resp.Error = "task_id is empty"
		return resp, status.Errorf(codes.InvalidArgument, "task_id is empty")
	}

	task := s.tm.GetTask(input.TaskId)
	if task == nil {
		resp.Ec = agentv1.ErrorCode_EC_NOT_FOUND
		resp.Error = fmt.Sprintf("task %s not found", input.TaskId)
		return resp, status.Errorf(codes.NotFound, resp.Error)
	}

	resp.Ec = agentv1.ErrorCode_EC_EOK
	resp.Data = task

	return resp, nil
}

func (s *Server) ListTasks(_ context.Context, _ *emptypb.Empty) (*agentv1.ListTasksReply, error) {
	resp := &agentv1.ListTasksReply{
		Ec:   agentv1.ErrorCode_EC_EOK,
		Data: s.tm.GetTasks(),
	}
	return resp, nil
}

func (s *Server) CreateTask(_ context.Context, input *agentv1.TaskInput) (*agentv1.CreateTaskReply, error) {
	if input.TaskId == "" {
		newID, err := uuid.NewV7()
		if err != nil {
			slog.Error("fail to generate task uuid")
			return nil, status.Errorf(codes.Unknown, "fail to generate task uuid")
		}

		input.TaskId = newID.String()
	}

	resp := &agentv1.CreateTaskReply{}
	if err := s.tm.CreateTask(input); err != nil {
		resp.Ec = agentv1.ErrorCode_EC_CREATE_TASK_FAILED
		resp.Error = err.Error()

		return resp, status.Errorf(codes.Unknown, resp.Error)
	}

	resp.Ec = agentv1.ErrorCode_EC_EOK
	resp.Data = &agentv1.CreateTaskReplyData{
		TaskId: input.TaskId,
	}

	return resp, nil
}
