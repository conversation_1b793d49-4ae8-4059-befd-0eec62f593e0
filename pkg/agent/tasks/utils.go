package tasks

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"strings"
	"time"

	cryptossh "golang.org/x/crypto/ssh"

	agentv1 "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
)

func handlePipe(pipe io.Reader, output *string) {
	r := bufio.NewReader(pipe)

	for {
		line, err := r.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				return
			}

			slog.Error("Failed to read info from stdout,", "error", err)
			line = fmt.Sprintf("io read string error: %v", err)
		}

		*output += line
	}
}

func createQueryMap(qas []*agentv1.QA) map[string]string {
	queryMap := make(map[string]string)
	for _, qa := range qas {
		queryMap[qa.Query] = qa.Answer
	}

	return queryMap
}

func handleQA(ctx context.Context, stdin io.WriteCloser, cmdQA []*agentv1.QA, queryCh chan string, passedQACh chan string, allPassedCh chan struct{}) {
	defer slog.Info("Exit answering query.")

	queryMap := createQueryMap(cmdQA)
	if len(queryMap) == 0 {
		allPassedCh <- struct{}{}
		return
	}

	for {
		select {
		case <-ctx.Done():
			allPassedCh <- struct{}{}
			return
		case query := <-passedQACh:
			if _, ok := queryMap[query]; ok {
				slog.Info("Remove passed query,", "query", query)
				delete(queryMap, query)
			}

			if len(queryMap) == 0 {
				allPassedCh <- struct{}{}
				return
			}

		case query := <-queryCh:
			if answer, ok := queryMap[query]; ok {
				fmt.Fprintln(stdin, answer)
				slog.Info("Answer query and remove answered query,", "query", query, "answer", answer)
				delete(queryMap, query)
			}

			if len(queryMap) == 0 {
				allPassedCh <- struct{}{}
				return
			}
		}
	}
}

func checkQA(qaInfo []*agentv1.QA, line string, queryCh chan string, passedQACh chan string) {
	for _, qa := range qaInfo {
		if qa.PassedMark != "" && strings.Contains(line, qa.PassedMark) {
			passedQACh <- qa.Query
			slog.Info("Notify query passed,", "query", qa.Query)
		} else if strings.Contains(line, qa.Query) {
			slog.Info("Notify query received,", "query", qa.Query)
			queryCh <- qa.Query
		}
	}
}

func checkQAIfHang(ctx context.Context, r *bufio.Reader, qaInfo []*agentv1.QA, queryCh chan string, passedQACh chan string, heartbeatCh chan struct{}, needAnswer *bool) {
	defer slog.Info("Exit checking query.")

	timer := time.NewTimer(1 * time.Second)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			if *needAnswer && r.Buffered() > 0 {
				data, err := r.Peek(r.Buffered())
				if err != nil {
					slog.Error("Failed to peek buffered info from stdout,", "error", err)
					return
				}

				line := string(data)
				slog.Debug("Get buffered info,", "len", r.Buffered(), "Buffered:", line)

				checkQA(qaInfo, line, queryCh, passedQACh)
			}

			timer.Reset(1 * time.Second)
		case <-heartbeatCh:
			timer.Reset(1 * time.Second)
		}
	}
}

func handlePipeWithQA(stdout io.Reader, stdin io.WriteCloser, cmdQA []*agentv1.QA, output *string) {
	defer slog.Info("Exit handling pipe with query.")

	ctx, cancel := context.WithCancel(context.Background())

	qaInfo := append([]*agentv1.QA{}, cmdQA...)
	slog.Info("Command query & answer defined,", "cmd_qa", qaInfo)

	queryCh := make(chan string, len(cmdQA))
	passedQACh := make(chan string, len(cmdQA))
	allPassedCh := make(chan struct{}, 1)

	// handle query
	go func() {
		handleQA(ctx, stdin, cmdQA, queryCh, passedQACh, allPassedCh)
	}()

	needAnswer := new(bool)
	*needAnswer = true

	go func() {
		<-allPassedCh
		slog.Info("All queries passed.")

		*needAnswer = false
	}()

	r := bufio.NewReader(stdout)
	heartbeatCh := make(chan struct{}, 1)

	go checkQAIfHang(ctx, r, qaInfo, queryCh, passedQACh, heartbeatCh, needAnswer)

	for {
		line, err := r.ReadString('\n')
		fmt.Println(line)

		if err != nil {
			if err == io.EOF {
				cancel()
				return
			}

			slog.Error("Failed to read info from stdout,", "error", err)
			line = fmt.Sprintf("io read string error: %v", err)
		}

		*output += line

		if *needAnswer {
			checkQA(qaInfo, line, queryCh, passedQACh)
		}

		heartbeatCh <- struct{}{}
	}
}

func handleTaskExecutionError(task *agentv1.Task, errorMessage string) {
	task.Status.Status = agentv1.TaskStatusEnum_FAILED
	task.Status.Error = errorMessage
}

func createPipes(session *cryptossh.Session) (io.WriteCloser, io.Reader, io.Reader, error) {
	stdinPipe, err := session.StdinPipe()
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create stdin pipe: %s", err.Error())
	}

	stdoutPipe, err := session.StdoutPipe()
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create stdout pipe: %s", err.Error())
	}

	stderrPipe, err := session.StderrPipe()
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create stderr pipe: %s", err.Error())
	}

	return stdinPipe, stdoutPipe, stderrPipe, nil
}

func GetSSHUserInfo() (string, string) {
	// when admin not exists we use smartx user
	if _, err := os.Stat(config.AdminSSHKeyPath); os.IsNotExist(err) {
		return config.SmartxSSHUsername, config.SmartxSSHKeyPath
	}

	return config.AdminSSHUsername, config.AdminSSHKeyPath
}
