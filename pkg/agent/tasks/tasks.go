package tasks

import (
	"fmt"
	"io"
	"log/slog"
	"sort"
	"sync"
	"time"

	cryptossh "golang.org/x/crypto/ssh"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"

	agentv1 "github.smartx.com/LCM/lcm-manager/gen/proto/agent/v1"
	sshclient "github.smartx.com/LCM/lcm-manager/pkg/client/ssh"
)

type TaskManager struct {
	tasks map[string]*agentv1.Task
	mu    sync.Mutex
}

func NewTaskManager() *TaskManager {
	return &TaskManager{
		tasks: make(map[string]*agentv1.Task),
	}
}

func (tm *TaskManager) GetTasks() []*agentv1.Task {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tasks := make([]*agentv1.Task, 0, len(tm.tasks))
	for _, task := range tm.tasks {
		tasks = append(tasks, task)
	}

	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].Status.StartAt.AsTime().After(tasks[j].Status.StartAt.AsTime())
	})

	return tasks
}

func (tm *TaskManager) CreateTask(input *agentv1.TaskInput) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	taskID := input.TaskId
	if task, ok := tm.tasks[taskID]; ok {
		if task.Status.Status == agentv1.TaskStatusEnum_PENDING || task.Status.Status == agentv1.TaskStatusEnum_RUNNING {
			return fmt.Errorf("task %s is already exist", taskID)
		}
	}

	task := &agentv1.Task{
		TaskId: taskID,
		Input:  input,
		Output: &agentv1.TaskOutput{},
		Status: &agentv1.TaskStatus{Status: agentv1.TaskStatusEnum_PENDING},
	}
	tm.tasks[taskID] = task

	go tm.executeTask(task)

	return nil
}

func (tm *TaskManager) GetTask(taskID string) *agentv1.Task {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if task, ok := tm.tasks[taskID]; ok {
		return task
	}

	return nil
}

func (tm *TaskManager) executeTask(task *agentv1.Task) {
	username, sshKeyPath := GetSSHUserInfo()

	client, err := sshclient.NewClient(task.Input.TargetIp, username, sshKeyPath)
	if err != nil {
		slog.Error("SSH connection error,", "error_info", err)
		handleTaskExecutionError(task, fmt.Sprintf("SSH connection error: %s", err))

		return
	}

	defer client.Close()

	session, err := client.NewSession()
	if err != nil {
		slog.Error("SSH session error,", "error_info", err)
		handleTaskExecutionError(task, fmt.Sprintf("SSH session error: %s", err))

		return
	}

	defer session.Close()

	if err := session.RequestPty("xterm", 80, 40, cryptossh.TerminalModes{}); err != nil { //nolint:gomnd
		slog.Error("RequestPty error,", "error_info", err)
		handleTaskExecutionError(task, fmt.Sprintf("RequestPty error: %s", err))

		return
	}

	executeCommand(session, task)
}

func handlePipes(wg *sync.WaitGroup, stdinPipe io.WriteCloser, stdoutPipe io.Reader, stderrPipe io.Reader, task *agentv1.Task) {
	wg.Add(2) //nolint:gomnd

	go func() {
		defer wg.Done()
		handlePipe(stderrPipe, &task.Output.Stderr)
	}()

	if task.Input.CmdQa != nil {
		go func() {
			defer wg.Done()
			handlePipeWithQA(stdoutPipe, stdinPipe, task.Input.CmdQa, &task.Output.Stdout)
		}()
	} else {
		go func() {
			defer wg.Done()
			handlePipe(stdoutPipe, &task.Output.Stdout)
		}()
	}
}

func executeCommand(session *cryptossh.Session, task *agentv1.Task) {
	stdinPipe, stdoutPipe, stderrPipe, err := createPipes(session)
	if err != nil {
		slog.Error("Failed to create pipes,", "error_info", err)
		handleTaskExecutionError(task, fmt.Sprintf("Failed to create pipes: %s", err))

		return
	}

	if err = session.Start(task.Input.Command); err != nil {
		slog.Error("Failed to start command,", "error_info", err)
		handleTaskExecutionError(task, fmt.Sprintf("Failed to start command: %s", err))

		return
	}

	task.Status.Status = agentv1.TaskStatusEnum_RUNNING
	task.Status.StartAt = timestamppb.Now()

	defer func() {
		task.Status.EndAt = timestamppb.Now()
	}()

	var wg sync.WaitGroup

	handlePipes(&wg, stdinPipe, stdoutPipe, stderrPipe, task)

	done := make(chan error, 1)
	go func() {
		done <- session.Wait()
	}()

	select {
	case <-time.After(time.Duration(task.Input.Timeout) * time.Second):
		task.Status.Status = agentv1.TaskStatusEnum_FAILED
		if err := session.Signal(cryptossh.SIGTERM); err != nil {
			task.Status.Error = "Command execution timeout and session signal error: " + err.Error()
		} else {
			task.Status.Error = "Command execution timeout"
		}

		slog.Error(task.Status.Error)
	case err := <-done:
		if err != nil {
			handleTaskExecutionError(task, err.Error())
			slog.Error("Execute task failed,", "status", task.Status)
		} else {
			task.Status.Status = agentv1.TaskStatusEnum_COMPLETED
			slog.Info("Complete task,", "task", task.TaskId)
		}
	}

	wg.Wait()
}
