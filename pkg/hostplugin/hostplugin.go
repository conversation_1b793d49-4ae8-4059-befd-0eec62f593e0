package hostplugin

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path"

	"gopkg.in/yaml.v2"

	"github.smartx.com/LCM/lcm-manager/pkg/client/shell"
	"github.smartx.com/LCM/lcm-manager/pkg/config"
	"github.smartx.com/LCM/lcm-manager/third_party/tower"
)

type PkgsYAML struct {
	Pkgs []*PkgInfo `yaml:"pkgs"`
}

type PkgInfo struct {
	Name         string `yaml:"name"`
	Version      string `yaml:"version"`
	Architecture string `yaml:"architecture"`
	Filename     string `yaml:"filename"`
	FilePath     string `yaml:"file_path,omitempty"`
}

func NewPkgsYAML() (*PkgsYAML, error) {
	pkgsDir := config.HpoPkgsDIR
	pkgYAMLPath := path.Join(pkgsDir, "pkgs.yaml")

	data, err := os.ReadFile(pkgYAMLPath)
	if err != nil {
		slog.Error("failed to read host plugin pkgs.yaml file", "file", pkgYAMLPath, "error", err)
		return nil, fmt.Errorf("failed to read file %s: %w", pkgYAMLPath, err)
	}

	var pkgsYAML PkgsYAML

	if err := yaml.Unmarshal(data, &pkgsYAML); err != nil {
		slog.Error("failed to unmarshal yaml", "error", err)
		return nil, err
	}

	for i := range pkgsYAML.Pkgs {
		pkgsYAML.Pkgs[i].FilePath = path.Join(pkgsDir, pkgsYAML.Pkgs[i].Filename)
	}

	return &pkgsYAML, nil
}

func (p *PkgsYAML) GetPkgInfo(name, arch string) *PkgInfo {
	for _, pkg := range p.Pkgs {
		if pkg.Name == name && pkg.Architecture == arch {
			return pkg
		}
	}

	return nil
}

const (
	uploadTimeout = 60
)

func (p *PkgInfo) Upload() (string, error) {
	return uploadPkg(p.FilePath)
}

func (p *PkgInfo) IsPkgInstalled(hp tower.QuerySpecificHostPluginsByClustersHostPluginsHostPlugin) bool {
	if p.Version != hp.Host_plugin_package.Version {
		return false
	}

	instances := UnmarshalInstances(hp.Host_plugin_instances)
	if len(instances) == 0 {
		return false
	}

	for _, instance := range instances {
		if !instance.Healthy {
			return false
		}
	}

	return true
}

func uploadPkg(pkgPath string) (string, error) {
	fileStat, err := os.Stat(pkgPath)
	if err != nil {
		return "", fmt.Errorf("failed to stat file %s: %w", pkgPath, err)
	}

	shellClient := shell.Client{TrimNewline: true}
	cmd := fmt.Sprintf("curl --location -X POST '%s?size=%d' -F file=@%s", config.HostPluginUploadURL, fileStat.Size(), pkgPath)

	stdout, stderr, err := shellClient.RunCommand(cmd, uploadTimeout)
	if err != nil {
		return "", fmt.Errorf("failed to upload file %s: %w, stdout: %s, stderr: %s", pkgPath, err, stdout, stderr)
	}

	type response struct {
		ErrorCode    string `json:"error_code"`
		ErrorMessage string `json:"error_message"`
		PackageID    string `json:"package_id"`
	}

	var res response
	if err = json.Unmarshal([]byte(stdout), &res); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if res.ErrorCode != "" && res.ErrorCode != "HOST_PLUGIN_PACKAGE_DUPLICATE" {
		return "", fmt.Errorf("failed to upload file %s: %s", pkgPath, res.ErrorMessage)
	}

	return res.PackageID, nil
}

func GetPkgInfo(name, arch string) (*PkgInfo, error) {
	pkgsYAML, err := NewPkgsYAML()
	if err != nil {
		return nil, err
	}

	return pkgsYAML.GetPkgInfo(name, arch), nil
}

type Instance struct {
	HostID  string `json:"host_id"`
	Healthy bool   `json:"healthy"`
}

func UnmarshalInstances(data json.RawMessage) []*Instance {
	if len(data) == 0 {
		return nil
	}

	instances := make([]*Instance, 0)
	if err := json.Unmarshal(data, &instances); err != nil {
		return nil
	}

	return instances
}
