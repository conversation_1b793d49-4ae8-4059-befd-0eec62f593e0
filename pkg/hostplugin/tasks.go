package hostplugin

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.smartx.com/LCM/lcm-manager/third_party/tower"
)

const (
	retryInterval = 10
)

func WaitTowerTask(towerClient tower.Client, taskID string, timeout int) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	for {
		task, err := towerClient.GetTask(context.Background(), taskID)
		if err != nil {
			slog.Error(fmt.Sprintf("failed to get task %s: %v", taskID, err))
			return fmt.Errorf("failed to get task %s: %w", taskID, err)
		}

		taskInfo := fmt.Sprintf("tower task id: %s, description: %s, status: %s", task.Id, task.Description, task.Status)

		switch task.Status {
		case tower.TaskStatusFailed:
			slog.Error(fmt.Sprintf("%s: [%s]: %s", taskInfo, task.Error_code, task.Error_message))
			return fmt.Errorf("[%s]: %s", task.Error_code, task.Error_message)
		case tower.TaskStatusSuccessed:
			slog.Info(taskInfo)
			// sleep for a while to wait for database update
			time.Sleep(retryInterval * time.Second)

			return nil
		case tower.TaskStatusExecuting, tower.TaskStatusPending, tower.TaskStatusPaused:
			slog.Info(fmt.Sprintf("%s, retry after %d seconds", taskInfo, retryInterval))
		default:
			slog.Error(fmt.Sprintf("%s unknown task status: %s", taskInfo, task.Status))
			return fmt.Errorf("unknown task status: %s", task.Status)
		}

		select {
		case <-ctx.Done():
			slog.Error("timeout to wait for task" + taskID)
			return fmt.Errorf("timeout to wait for task %s", taskID)
		default:
			time.Sleep(retryInterval * time.Second)
		}
	}
}
