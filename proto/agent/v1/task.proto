syntax = "proto3";
package lcm.agent.protobuf;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "proto/server/v1/server.proto";

service TaskManager {
  // 创建 LCM Manager Agent Task
  rpc CreateTask(TaskInput) returns (CreateTaskReply) {
    option (google.api.http) = {
      post: "/api/v1/tasks"
      body: "*"
    };
  }

  // 获取 LCM Manager Agent 的 Task 信息
  rpc GetTask(GetTaskRequest) returns (GetTaskReply) {
    option (google.api.http) = {get: "/api/v1/tasks/{task_id}"};
  }

  // 获取 LCM Manager Agent 的 Task 列表
  rpc ListTasks(google.protobuf.Empty) returns (ListTasksReply) {
    option (google.api.http) = {get: "/api/v1/tasks"};
  }

  // 健康检查
  rpc Healthz(google.protobuf.Empty) returns (lcm.manager.protobuf.HealthzResponse) {
    option (google.api.http) = {get: "/api/v1/agent/healthz"};
  }
}

enum ErrorCode {
  EC_UNSPECIFIED = 0;
  EC_EOK = 1;
  EC_INVALID_PARAMETER = 2;
  EC_CREATE_TASK_FAILED = 3;
  EC_UNAUTHORIZED = 4;
  EC_NOT_FOUND = 5;
}

message QA {
  string query = 1;
  string answer = 2;
  string passed_mark = 3 [json_name = "passed_mark"];
}

message TaskInput {
  string task_id = 1 [json_name = "task_id"];
  string command = 2;
  string target_ip = 3 [json_name = "target_ip"];
  int32 timeout = 4;
  repeated QA cmd_qa = 5 [json_name = "cmd_qa"];
}

message TaskOutput {
  string stdout = 1;
  string stderr = 2;
}

enum TaskStatusEnum {
  UNSPECIFIED = 0;
  FAILED = 1;
  PENDING = 2;
  COMPLETED = 3;
  RUNNING = 4;
}

message TaskStatus {
  TaskStatusEnum status = 1;
  string error = 2;
  google.protobuf.Timestamp start_at = 3 [json_name = "start_at"];
  google.protobuf.Timestamp end_at = 4 [json_name = "end_at"];
}

message Task {
  string task_id = 1 [json_name = "task_id"];
  TaskInput input = 2;
  TaskOutput output = 3;
  TaskStatus status = 4;
}

message GetTaskRequest {
  string task_id = 1 [json_name = "task_id"];
}

message GetTaskReply {
  ErrorCode ec = 1;
  Task data = 2;
  string error = 3;
}

message ListTasksReply {
  ErrorCode ec = 1;
  repeated Task data = 2;
  string error = 3;
}

message CreateTaskReply {
  ErrorCode ec = 1;
  CreateTaskReplyData data = 2;
  string error = 3;
}

message CreateTaskReplyData {
  string task_id = 1 [json_name = "task_id"];
}
